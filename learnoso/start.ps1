param(
    [Parameter(Mandatory = $true)]
    [string]$RepoUrl
)

# Extract the repository name from the URL (assumes the repo name is the last part of the URL)
$repoName = ($RepoUrl -split '/' | Select-Object -Last 1).Replace('.git','')

Write-Host "Cloning repository from $RepoUrl..."
git clone $RepoUrl

if (-Not (Test-Path -Path $repoName)) {
    Write-Error "Repository folder '$repoName' not found. Exiting..."
    exit 1
}

Write-Host "Changing directory to $repoName..."
Set-Location $repoName

Write-Host "Running 'npm install --legacy-peer-deps'..."
npm install --legacy-peer-deps

Write-Host "Starting development server with 'npm start'..."
npm start
