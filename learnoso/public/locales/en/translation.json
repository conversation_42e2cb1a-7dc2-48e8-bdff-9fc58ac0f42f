{"common": {"email": "Email", "password": "Password", "app-name": "<PERSON><PERSON><PERSON>"}, "auth": {"login": {"welcome_learnoso": "Welcome back to Learnoso, please enter the required details to access your account. Don't have an account?", "signup": "Sign up", "email": "Email", "email_placeholder": "<EMAIL>", "forget_password": "Forget password?", "no_account": "Don't have an account?", "signin_with_google": "Sign in with Google"}, "forgotpassword": {"reset_password": "Reset your password", "description": "Enter your email address for verification and recovery of your account.", "email": "Email", "email_placeholder": "<EMAIL>", "send_recovery_code": "Send Recovery code", "no_account": "Don't have an account?", "signup": " Sign up"}, "register": {"welcome_to_learnoso": "Welcome to <PERSON><PERSON><PERSON>, please enter the required details to create an account. Already have an account?", "signin": "Sign in", "first_name": "First Name", "last_name": "Last Name", "email": "Email", "email_placeholder": "<EMAIL>", "country": "Country", "country_placeholder": "Select a country", "password": "Password", "confirm_password": "Confirm Password", "already_have_an_account": "Already have an account?"}, "resetpassword": {"reset_your_password": "Reset your password", "new_password": "Please enter your new passwords", "password": "Password", "confirm_password": "Confirm Password", "signup": "Sign up", "remember_password": "Remember your old password?", "signin": "Sign in"}}, "home": {"hero": {"cta-text": "Looking to <span1>Boost</span1> your <br />  <span2>Career & Academics?</span2>", "connect": "Connect with the Best IT, Tech, and Language Tutors", "cta-button-title": "Browse Tutors"}, "achievement": {"cta-text": "Achievements", "description": "Our achievements in numbers", "students": "Students", "tutors": "Tutors"}, "features": {"cta-text": "Our Top Features", "description": "Personalized tutoring from expert instructors", "tutor": "Expert Tutors", "description01": "Access to qualified and experienced tutors in various subjects", "personalized": "Personalized Learning", "description02": "Tailored learning paths and recommendations based on individual needs and goals", "interactive": "Interactive Learning", "description03": "Engaging features like live classes, interactive exercises, and quizzes; video conferencing, and integrated IDE", "flexible": "Flexible Scheduling", "description04": "Choose from a variety of scheduling options to fit your busy lifestyle", "affordable": "Affordable Pricing", "description05": "Transparent and competitive pricing plans", "process": "Progress Tracking", "description06": "Monitor your progress and receive personalized feedback"}, "testimonials": {"cta-text": " Testimonials", "description": "What people say about us"}, "footer": {"description": "Learnoso is an online platform that connects students with tutors in various fields, including technology, IT, business, and languages. It aims to provide an inclusive and reliable learning experience for learners worldwide.", "quick_link": "Quick Links", "tutor": "Become a tutor", "schedule_class": "Schedule class", "blog": "Blog", "help": "Help", "contacts": "Contacts", "official_email": "www.learnoso.com", "email": "<EMAIL>", "location": " Global", "newsletter": "Newsletter", "recieve_updates": "Sign up to receive email updates", "form_email": "Enter your email", "subscribe": "Subscribe", "copyright": "All rights reserved | Skye8"}, "header": {"login": "<PERSON><PERSON>", "signup": "Sign Up"}}, "howitworks": {"banner": {"title": "How It Works", "discover": "Discover a seamless learning experience, where tutors and students  collaborate effectively, enhancing your skills through  well-structured lessons and hands-on projects.", "description": "With <PERSON><PERSON><PERSON>, you can track your progress, receive personalized feedback, and engage in interactive learning sessions."}, "detailedlearningprocess": {"title": "Your Learning Journey", "startLearningButton": "Start Your Learning Journey"}, "howtheplatformworks": {"title": "How Our Platform Works", "signup": "Step 1: Sign Up", "create": "Create an account as a tutor or student to access personalized learning plans and start your educational journey.", "jion": " Step 2: Join a <PERSON>", "browse": "Browse our available courses or book live tutoring sessions tailored to your skill level and goals.", "track": "Step 3: Track Your Progress", "monitor": "Monitor your progress with detailed reports, quizzes, and feedback from tutors to ensure continuous improvement.", "get_started": "Get Started "}}, "onboard": {"title": "Choose Your Role", "select_platform": "Select how you want to use the platform", "jion_student": " Join as a student to access courses, connect with tutors, and track your learning progress", "tutor": "Tutor", "jion_tutor": "Join as a tutor to create courses, mentor students, and earn while sharing your expertise", "student": "Student"}, "errorcard": {"error": "Error!", "retry": "Retry"}, "successcard": {"success": "Success!", "continue": "Continue"}, "tutorcard": {"min_lesson": "{{value}} min lesson", "lessons": "lessons", "students": "Students", "active_students": "active Students", "read_more": "Read More", "trail_lesson": "Book trial lesson", "send_message": "Send Message"}, "notfound": {"page_not_found": "Page Not Found", "no_page": "We couldn't find the page you were looking for. It might have been moved or doesn't exist.", "back_to_home": "Back to Home", "go_back": "Go Back", "need_help": " Need help?", "contact_support": "Contact Support"}, "faqs": {"items": [{"question": "How do I reset my password?", "answer": "To reset your password, click on the 'Forgot Password' link on the login page. Enter your email address, and we'll send you instructions to reset your password."}, {"question": "How can I update my profile information?", "answer": "Log in to your account, go to the 'Settings' page, and click on 'Edit Profile'. Make your changes and click 'Save' to update your information."}, {"question": "What payment methods do you accept?", "answer": "We accept various payment methods including credit/debit cards (Visa, MasterCard, American Express), PayPal, and bank transfers."}, {"question": "How do I cancel my subscription?", "answer": "To cancel your subscription, go to 'Account <PERSON><PERSON><PERSON>', click on 'Subscription', and then select 'Cancel Subscription'. Follow the prompts to complete the cancellation process."}], "Frequently_asked_questions": "Frequently Asked Questions", "contact_us": "Contact Us", "support_email": "<EMAIL>", "phone": "Phone", "live_chart": "Live Chat", "start_chat": "Start a chat", "additional_resources": "Additional Resources", "user_guide": "User Guide", "video": "Video Tutorials", "community": "Community Forums", "api_documentation": "API Documentation"}, "students": {"organs": {"session": {"no-lessons": "No upcoming sessions found.", "upcoming_session": "Upcoming Sessions", "view_all": "View All", "reschedule_session": "You can reschedule sessions to a more convenient time"}}}, "conference": {"livescreen": {"you_call": "You left the call", "jion_again": "Join Again"}, "videoconference": {"agora": " Agora Video Conference", "leave_call": "Leave Call", "jion_call": "Join Call"}, "videoplayer": {"no_video": "No Video"}}, "tutors": {"withcoursecomponent": {"editing_component": "Editing Component", "test_component": "Test Component: ", "edit_test": "Edit Test Component: "}, "coursecard": {"course": "Course: ", "resources": " Resources: ", "review": "reviews", "curriculum": " Curriculum", "view": "View", "edit": "Edit", "delete": "Delete", "course_curriculum": "Course Curriculum", "share": "Share", "duplicate_course": "Duplicate Course", "archive": "archive"}, "dashboardheader": {"welcome": "Welcome,", "description": "Here is what has been happening with both your teaching and learning journey.", "monthly": "Monthly", "weekly": "Weekly", "daily": "Daily"}, "gradecard": {"grade_assignment": "Grade Assignment", "select_grade": "Select Grade:", "motivation": "Motivation", "cancel": "Cancel", "save": "Save"}, "lessontable": {"title": "Available Lessons", "view_all": "View all", "search_lesson": "Search lessons...", "all_statuses": "All Statuses", "all_courses": "All Courses", "sn": "SN", "student": "Student", "course": "Course", "hour": "Hour", "date": "Date", "amount": "Amount", "status": "Status", "no_lesson": " No lessons found"}, "payment": {"recent_transaction": "Recent Transactions", "view_all": "View all"}, "studentlessoncard": {"paid": "paid", "min_lesson": "min lesson", "lesson": "lessons", "one_on_one": "One-on-One ", "resource": " Resources:", "curriculum_category": " Curriculum Category: ", "assignment": " Assignment: ", "status": "Status:", "submitted": "Submitted", "assign_assignment": "Assign Assignment", "grade_assignment": "Grade Assignment"}, "upcoming": {"view_all": "View All"}, "header": {"accounting_setting": "Account <PERSON><PERSON>", "profile": "Profile", "my_wallet": "My Wallet", "help_and_support": "Help and Support", "logout": "Logout", "turn_off_notification": "Turn off notifications for this type", "mute": "Mute for {{value}} hours", "notification_settings": "Notification settings", "notifications": "Notifications", "nothing_here": "Nothing here.", "become_a_student": "Become a Student", "visit_student_dashboard": "Visit Student Dashboard"}, "coursecurriculum": {"curriculum": "Curriculum", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advance": "Advanced", "introduction_to_physics": "Introduction to Physics", "mechanics": "Mechanics", "thermodynamics": "Thermodynamics", "wave": "Wave and Oscillation", "electricity_and_megnetism": "Electricity and Magnetism", "advanced_mechanics": "Advanced Mechanics", "electromagnetic": "Electromagnetic Theory", "optics_physics": "Optics and Modern Physics", "thermodynamics_statistical": "Thermodynamics and Statistical Mechanics", "nuclear_particle": "Nuclear and Particle Physics"}, "editcurriculum": {"categories": "Categories", "category_name": "Category:", "add_module": "+ Add Module", "save_changes": "Save changes", "module": "Module {{index}}"}, "coursedescription": {"description": "Description", "skill_level": "Skill Level: ", "last_updated": "Last Updated: ", "pricing": "Pricing", "currency": "Currency:", "price_per_hour": "Price per hour", "students": "Students", "hours_total": "hours total", "ui_ux": "UI/UX Design", "description_lorem": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam a neque faucibus, mollis tortor sed, tristique ex. Praesent nibh justo, semper vel elementum eget, luctus in elit. Integer laoreet augue tortor, ac ultricies velit vulputate eget. Phasellus tempus dapibus sem, a consequat nunc consequat nec. Ut pulvinar egestas nisl eu sollicitudin. Integer ornare lectus sed augue fringilla tristique. Cras condimentum tristique est eget tempor. Vivamus fermentum, purus eu fermentum ultricies, turpis neque tempor justo, et maximus velit dui non magna.", "short_description": "Design thinking, design principles, empathy, responsive design. Description continues ...", "language": "English", "beginner": "<PERSON><PERSON><PERSON>", "november": "November {{value}}", "editcoursedescription": {"ui_ux": "UI/UX Design", "description_lorem": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam a neque faucibus, mollis tortor sed, tristique ex. Praesent nibh justo, semper vel elementum eget, luctus in elit. Integer laoreet augue tortor, ac ultricies velit vulputate eget. Phasellus tempus dapibus sem, a consequat nunc consequat nec. Ut pulvinar egestas nisl eu sollicitudin. Integer ornare lectus sed augue fringilla tristique. Cras condimentum tristique est eget tempor. Vivamus fermentum, purus eu fermentum ultricies, turpis neque tempor justo, et maximus velit dui non magna.", "short_description": "Design thinking, design principles, empathy, responsive design. Description continues ...", "language": "English", "beginner": "<PERSON><PERSON><PERSON>", "november": "November {{value}}", "intermediate": "Intermediate", "advanced": "Advanced", "description": "Description", "skill_level": "Skill Level: ", "pricing": "Pricing", "currency": "Currency:", "price_per_hour": "Price per hour", "students": "Students", "hours_total": "hours total", "save_changes": "Save changes"}}, "courseprerequisites": {"concepts": "Concepts", "description": "In order to progress in this course, you need some understanding in the following concepts:", "tools": "Tools", "tool_needed": "Tools needed for this course:", "resources": "Resources ", "add_resourceses": "Add Resources", "addresourcemodal": {"add_a_resource": "Add a Resource", "title": "Title", "type": "Type", "filename": "Filename", "link": "Link", "add": "Add", "cancel": "Cancel"}, "editresourcemodal": {"edot_resource": "Edit Resource", "title": "Title", "type": "Type", "filename": "Filename", "link": "Link", "save": "Save", "cancel": "Cancel"}}, "editcourseprerequisites": {"concepts": " Concepts", "description": "In order to progress in this course, you need some understanding in the following concepts:", "add_Concept": "Add Concept", "tools": "Tools", "tool_needed": "Tools needed for this course:", "add_tool": "<PERSON><PERSON>", "resources": "Resources ", "add_resourceses": " Add Resources"}, "coursereviews": {"student_feedback": "Student Feedback", "review": "Reviews", "all_ratings": "All Ratings", "search_here_placeholder": "Search here", "votes": "Votes", "replies": "Replies", "reply": "Reply", "see_more_reviews": "See more reviews", "recommend": "You are good at what you do, I will recommend you to anyone wishing to learn software development.", "min_ago": "{{value}} minutes ago", "nice": "Very nice", "you_better": "You can do better.", "min": "{{value}} minutes ago"}, "coursedetails": {"delete_course": "Delete Course"}, "courses": {"add_course": " Add Course", "no_data": "No data yet. Your courses will appear here.", "name": "UI/UX Design", "description": "Design thinking, design principles, empathy, responsive design. Description continues ...", "php": "PHP", "design_systems": "Design Systems", "system_design": "Designing the system of design programming", "courses": " Courses "}, "dashboard": {"total_income": "Total Income (USD)", "active_student": "Active Students", "total_lesson": "Total Lessons", "total_hour": "Total Hours", "total_session": "Total Sessions"}, "lessonrequest": {"lesson_request": "Lesson Request Details", "message_student": "Message Student", "course": "Course: UI/UX Design", "hours": "Hours: {{value}} Hours", "time": "Time: {{value}}", "language": "Language: English", "paid": "Paid", "start_lesson": "Start Lesson", "decline": "Decline", "curriculum": " Curriculum", "customize_curriculum": "Customize Curriculum", "beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advance": "Advanced", "introduction_to_physics": "Introduction to Physics", "mechanics": "Mechanics", "wave": "Wave and Oscillation", "thermodynamics": "Thermodynamics", "electricity_and_megnetism": "Electricity and Magnetism", "advanced_mechanics": "Advanced Mechanics", "electromagnetic": "Electromagnetic Theory", "optics_physics": "Optics and Modern Physics", "thermodynamics_statistical": "Thermodynamics and Statistical Mechanics", "nuclear_particle": "Nuclear and Particle Physics"}, "lessonstudentdetails": {"stephanie": " <PERSON>", "message": "Message", "number_complete": "Number of completed lessons: ", "course": "Courses:", "lesson": "Lessons"}, "lessonstudent": {"student": "Student", "available_student": "Available Students", "filter_by": " Filter By:", "course": " Course ", "date": "Date", "lesson": "Lesson", "sn": "SN", "students": "Student", "courses": " Courses ", "enrollment_date": "Enrollment Date", "no_lesson": "No of Lessons", "actions": "Actions", "no_data": "No data"}, "transaction": {"transactionhistory": {"all_transaction": "All Transactions", "withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Deposits", "date": "Date", "type": "Type", "amount": "Amount", "status": "Status", "description": "Description", "page": "Page"}, "tutorwallet": {"your_wallet": "Your Wallet", "manage_earning": "Manage your earnings and transactions", "current_balance": "Current Balance", "withdraw_fund": "Withdraw Funds", "transaction_history": "Transaction History"}, "withdrawfunds": {"select_withdrawal": "Select Withdrawal Method", "bank_name": "Bank Name", "account_number": "Account Number", "account_holder_name": "Account Holder Name", "paypal": "PayPal Email", "mobile_number": "Mobile Number", "select_provider": "Select provider", "mtn": "MTN", "orange": "ORANGE", "amount_to_withdraw": "Amount to Withdraw", "request_withdrawal": "Request Withdrawal"}}, "profilesetting": {"about": "About ", "full_name": "Full Name", "phone_number": "Phone Number", "country": "Country", "cameroon": "Cameroon", "nigeria": "Nigeria", "ghana": "Ghana", "bio": "Bio", "day": "Day", "type_placeholder": "Type bio here...", "save": "Save", "pricing_available": "Pricing and Availability", "pricing": "Pricing", "amount": "Amount", "currency": "<PERSON><PERSON><PERSON><PERSON>", "availability": "Availability", "teaching_schedule": "Set your teaching schedule", "time_of_day": "Time of Day", "slot": "Time Slot", "from": "From", "to": "To", "time_slot": "+ Add another Time Slot", "save_changes": "Save Changes", "switch_language": "Switch to your preferred Language", "language": "Language:", "english": "English", "french": "French", "update": "Update", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "statistics": {"page_under_development": "This page is in under development....", "tutor_dashboard": "Tutor Dashboard", "analytics_statistics": "Analytics and statistics for ", "total_session": "Total Sessions", "12%": "{{value}} from last month", "total_hours": "Total Hours", "8%": "{{value}} from last month", "active_students": "Active Students", "month": "{{value}}  new this month", "average_rating": "Average Rating", "0.2_increase": "{{value}} increase", "completion_rate": "Completion Rate", "2%": "{{value}} from last month", "total_earning": "Total Earnings", "15%": "{{value}} from last month", "overview": "overview", "student_progress": "Student Progress", "students": "Student", "weekly": "Weekly", "monthly": "Monthly", "upcoming_session": "Upcoming Sessions", "mathematics": "Mathematics", "tomorrow": "Tomorrow, {{value}}", "min": "{{value}} min", "english": "English", "wednesday": "Wednesday, {{value}}", "min_45": "{{value}} min", "science": "Science", "friday": "Friday, {{value}} ", "min_90": "{{value}} min", "session_distribution": " Session Distribution by Subject", "student_details": "Student Details", "active_student": "List of all your active students and their progress", "subject": "Subject", "progress": "Progress", "sessions": "sessions", "last_session": "Last session", "mathematic": "Mathematics", "mar_28": "Mar {{value}}", "mar_25": "Mar {{value}}", "mar_27": "Mar {{value}}", "mar_29": "Mar {{value}}", "history": "History", "mar_26": "Mar {{value}}", "subject_analysis": "Subject Analysis", "session_by": "Sessions by Subject", "earning_by": "Earnings by Subject", "subject_performance": "Subject Performance", "details": "Details on each subject you teach", "avg": " Avg. Session Length", "avg_rating": "Avg. <PERSON>ing", "earning": " Earnings"}, "calender": {"customtoolbar": {"calender": "Calendar", "list": "List", "search": "Search"}, "calendersidebar": {"recent_activities": "Recent Activities", "no_activity": "No activity", "web_development": "Web Development", "data_science": "Data Science", "machine_learning": "Machine Learning", "artificial_intelligence": "Artificial Intelligence", "software_engineering": "Software Engineering"}, "listview": {"date": "Date", "agenda": "Agenda", "course": " Course", "hours": "Hours", "status": "Status"}}}, "tutoronboarding": {"onboardfive": {"pricing_and_availability": "Pricing and Availability"}, "onboardfour": {"your_profile": "Your Profile", "profile_photo": "Profile Photo", "choose_photo": "Choose a photo that will help learners get to know you.", "drag_drop": "Drag and Drop your image here", "or": "or", "upload": "Upload", "from_system": "from system", "learn_more": "Learn more", "video_url": "Video URL", "short_video": "Create a short intro video of yourself, upload it to a cloud-sharing platform like YouTube or Google Drive, and embed the link. Video length should be between 30s to 3 mins.", "video_placeholder": "Paste link to video", "your_video": "Your video will be previewed here", "prev": "Prev", "next": "Next"}, "onboardthree": {"education_and_certification": "Education and Cerifications", "description": "you can mention any relavant certificate attained, while providing name of issuing institution, date and description", "certificate": " Certificate", "subject": "Subject:", "select_subject": " Select Subject", "description_of_certicate": "Description of Certificate", "certificate_placeholder": "More about the cerfificate", "issued_by": "Issued By", "issued_by_placeholder": "Issued By", "years_of_study": " Years of study", "start_date_placeholder": "start date", "end_date_placeholder": "end date", "upload_certificate": "Upload Certificate", "select_file": "Select File", "remove_certificate": "Remove certificate", "add_another_certificate": "Add another certificate", "prev": "Prev", "next": "Next"}, "onboardtwo": {"about": "About ", "teaching_experience": "About you teaching experience:", "description": "The tutor is required to write about themselves, making  sure to mention their experience and relevant certificates. Making sure not to disclose any information about themselves", "motivation": "Motivation", "second_section": "The second section will require the tutor to motivate the students and tell them about their teaching methodology.", "language": "Language", "select_atleast_a_language": "Select at least one language starting with the most proficient", "select_languages": "Select Languages", "prev": "Prev", "next": "Next"}, "onboardone": {"welcome_to_learnoso": "Welcome to Learnoso", "select_max_two": "Note that you are required to select a maximum of two courses to be taught", "select_course": " Select course to be taught", "next": "Next"}, "onboardsuccess": {"account_updated_successful": "Account Updated <span>Successful</span> ", "notified_application": "  You will be notified of the status of your application with 2-5 working days", "error_fetching": "Error Fetching courses", "next": "Next"}, "onboardpreviewinfo": {"information_preview": "Information Preview", "information_submit": "Review the following information before submitting", "submit": "Submit"}, "molecules": {"stepfive": {"addtimeslotbutton": {"time_slot": "Add another Time Slot"}, "availableheader": {"available": "Availability ", "description": "Availability shows students your working hours and when you will be available"}, "enableavailability": {"enable": " Enable", "quick_enable": "Quickly enable and disable availability"}, "pricingsection": {"pricing": "Pricing", "pricing_per_hour": "Pricing is base per hour", "select_country": "Select currency:", "select_currency": " Select currency", "enter_price": "Enter price here in selected currency"}, "timezoneselect": {"select_time_zone": "Select time zone:"}}, "steppreview": {"aboutyou": {"aboutyou": "About you", "teaching_experience": "About your teaching experience:", "tutor_required": "The tutor is required to write about themselves, making sure to mention their experience and relevant certificates.", "motivation": "Motivation", "tutor_motivation": " The tutor should write about their motivation and what drives them to teach."}, "educationcertification": {"education_certification": "Education and Certifications", "mention": "Mention relevant certificates, issuing institution, and dates.", "subject": "Subject:", "cerfificate": "Certificate:", "description_of_certificate": "Description of Certificate", "issued_by": " Issued By:", "years_of_study": " Years of study", "upload_file": "Uploaded File:"}, "languages": {"language": "Languages"}, "pricingavailable": {"pricing_available": "Pricing and Availability", "pricing": "Pricing", "pricing_per_hour": "Pricing is per hour", "availability": "Availability", "working_hour": "Shows students your working hours and when you will be available.", "from": "From", "to": "To"}, "profile": {"video_url": "Video URL", "short_video": "create a short intro video of themselves, upload it to a cloud-sharing  platform like YouTube or Google Drive, etc,and then embed the link into it. Video length should 30s to 3mins."}, "selectedcourses": {"selct_course": "Selected Courses:"}}}}, "admin": {"adminnavbar": {"accounting_setting": "Account <PERSON><PERSON>", "profile": "Profile", "help_and_support": "Help and Support", "logout": "Logout", "dashboard": "Dashboard", "users": "Users", "application": "Applications", "transactions": "Transactions"}, "applicationheader": {"loading": "Loading...", "error_fetching": "Error fetching data", "tutoring": "Tutoring Applications", "recent_applicants": "Here is an overview of recent applicants on your system", "total_applications": "Total Applications", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "applicationfilter": {"filter_by": " Filter By:", "names_placeholder": "Names", "all_names": "All Names", "course_placeholder": "Course", "country_placeholder": "Country", "language_placeholder": "Language", "search_here": "Search here"}, "applicationspagination": {"showing": "Showing", "out_of": "Out of"}, "applicaitontable": {"approve": "Approve", "reject": " Reject", "revert": "<PERSON><PERSON>", "sn": "SN", "name": "Name", "email": "Email", "country": "Country", "language": "Language", "course": " Course ", "date": "Date", "status": "Status", "actions": "Actions", "no_applicant": "No Applicant"}, "courseengagementchart": {"course_engegement": "Course Engagement", "year_placeholder": "Year", "student": "Student", "tutor": "Tutor"}, "notification": {"application": "Application", "design": "UI/UX Design", "pending": "Pending", "course_completion": "Course Completion", "withdrawal_request": "Withdrawal Request", "completed": "Completed", "notifications": "Notifications", "view_all": "View All", "recent_changes": "You recent changes..", "charges": "Charges:"}, "recentapplication": {"all_applications": "All Applications", "error_loading_applications": "Error loading applications"}, "userengagementchart": {"user_engagement": "User Engagement", "year_placeholder": "Year"}, "applicationdashboard": {"all_applications": "All Applications", "error_loading_applications": "Error loading applications"}, "approvedapplications": {"approved_applications": "approvedapplications", "error_loading_applications": "Error loading applications"}, "pendingapplication": {"pending_applications": "Pending Applications", "error_loading_applications": "Error loading applications"}, "rejectedapplications": {"rejectd_applications": "Rejected Applications", "error_loading_applications": "Error loading applications"}, "transaction": {"earningchart": {"no_data": "No data"}, "revenue": {"no_data": "No data"}, "transactions": {"loading": "Loading transactions...", "error_loading_transactions": "Error loading transactions", "filter_by": "Filter By:", "id": "ID", "type": "Type", "amount": "Amount", "date": "Date", "status": "Status", "actions": "Actions", "view_details": "View details", "delete_transaction": "Delete transaction", "no_transactions": "No transactions found", "showing": "Showing", "of": "of"}, "welcome": "Welcome", "your_application": "Here is an overview of your application", "total_revenue": "Total Revenue", "total_transfer": "Total Transfer", "expenses": "Expenses", "balance": "Balance", "revenues": "Revenue", "total_earning": "Total Earnings", "recent_transactions": "Recent Transactions"}, "user": {"userheader": {"users": "Users", "your_system": "Here is an overview of your system users.", "total_users": "Total users", "students": "Students", "tutors": "Tutors", "administrators": "Administrators", "guests": "Guests"}, "usertable": {"role_placeholder": "Role", "email_placeholder": "Email", "all_roles": "All Roles", "tutor": "Tutors", "student": "Students", "tutor_and_students": "Tutors & Students", "all_emails": "All Emails", "gmail": "Gmail", "yahoo": "Yahoo", "example": "example", "country_placeholder": "Country", "all_countries": "All Countries", "cameroon": "Cameroon", "botswana": "Botswana", "cmr": "Cmr", "status_placeholder": "Status", "all_statuses": "All Statuses", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "search_here": "Search here", "error_loading_data": "Error loading data. Please try again.", "id": "ID", "name": "Name", "email": "Email", "country": "Country", "date_joined": "Date Joined", "role": "Role", "status": "Status", "actions": "Actions", "no_users_found": " No users found", "showing": "Showing", "to": "to", "of": "of", "results": "results"}, "acitve": "Active", "disabled": "Disabled"}, "total_income": "Total Income", "welcome": "Welcome", "overview": "Here is an overview of your System", "students": "Students", "tutors": "Tutors", "total_courses": "Total Courses", "total_sessions": "Total sessions"}, "student": {"studentdashboard": {"filter_tutor": "Filter <PERSON>", "langauge": "Language", "any": "Any", "english": "English", "french": "French", "spanish": "Spanish", "subject": " Subject", "maths": "e.g., Math, Science", "cost": " Cost (F)", "max": "Max", "min": "Min"}, "tutordetails": {"availability": "Availability", "days": "Day(s)", "time_zone": "Timezone", "review": "Write a Review", "rating": "Rating", "comment": "Comment", "submit_review": "Submit Review", "lesson": " lessons", "student": "Students", "active_student": "active Students", "about_me": "About Me", "book": "Book trial lesson", "send_message": "Send Message", "curriculum": "Curriculum", "resources": "Resources", "get_resources": "Get Resources", "reviews": "Reviews", "introduction_to_physics": "Introduction to Physics", "mechanics": "Mechanics", "thermodynamics": "Thermodynamics", "wave_and_oscillation": "Wave and Oscillation", "electricity_and_magnetism": "Electricity and Magnetism", "advanced_mechanics": "Advanced Mechanics", "electromagnetic_theory": "Electromagnetic Theory", "optics_and_modern_physics": "Optics and Modern Physics", "thermodynamics_and_statistical_mechanics": "Thermodynamics and Statistical Mechanics", "nuclear_and_particle_physics": "Nuclear and Particle Physics"}, "studentsessions": {"total_sessions": "Total sessions", "upcoming_sessions": "Upcoming Sessions", "past_sessions": "Past Sessions", "total_hours": "Total Hours", "upcoming_session": "Upcoming Session", "past_session": "Past Sessions", "search": "Search", "search_for_sessions": "Search for session", "search_button": "Search", "calendar": "Calendar"}, "resources": {"study_guides": "Study Guides", "comprehensive_materials": "Comprehensive study materials for various subjects to help you excel in your courses", "browse_study_guides": "Browse Study Guides", "video_tutorials": "Video Tutorials", "in_depth_video": "In-depth video explanations of complex topics from experienced tutors.", "watch_tutorials": "Watch Tutorials", "practice_test": "Practice Tests", "prepare_for_your_exams": "Prepare for your exams with our extensive collection of practice tests and quizzes.", "take_practice_test": "Take Practice Tests", "reccomended_books": "Recommended Books", "curated_list_of_textbooks": "Curated list of textbooks and supplementary reading materials for each subject.", "view_book_list": "View Book List", "downloadable_worksheets": "Downloadable Worksheets", "printable_worksheets": "Printable worksheets and exercises to reinforce your learning offline.", "download_worksheet": "Download Worksheets", "online_forum": "Online Forums", "connect_with_other_students": "Connect with fellow students and tutors to discuss academic topics and get help.", "join_discussions": "Join <PERSON>ussions", "student_resources": "Student Resources", "explore_a_variety_of_materials": "Explore a variety of materials to support your learning journey.", "welcome_to_our_comprehensive_resource_center": " Welcome to our comprehensive resource center! Here you'll find a variety of materials to support your learning journey. From studyguides to practice tests, we've got you covered. Explore the resourcesbelow to enhance your academic performance.", "learnoso": "<PERSON><PERSON><PERSON>. All rights reserved."}, "wallet": {"fund_your_wallet": "Fund Your Wallet", "choose_your_preferred_payment_method": "Choose your preferred payment method", "current_balance": "Current Balance", "credit_card": "Credit Card", "mobile_money": "Mobile Money", "select_payment_processor": "Select Payment Processor", "pay_with_stripe": " Pay with Stripe", "pay_with_paypal": "Pay with PayPal", "currency": "<PERSON><PERSON><PERSON><PERSON>", "loading_currency": "Loading currencies", "amount": " Amount", "amount_is_required": "Amount is required", "enter_amount": "Enter amount", "pay_with": "Pay with", "amount_mobile_money": "Amount", "enter_amount_mobile_money": "Enter amount", "provider_mobile_money": "Provider", "mtn": "MTN", "orange": " Orange", "phone_number": " Phone Number", "amount_is_required_mobile_money": "Amount is required", "phone_number_is_required": "Phone number is required", "place_holder": "e.g. *********", "fund_wallet": "Fund Wallet"}, "profile": {"personal_info": "Personal Information", "edit_details": "Edit Details", "full_names": "Full Names", "email": "Email", "phone": "Phone", "add_phone_number": "Add phone number", "country": "Country", "notifications": "  Notifications", "recieve_notifications": " Receive notifications on ", "password_security": "Password and Security", "current_password": "Current Password", "enter_current_password": "Enter current password", "new_password": " New Password", "enter_new_password": "Enter a new password", "repeat_new_password": "Repeat New Password", "re-type_new_password": "Re-type new password", "change_password": "Change Password"}, "editprofile": {"update_your_personal_info": "Update your personal information", "first_name": "First Name", "last_name": "Last Name", "email": "email", "phone_number": "Phone Number", "country": "Country", "language": "Language", "bio": "Bio", "tell_us_about_yourself": "Tell us about yourself...", "cancel": " Cancel", "save_changes": "Save Changes", "edit_profile": "Edit Profil"}, "booktraillesson": {"select_a_course": "Select a Course", "when_would_you_like_your_session": "When would you like your session", "choose_date_and_time": "Choose Date and Time", "morning": "Morning", "session_details": "Session Details", "session_type": "Session Type", "one_on_one": "One-on-One", "group": "Group", "duration": "Duration", "min": "20mins", "use_trail_session": "Use trial sessions to discuss your level and learning plan with your tutor.", "finish": "Finish"}, "checkouttriallesson": {"no_tutor": "No tutor ", "book_session": "Book session", "select_a_course": "Select a Course", "review": "(0 Reviews)", "min": "20 min", "session": "Session", "your_order": "Your Order", "20mins_lesson": "20-min <PERSON><PERSON>", "processing_fee": "Processing fee", "choose_a_payment_method": "Choose Payment Method", "master_card": "Master Card", "mobile_money": "Mobile Money", "card_number": "Card Number", "mm": "MM", "yy": "YY", "expiration_date_required": "Expiration year is required", "must_be_a_valid_2digit_number": "Must be a valid 2-digit year", "cvc_is_required": "CVC is required", "cvc_must_vali": "Must be a valid 3 or 4 digit CVC", "save_this_card_for_future_payments": " Save this card for future payments", "by_confirming_this_payment_you_agree": "By confirming this payment, you agree to our", "refund": "Refund", "and": "and", "payment_policy": "Payment Policy", "network_provider": " Network Provider:", "mtn": " mtn", "orange": "orange", "account_number": "Account Number", "phone_number_is_required": "Phone number is required", "must_be_a_valid_orange_or_mtn_number": "Must be a valid MTN or Orange number (e.g., *********)", "confirm_payment": "Confirm Payment", "payment": "Payment", "sucessful": "Successfully", "processed": "Processed", "you_will_be_notified": "You will be notified on the status of your session before the scheduled date.", "done": "Done"}, "molecules": {"filters": {"day_of_the_week": "Day of the Week", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "sessioncard": {"on_the": "on the", "just_now": "just now"}}, "organs": {"sessions": {"no_upcoming_sessions_found": "No upcoming sessions found.", "upcoming_sessions": "Upcoming Sessions", "you_can_reschedule": "You can reschedule sessions to a more convenient time"}, "tutors": {"find_the_perfect_tutor": "Find the perfect tutor here!", "looking_for_a_personalized_tutor": "Looking for a personalized and interactive tutor experience? Browsethrough 3200 online tutors to find the perfect fit.", "search_for_a_tutor": "Search for tutor by name or lessons", "recommended_for_you": " Recommended For You", "previous": "Previous", "page": "Page", "of": "of", "next": "Next", "no_tutors": "No Tutors", "search": " Search"}, "upcoming_sessions": {"no_upcoming_session": "No upcoming sessions", "book_a_lesson": "Book a Lesson"}}, "partials": {"studentheader": {"account_setting": "Account <PERSON><PERSON>", "profile": "Profile", "help_and_support": "Help and Support", "logout": "Logout", "turn_off_notifications_for_this_type": "Turn off notifications for this type", "muted_for_24hours": "Mute for 24 hours", "notification_settings": "Notification settings", "notifications": "  Notifications", "nothing_here": "Nothing here.", "learnoso": "learnoso"}, "steps": {"onboardbudget": {"whats_your_budget": "What’s your Budget per lesson", "budget": "Budget", "this_fiels_is_required": "This field is required", "currency": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Amount", "this_field_is_required2": "This field is required", "currency_and_amount": " Currency and Amount required", "continue": "Continue"}, "onboardfinal": {"lets_find_the_perfect_tutor": "Let's find the Perfect Tutor", "hold_on": "Hold on, this might take a moment"}, "onboardgetstarted": {"lets_get_your_preferences": "Let’s get to know your Preferences for a personalized learning", "get_started": "Get Started"}, "onboardgoals": {"what_are_your_goals": "What are your goals?", "tell_us_why": "Tell us why you want to learn French", "select_atleast_a_goal": "Select at least one goal", "continue": "Continue"}, "onboardlevel": {"whats_your_level": "}What’s your level in ", "please_select_your_level": "Please select your level", "continue": "Continue"}, "onboardsession": {"when_would_you_like_to_have_your_session": "When would you like to have your sessions?", "select_a_timezone": "Select time zone :", "please_select_a_timezone": "Please select a timezone", "select_a_timeone1": "Select time zone", "done": "Done"}, "onboardsubject": {"what_would_you_like_to_learn": "What would you like to learn?", "this_field_is_required": "This field is required", "select_preferred_learning_subject": "Select preferred Learning Subject", "could_not_load_course": " Could not load course", "language": "Language:", "this_field_is_required1": "This field is required", "select_preferred_learning_language": "Select preferred Learning Language", "continue": "Continue"}}}, "onboardfinal": {"lets_find_the_perfect_tutor": "Let's find the Perfect Tutor", "hold_on": "Hold on, this might take a moment"}, "onboardgetstarted": {"lets_get_your_preferences": "Let’s get to know your Preferences for a personalized learning", "get_started": "Get Started"}, "onboardgoals": {"what_are_your_goals": "What are your goals?", "tell_us_why": "Tell us why you want to learn French", "select_atleast_a_goal": "Select at least one goal", "continue": "Continue"}, "onboardlevel": {"whats_your_level": "}What’s your level in ", "please_select_your_level": "Please select your level", "continue": "Continue"}, "onboardsession": {"when_would_you_like_to_have_your_session": "When would you like to have your sessions?", "select_a_timezone": "Select time zone :", "please_select_a_timezone": "Please select a timezone", "select_a_timeone1": "Select time zone", "done": "Done"}, "onboardsubject": {"what_would_you_like_to_learn": "What would you like to learn?", "this_field_is_required": "This field is required", "select_preferred_learning_subject": "Select preferred Learning Subject", "could_not_load_course": " Could not load course", "language": "Language:", "this_field_is_required1": "This field is required", "select_preferred_learning_language": "Select preferred Learning Language", "continue": "Continue"}}}