<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 343.15 423.53">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
      }

      .cls-2 {
        fill: none;
      }
    </style>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <g>
      <rect class="cls-2" width="343.15" height="423.53"/>
      <g>
        <path class="cls-1" d="m190.03,204.08c0,1.15-.44,2.32-1.31,3.2-6.07,6.08-56.65,56.67-80.53,80.55-21.47-17.91-35.24-44.74-35.62-74.79,0-.43,0-.85,0-1.28,0-.35,0-.7,0-1.05.03-1.78.09-3.55.19-5.31,0-.07,0-.13.01-.2.02-.36.05-.71.07-1.06,2.38-31.33,19.35-58.59,44.13-74.99l71.48,71.49.23.23c.89.89,1.33,2.03,1.33,3.21Z"/>
        <path class="cls-1" d="m270.58,211.76c0,.54,0,1.07-.01,1.61,0,.33-.01.65-.02.97,0,.42-.02.84-.04,1.26-.01.33-.03.66-.04.99-.03.52-.05,1.05-.09,1.58-.05.79-.11,1.58-.18,2.36-.07.87-.17,1.74-.26,2.6-.05.4-.09.79-.14,1.19-.06.44-.11.87-.17,1.3,0,.03-.01.07-.01.11-.11.75-.22,1.5-.34,2.25-7.74,46.97-48.53,82.8-97.69,82.8-18.2,0-35.25-4.91-49.9-13.47,32.58-32.58,70.84-70.86,92.66-92.69.04-.05.04-.11,0-.16l-83.09-83.11c12.32-5.5,25.98-8.56,40.34-8.56,49.16,0,89.94,35.83,97.69,82.8.12.74.23,1.49.34,2.24,0,.03,0,.07.01.11.07.48.13.96.19,1.44.04.34.08.68.12,1.03.09.86.19,1.72.26,2.6.07.8.13,1.6.18,2.42.03.52.06,1.05.09,1.58.02.35.03.7.05,1.05.01.36.03.73.03,1.09,0,.34.01.68.02,1.03,0,.53.01,1.07.01,1.6Z"/>
      </g>
    </g>
  </g>
</svg>