import { Course } from "@/features/tutor/types";
import { TypeAssertionError } from "../util/error";

import {
  OnboardFiveFormData,
  OnboardFourFormData,
  OnboardOneFormData,
  OnboardThreeFormData,
  OnboardTwoFormData,
  TimeSlot,
  TutorCertificate,
  TutorValueType,
} from "@/features/tutorOnboarding/types";

import { TutorDataKey } from "@/features/tutorOnboarding/types";

export const assertTutorType = (
  key: TutorDataKey,
  value: TutorValueType,
): void => {
  switch (key) {
    case "stepOneData":
      if (!isOnboardOneFormData(value) && typeof value !== "undefined") {
        throw TypeAssertionError.from(value, "OnboardOneFormData or undefined");
      }
      break;
    case "stepTwoData":
      if (!isOnboardTwoFormData(value) && typeof value !== "undefined") {
        throw TypeAssertionError.from(value, "OnboardTwoFormData or undefined");
      }
      break;
    case "stepThreeData":
      if (!isOnboardThreeFormData(value) && typeof value !== "undefined") {
        throw TypeAssertionError.from(
          value,
          "OnboardThreeFormData or undefined",
        );
      }
      break;
    case "stepFourData":
      if (!isOnboardFourFormData(value) && typeof value !== "undefined") {
        throw TypeAssertionError.from(
          value,
          "OnboardFourFormData or undefined",
        );
      }
      break;
    case "stepFiveData":
      if (!isOnboardFiveFormData(value) && typeof value !== "undefined") {
        throw TypeAssertionError.from(
          value,
          "OnboardFiveFormData or undefined",
        );
      }
      break;
    default:
      throw new TypeAssertionError(`Invalid key: '${key}'`);
  }
};

const isOnboardOneFormData = (value: any): value is OnboardOneFormData => {
  return (
    value &&
    Array.isArray(value.courses) &&
    value.courses.every((item: Course) => typeof item.id === "number")
  );
};

const isOnboardTwoFormData = (value: any): value is OnboardTwoFormData => {
  return (
    value &&
    typeof value.experience === "string" &&
    typeof value.language === "number" &&
    typeof value.motivation === "string"
  );
};

const isOnboardThreeFormData = (value: any): value is OnboardThreeFormData => {
  return (
    value &&
    Array.isArray(value.certificates) &&
    value.certificates.every(
      (certificate: TutorCertificate) =>
        typeof certificate.certificateSubject === "string" &&
        typeof certificate.certificateTitle === "string" &&
        typeof certificate.certificateDescription === "string" &&
        typeof certificate.certificateIssuedBy === "string" &&
        typeof certificate.startDate === "string" &&
        typeof certificate.endDate === "string" &&
        Array.isArray(certificate.fileUpload) &&
        certificate.fileUpload.every((file) => file instanceof FileList),
    )
  );
};

const isOnboardFourFormData = (value: any): value is OnboardFourFormData => {
  return (
    value &&
    Array.isArray(value.profilePhoto) &&
    value.profilePhoto.every((file: FileList) => file instanceof FileList) &&
    typeof value.profileVideo === "string"
  );
};

const isOnboardFiveFormData = (value: any): value is OnboardFiveFormData => {
  return (
    value &&
    typeof value.currency === "string" &&
    typeof value.price === "number" &&
    typeof value.availability === "boolean" &&
    typeof value.timezone === "string" &&
    Array.isArray(value.timeslots) &&
    value.timeslots.every(
      (timeslot: TimeSlot) =>
        typeof timeslot.enable === "boolean" &&
        [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
          "Sunday",
        ].includes(timeslot.day) &&
        typeof timeslot.startTime === "string" &&
        typeof timeslot.endTime === "string",
    )
  );
};
