import { <PERSON>rnosoStorageKey } from "@/types";
import { TypeAssertionError } from "./error";

export const assertStorageKeyType = (
  key: <PERSON>rnosoStorageKey,
  value: IUser | string | number,
): void => {
  switch (key) {
    case "learnoso-sessionExpiry":
    case "learnoso-user-session-expire-at":
      if (typeof value !== "number") {
        throw TypeAssertionError.from(value, "number");
      }
      break;
    case "learnoso-sessionId":
    case "learnoso-user-auth-token":
    case "learnoso-user-session-id":
      if (typeof value !== "string") {
        throw TypeAssertionError.from(value, "string");
      }
      break;
    case "learnoso-user-info":
      if (typeof value !== "object") {
        throw TypeAssertionError.from(value, "IUser");
      }
  }
};
