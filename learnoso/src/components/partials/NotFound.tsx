import { ArrowLeft, Home } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

const NotFoundPage = () => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white px-4">
      <div className="text-center max-w-md">
        {/* SVG Illustration */}
        <div className="mb-8 w-full max-w-sm mx-auto">
          <svg
            viewBox="0 0 300 300"
            xmlns="http://www.w3.org/2000/svg"
            className="w-full h-full"
          >
            {/* Background circle */}
            <circle cx="150" cy="150" r="130" fill="#E9ECF5" />

            {/* 404 Text */}
            <text
              x="150"
              y="140"
              fontSize="60"
              fontWeight="700"
              textAnchor="middle"
              fill="#21409A"
            >
              404
            </text>

            {/* Page broken element */}
            <g transform="translate(100, 150)">
              <rect
                x="0"
                y="0"
                width="100"
                height="60"
                rx="4"
                fill="white"
                stroke="#21409A"
                strokeWidth="2"
              />
              <line
                x1="15"
                y1="15"
                x2="85"
                y2="15"
                stroke="#21409A"
                strokeWidth="2"
              />
              <line
                x1="15"
                y1="30"
                x2="65"
                y2="30"
                stroke="#21409A"
                strokeWidth="2"
              />
              <line
                x1="15"
                y1="45"
                x2="75"
                y2="45"
                stroke="#21409A"
                strokeWidth="2"
              />

              {/* Broken corner */}
              <path
                d="M100,15 L120,0 L120,30 Z"
                fill="white"
                stroke="#21409A"
                strokeWidth="2"
              />

              {/* Search icon */}
              <circle
                cx="35"
                cy="-20"
                r="15"
                fill="white"
                stroke="#21409A"
                strokeWidth="2"
              />
              <line
                x1="45"
                y1="-10"
                x2="55"
                y2="0"
                stroke="#21409A"
                strokeWidth="2"
                strokeLinecap="round"
              />
            </g>
          </svg>
        </div>

        {/* Error message */}
        <h1 className="text-3xl font-bold text-black mb-2">
          {t("notfound.page_not_found")}
        </h1>
        <p className="text-gray-500 mb-8">{t("notfound.no_page")}</p>

        {/* Action buttons */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Link
            to="/"
            className="flex items-center justify-center gap-2 px-5 py-2.5 bg-primary text-white rounded-md w-full sm:w-auto hover:bg-primary/90 transition-colors"
          >
            <Home size={18} />
            <span> {t("notfound.back_to_home")}</span>
          </Link>

          <button
            onClick={() => window.history.back()}
            className="flex items-center justify-center gap-2 px-5 py-2.5 border border-slate-200 rounded-md w-full sm:w-auto hover:bg-light-gray transition-colors"
          >
            <ArrowLeft size={18} />
            <span>{t("notfound.go_back")}</span>
          </button>
        </div>
      </div>

      {/* Help link */}
      <div className="mt-12 text-center">
        <p className="text-gray-500">
          {t("notfound.need_help")}{" "}
          <Link to="/help-support" className="text-primary hover:underline">
            {t("notfound.contact_support")}
          </Link>
        </p>
      </div>
    </div>
  );
};

export default NotFoundPage;
