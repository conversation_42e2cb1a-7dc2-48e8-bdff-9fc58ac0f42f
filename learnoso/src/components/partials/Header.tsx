// src/components/Header.tsx
import StudentHeader from "@/features/student/components/partials/StudentHeader";
import TutorHeader from "@/features/tutor/components/partials/TutorHeader";
import { useAuth } from "@/hooks";
import React from "react";
import { useTranslation } from "react-i18next";
import { FaGlobe } from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";

function DefaultHeader() {
  const { pathname } = useLocation();
  const navItems = [
    { label: "Home", path: "/" },
    { label: "Find Tutors", path: "/tutors" },
    { label: "Start Tutoring", path: "/register" },
    { label: "How It Works", path: "/how-it-works" },
  ];
  const { t } = useTranslation();

  return (
    <header className="bg-white shadow-sm ">
      <div className="mx-auto px-28 py-4 flex justify-between items-center">
        <Link to={"/"} className="flex items-center">
          <svg
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.9763 16.2472C20.9763 16.4518 20.8978 16.6601 20.7424 16.8167C19.6584 17.8988 10.6257 26.9024 6.36112 31.1524C2.52695 27.9649 0.0678615 23.1899 0 17.8418C0 17.7653 0 17.6905 0 17.614C0 17.5517 0 17.4894 0 17.4271C0.00535748 17.1103 0.0160729 16.7953 0.0339312 16.4821C0.0339312 16.4696 0.0339316 16.459 0.0357174 16.4465C0.039289 16.3824 0.0446465 16.3201 0.0482181 16.2579C0.473245 10.682 3.50379 5.8304 7.92908 2.91164L20.6942 15.6349L20.7353 15.6759C20.8942 15.8343 20.9728 16.0372 20.9728 16.2472H20.9763Z"
              fill="#FF7F00"
            />
            <path
              d="M35.3611 17.614C35.3611 17.7101 35.3611 17.8045 35.3593 17.9006C35.3593 17.9593 35.3575 18.0162 35.3557 18.0732C35.3557 18.1479 35.3522 18.2227 35.3486 18.2974C35.3468 18.3562 35.3432 18.4149 35.3415 18.4736C35.3361 18.5662 35.3325 18.6605 35.3254 18.7548C35.3165 18.8954 35.3057 19.036 35.2932 19.1748C35.2807 19.3297 35.2629 19.4845 35.2468 19.6376C35.2379 19.7088 35.2307 19.7782 35.2218 19.8494C35.2111 19.9277 35.2022 20.0042 35.1915 20.0807C35.1915 20.0861 35.1897 20.0932 35.1897 20.1003C35.17 20.2338 35.1504 20.3673 35.1289 20.5007C33.7467 28.8602 26.4623 35.2369 17.6832 35.2369C14.433 35.2369 11.3881 34.3631 8.77191 32.8396C14.5901 27.0413 21.4227 20.2284 25.3194 16.3433C25.3265 16.3344 25.3265 16.3237 25.3194 16.3148L10.4809 1.52345C12.6811 0.544598 15.1205 0 17.685 0C26.4641 0 33.7467 6.37678 35.1307 14.7362C35.1522 14.8679 35.1718 15.0014 35.1915 15.1349C35.1915 15.1402 35.1915 15.1473 35.1932 15.1544C35.2057 15.2399 35.2165 15.3253 35.2272 15.4107C35.2343 15.4712 35.2415 15.5317 35.2486 15.594C35.2647 15.7471 35.2825 15.9001 35.295 16.0568C35.3075 16.1991 35.3182 16.3415 35.3272 16.4874C35.3325 16.58 35.3379 16.6743 35.3433 16.7686C35.3468 16.8309 35.3486 16.8932 35.3522 16.9555C35.354 17.0196 35.3575 17.0854 35.3575 17.1495C35.3575 17.21 35.3593 17.2705 35.3611 17.3328C35.3611 17.4271 35.3629 17.5233 35.3629 17.6176L35.3611 17.614Z"
              fill="#21409A"
            />
          </svg>
          <span className="ml-2 text-xl text-primary font-bold">
            {t("common.app-name")}
          </span>
        </Link>
        <nav>
          <ul className="flex space-x-6">
            {navItems.map((item) => (
              <Link
                key={item.label}
                className={`text-gray-500 hover:text-dark ${
                  item.path === pathname
                    ? "border-solid text-primary border-primary border-b-2 font-bold"
                    : ""
                }`}
                to={item.path}
              >
                {item.label}
              </Link>
            ))}
          </ul>
        </nav>
        <div className="flex items-center space-x-4">
          <>
            {" "}
            <Link
              to="/login"
              className="px-4 py-2 text-primary border border-primary rounded hover:bg-blue-50"
            >
              {t("home.header.login")}
            </Link>
            <Link
              to={"/register"}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary"
            >
              {t("home.header.signup")}
            </Link>
          </>

          <FaGlobe className="h-8 w-8 text-primary" />
        </div>

        {/* {isMenuOpen && (


        )} */}
      </div>
    </header>
  );
}

const Header: React.FC = () => {
  const { user } = useAuth();

  switch (user?.current_role) {
    case "student":
      return <StudentHeader />;
    case "tutor":
      return <TutorHeader />;
    default:
      return <DefaultHeader />;
  }
};

export default Header;
