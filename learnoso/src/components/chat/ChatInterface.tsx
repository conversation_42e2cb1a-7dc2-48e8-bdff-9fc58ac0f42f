import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { useToast } from '@/components/ui/Toast';
import { chatService, Message, Conversation, ChatWebSocketManager } from '@/app/services/chat/chat.service';
import { Send, Paperclip, Smile, MoreVertical } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInterfaceProps {
  conversationId: number;
  currentUserId: number;
  className?: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  conversationId,
  currentUserId,
  className,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsManagerRef = useRef<ChatWebSocketManager | null>(null);
  const { error } = useToast();

  useEffect(() => {
    loadMessages();
    initializeWebSocket();
    
    return () => {
      wsManagerRef.current?.disconnect();
    };
  }, [conversationId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await chatService.getMessages(conversationId);
      if (response.success && response.data) {
        setMessages(response.data.data);
      }
    } catch (err) {
      error('Failed to load messages');
    } finally {
      setLoading(false);
    }
  };

  const initializeWebSocket = () => {
    const token = localStorage.getItem('auth_token');
    if (!token) return;

    wsManagerRef.current = chatService.createWebSocketManager(token);
    
    // Listen for new messages
    wsManagerRef.current.on('message-sent', (message: Message) => {
      setMessages(prev => [...prev, message]);
    });

    // Listen for typing indicators
    wsManagerRef.current.on('typing-start', (data: { user_name: string }) => {
      setTypingUsers(prev => [...prev, data.user_name]);
    });

    wsManagerRef.current.on('typing-stop', (data: { user_id: number }) => {
      setTypingUsers(prev => prev.filter(name => name !== data.user_id.toString()));
    });
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      const messageData = {
        conversation_id: conversationId,
        content: newMessage,
        type: 'text' as const,
      };

      // Send via API for persistence
      const response = await chatService.sendMessage(messageData);
      
      if (response.success) {
        // Send via WebSocket for real-time delivery
        wsManagerRef.current?.send('send-message', messageData);
        setNewMessage('');
      }
    } catch (err) {
      error('Failed to send message');
    }
  };

  const handleTyping = () => {
    if (!isTyping) {
      setIsTyping(true);
      wsManagerRef.current?.send('typing-start', { conversation_id: conversationId });
      
      // Stop typing after 3 seconds of inactivity
      setTimeout(() => {
        setIsTyping(false);
        wsManagerRef.current?.send('typing-stop', { conversation_id: conversationId });
      }, 3000);
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <Card className={cn('flex flex-col h-[600px]', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarImage src={conversation?.participants[0]?.profile_image} />
              <AvatarFallback>
                {conversation?.participants[0]?.first_name?.[0]}
                {conversation?.participants[0]?.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold">
                {conversation?.participants
                  .filter(p => p.id !== currentUserId)
                  .map(p => `${p.first_name} ${p.last_name}`)
                  .join(', ')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {conversation?.participants.some(p => p.is_online) ? 'Online' : 'Offline'}
              </p>
            </div>
          </div>
          <Button variant="ghost" size="icon">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col p-0">
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-4 py-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-3',
                  message.sender_id === currentUserId ? 'justify-end' : 'justify-start'
                )}
              >
                {message.sender_id !== currentUserId && (
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={message.sender?.profile_image} />
                    <AvatarFallback className="text-xs">
                      {message.sender?.first_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div
                  className={cn(
                    'max-w-[70%] rounded-lg px-3 py-2',
                    message.sender_id === currentUserId
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className={cn(
                    'text-xs mt-1',
                    message.sender_id === currentUserId
                      ? 'text-primary-foreground/70'
                      : 'text-muted-foreground'
                  )}>
                    {formatTime(message.created_at)}
                  </p>
                </div>
              </div>
            ))}
            
            {typingUsers.length > 0 && (
              <div className="flex gap-3">
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="text-xs">...</AvatarFallback>
                </Avatar>
                <div className="bg-muted rounded-lg px-3 py-2">
                  <p className="text-sm text-muted-foreground">
                    {typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
                  </p>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Button type="button" variant="ghost" size="icon">
              <Paperclip className="h-4 w-4" />
            </Button>
            
            <Input
              value={newMessage}
              onChange={(e) => {
                setNewMessage(e.target.value);
                handleTyping();
              }}
              placeholder="Type a message..."
              className="flex-1"
            />
            
            <Button type="button" variant="ghost" size="icon">
              <Smile className="h-4 w-4" />
            </Button>
            
            <Button type="submit" size="icon" disabled={!newMessage.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
};
