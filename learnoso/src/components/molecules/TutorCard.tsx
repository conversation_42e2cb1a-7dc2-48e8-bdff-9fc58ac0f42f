import { useAuth } from "@/hooks";
import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  FaCalendarAlt,
  FaGlobe,
  FaGraduationCap,
  FaUsers,
} from "react-icons/fa";
import { FaMessage } from "react-icons/fa6";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { LearnosoButton } from "../atoms";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

interface TutorCardProps {
  id: number;
  tutorId: number;
  name: string;
  image: string;
  subject: string;
  lessons: number;
  students: number;
  languages: string;
  description: string;
  price: number;
  activeStudents?: number;
  currency?: string;
}

export const TutorCard = ({
  id,
  tutorId,
  name,
  image,
  subject,
  lessons,
  students,
  activeStudents = 0,
  languages,
  description,
  price,
  currency = "$",
}: TutorCardProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const navigateToTutorProfile = () =>
    navigate(`/student/tutors/${id}/info/${tutorId}`);
  const { user } = useAuth();
  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-4 mb-4"
      initial="hidden"
      animate="visible"
      variants={fadeInUp}
    >
      <div className="flex">
        <motion.img
          src={image}
          alt={name}
          className="w-24 h-24 rounded-lg object-cover mr-4 cursor-pointer"
          whileHover={{ scale: 1.05 }}
          onClick={navigateToTutorProfile}
        />
        <div className="flex-grow">
          <div className="flex justify-between items-start">
            <h3
              className="text-lg font-semibold cursor-pointer"
              onClick={navigateToTutorProfile}
            >
              {name}
            </h3>
            <div className="text-right">
              <div className="font-bold text-lg">
                {price} {currency}
              </div>
              <div className="text-sm text-gray-500">
                {t("tutorcard.min_lesson", { value: `45` })}
              </div>
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-2">
            <div className="flex items-center mb-1">
              <FaGraduationCap size={16} className="mr-2" />
              {subject} - {lessons} {t("tutorcard.lessons")}
            </div>
            <div className="flex items-center mb-1">
              <FaUsers size={16} className="mr-2" />
              {students} {t("tutorcard.students")} - {activeStudents}{" "}
              {t("tutorcard.active_lessons")}
            </div>
            <div className="text-sm text-gray-500 mb-2">
              <div className="flex items-center mb-1">
                <FaGraduationCap size={16} className="mr-2" />
                {subject} - {lessons} {t("tutorcard.lessons")}
              </div>
              <div className="flex items-center mb-1">
                <FaUsers size={16} className="mr-2" />
                {students} {t("tutorcard.active_lessons")}
              </div>
              <div className="flex items-center">
                <FaGlobe size={16} className="mr-2" />
                {languages}
              </div>
            </div>
            <p className="text-sm text-gray-500 mb-2">{description}</p>
            <Link
              to={`/student/tutors/${id}/info/${tutorId}`}
              className="text-blue-600 text-sm cursor-pointer"
            >
              {t("tutorcard.read_more")}
            </Link>
          </div>
        </div>
      </div>
      <div className="flex justify-end mt-4 space-x-2 ">
        <LearnosoButton
          title={t("tutorcard.trail_lesson")}
          icon={<FaCalendarAlt />}
          width="w-fit"
          animated
          action={() => {
            if (!user) {
              toast("You must be logged in to book a lesson.", { delay: 3000 });
              return;
            }
            navigate(`/student/tutors/${id}/info/${tutorId}`);
          }}
        />
        <LearnosoButton
          icon={<FaMessage />}
          title={t("tutorcard.send_message")}
          width="w-fit"
          action={() =>
            toast("Feature in progress", { type: "info", delay: 3000 })
          }
          animated
          to=""
          variant="orange"
        />
      </div>
    </motion.div>
  );
};
