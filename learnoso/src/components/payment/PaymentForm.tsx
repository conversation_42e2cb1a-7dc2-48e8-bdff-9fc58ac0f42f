import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { useToast } from '@/components/ui/Toast';
import { enhancedPaymentService } from '@/app/services/payment/enhanced-payment.service';
import { CreditCard, DollarSign, Wallet } from 'lucide-react';

interface PaymentFormProps {
  onSuccess?: (transactionId: number) => void;
  onError?: (error: string) => void;
  defaultAmount?: number;
  defaultCurrency?: string;
  purpose?: 'topup' | 'lesson_payment' | 'subscription';
  lessonId?: number;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({
  onSuccess,
  onError,
  defaultAmount = 50,
  defaultCurrency = 'USD',
  purpose = 'topup',
  lessonId,
}) => {
  const [amount, setAmount] = useState(defaultAmount);
  const [currency, setCurrency] = useState(defaultCurrency);
  const [paymentMethod, setPaymentMethod] = useState<'stripe' | 'paypal'>('stripe');
  const [loading, setLoading] = useState(false);
  const { success, error } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (purpose === 'topup') {
        const response = await enhancedPaymentService.initiateTopup({
          amount,
          currency,
          payment_method: paymentMethod,
          return_url: `${window.location.origin}/payments/success`,
          cancel_url: `${window.location.origin}/payments/failed`,
        });

        if (response.success && response.data) {
          if (paymentMethod === 'stripe' && response.data.client_secret) {
            // Handle Stripe payment
            await handleStripePayment(response.data.client_secret);
          } else if (paymentMethod === 'paypal' && response.data.approval_url) {
            // Redirect to PayPal
            window.location.href = response.data.approval_url;
          }
          
          onSuccess?.(response.data.transaction_id);
          success('Payment initiated successfully');
        }
      } else if (purpose === 'lesson_payment' && lessonId) {
        const response = await enhancedPaymentService.payForLesson(lessonId);
        if (response.success) {
          onSuccess?.(lessonId);
          success('Lesson payment successful');
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment failed';
      error(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleStripePayment = async (clientSecret: string) => {
    // This would integrate with Stripe Elements
    // For now, we'll simulate the payment process
    console.log('Processing Stripe payment with client secret:', clientSecret);
    
    // In a real implementation, you would use Stripe Elements here
    // const stripe = await loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);
    // const { error } = await stripe.confirmCardPayment(clientSecret, {
    //   payment_method: {
    //     card: elements.getElement(CardElement),
    //   }
    // });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wallet className="h-5 w-5" />
          {purpose === 'topup' ? 'Add Funds' : 'Payment'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {purpose === 'topup' && (
            <>
              <div>
                <label className="text-sm font-medium mb-2 block">Amount</label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    min="1"
                    step="0.01"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Currency</label>
                <Select value={currency} onValueChange={setCurrency}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}

          <div>
            <label className="text-sm font-medium mb-2 block">Payment Method</label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                type="button"
                variant={paymentMethod === 'stripe' ? 'default' : 'outline'}
                onClick={() => setPaymentMethod('stripe')}
                className="flex items-center gap-2"
              >
                <CreditCard className="h-4 w-4" />
                Card
              </Button>
              <Button
                type="button"
                variant={paymentMethod === 'paypal' ? 'default' : 'outline'}
                onClick={() => setPaymentMethod('paypal')}
                className="flex items-center gap-2"
              >
                PayPal
              </Button>
            </div>
          </div>

          {paymentMethod === 'stripe' && (
            <div className="space-y-3">
              <Input
                placeholder="Card Number"
                // This would be replaced with Stripe CardElement
              />
              <div className="grid grid-cols-2 gap-2">
                <Input placeholder="MM/YY" />
                <Input placeholder="CVC" />
              </div>
              <Input placeholder="Cardholder Name" />
            </div>
          )}

          <Button
            type="submit"
            className="w-full"
            loading={loading}
            disabled={loading}
          >
            {purpose === 'topup' 
              ? `Add $${amount} ${currency}` 
              : 'Pay Now'
            }
          </Button>
        </form>

        <div className="mt-4 text-xs text-muted-foreground text-center">
          <p>🔒 Your payment information is secure and encrypted</p>
        </div>
      </CardContent>
    </Card>
  );
};
