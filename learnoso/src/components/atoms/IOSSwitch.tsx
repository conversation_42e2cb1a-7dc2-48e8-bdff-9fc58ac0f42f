import Switch, { SwitchProps } from "@mui/material/Switch";
import { styled } from "@mui/material/styles";
import React from "react";

export const IOSSwitch: any = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({ theme }) => ({
  width: 52,
  height: 24,
  padding: 0,
  "& .MuiSwitch-switchBase": {
    padding: 0,
    margin: 2,
    transitionDuration: "200ms",
    "&.Mui-checked": {
      transform: "translateX(28px)",
      color: "#fff",
      "& + .MuiSwitch-track": {
        backgroundColor: "#21409A",
        opacity: 1,
        border: 0,
        // ...theme.applyStyles("dark", {
        //   backgroundColor: "#2ECA45",
        // }),
      },
      "& .MuiSwitch-thumb": {
        color: "#fff !important",
      },
      "&.Mui-disabled + .MuiSwitch-track": {
        opacity: 0.5,
      },
    },
    "&.Mui-focusVisible .MuiSwitch-thumb": {
      color: "#21409A",
      border: "6px solid #fff",
    },
    "&.Mui-disabled .MuiSwitch-thumb": {
      color: theme.palette.grey[100],
      //   ...theme.applyStyles("dark", {
      //     color: theme.palette.grey[600],
      //   }),
    },
    "&.Mui-disabled + .MuiSwitch-track": {
      opacity: 0.7,
      //   ...theme.applyStyles("dark", {
      //     opacity: 0.3,
      //   }),
    },
  },
  "& .MuiSwitch-thumb": {
    boxSizing: "border-box",
    width: 20,
    height: 20,
    color: "#A5A3A8",
  },
  "& .MuiSwitch-track": {
    borderRadius: 24 / 2,
    backgroundColor: "#D5D5D7",
    opacity: 1,
    transition: theme.transitions.create(["background-color"], {
      duration: 300,
    }),
  },
}));
