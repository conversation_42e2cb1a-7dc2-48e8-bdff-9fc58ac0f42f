import { RouteObject } from "react-router-dom";

export type CustomRouteObject = RouteObject & {
  children?: CustomRouteObject[];
};

export type Testimonial = {
  quote: string;
  author: string;
  role: string;
};

export type MessageCardProps = {
  message?: string;
  action?: () => void;
};

export type LearnosoStorageKey =
  | "learnoso-user-auth-token"
  | "learnoso-user-info"
  | "learnoso-user-session-id"
  | "learnoso-user-session-expire-at"
  | "learnoso-sessionId"
  | "learnoso-agora-key"
  | "learnoso-sessionExpiry";

export type StorageItem = {
  value: IUser | string | number;
  expiry: number;
};

export type Language = {
  name: string;
  id: number;
};
