@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
}

@layer base {
  input[type="checkbox"],
  input[type="radio"] {
    @apply accent-dark;
  }

  input::placeholder {
    @apply capitalize text-dark;
  }
  hr {
    @apply border-dark/10 my-4;
  }

  .error {
    @apply text-red-500 text-sm;
  }

  .text-2xs {
    @apply text-[10px];
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* src/styles/theme.css */
:root {
  /* Base colors for both themes */
  --transition-speed: 0.2s;
}

/* Dark theme (default) */
.dark-theme {
  --bg-primary: #111827; /* gray-900 */
  --bg-secondary: #1f2937; /* gray-800 */
  --bg-tertiary: #374151; /* gray-700 */
  --text-primary: #f9fafb; /* gray-50 */
  --text-secondary: #d1d5db; /* gray-300 */
  --text-tertiary: #9ca3af; /* gray-400 */
  --accent-primary: #3b82f6; /* blue-500 */
  --accent-secondary: #2563eb; /* blue-600 */
  --danger: #ef4444; /* red-500 */
  --success: #10b981; /* green-500 */
  --warning: #f59e0b; /* amber-500 */
  --info: #3b82f6; /* blue-500 */
  --border-color: #4b5563; /* gray-600 */
}

/* Light theme */
.light-theme {
  --bg-primary: #f9fafb; /* gray-50 */
  --bg-secondary: #f3f4f6; /* gray-100 */
  --bg-tertiary: #e5e7eb; /* gray-200 */
  --text-primary: #111827; /* gray-900 */
  --text-secondary: #374151; /* gray-700 */
  --text-tertiary: #4b5563; /* gray-600 */
  --accent-primary: #2563eb; /* blue-600 */
  --accent-secondary: #1d4ed8; /* blue-700 */
  --danger: #dc2626; /* red-600 */
  --success: #059669; /* green-600 */
  --warning: #d97706; /* amber-600 */
  --info: #2563eb; /* blue-600 */
  --border-color: #d1d5db; /* gray-300 */
}

/* Apply the variables to common elements */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

button,
input,
select,
textarea {
  transition: all var(--transition-speed) ease;
}

/* Animation for view transitions */
.view-transition {
  transition: all 0.3s ease;
}

/* Animation for mute indicator */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}
