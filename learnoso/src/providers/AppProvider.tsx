import { store } from "@/app/store";
import { AppRouter } from "@/router/Router";
import { ToastProvider } from "@/components/ui/Toast";

import React from "react";
import { Provider } from "react-redux";
import { ToastContainer } from "react-toastify";

export const AppProvider = () => {
  return (
    <Provider store={store}>
      <ToastProvider>
        <div className="min-h-screen bg-background font-sans antialiased">
          <AppRouter />
        </div>
        <ToastContainer />
      </ToastProvider>
    </Provider>
  );
};
