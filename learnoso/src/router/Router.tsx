import Layout from "@/components/partials/Layout";
import NotFound from "@/components/partials/NotFound";
import { adminRoutes } from "@/features/admin/router/router";
import AuthGuard from "@/features/auth/components/AuthGaurd";
import ForgotPassword from "@/features/auth/pages/ForgotPassword";
import { LoginPage } from "@/features/auth/pages/Login";
import { RegisterPage } from "@/features/auth/pages/Register";
import ResetPassword from "@/features/auth/pages/ResetPassword";
import LiveConference from "@/features/conference/pages/Page";
import { studentRoutes } from "@/features/student/router/router";
import { OnboardStudent } from "@/features/studentOnboarding/pages";
import CoursesDashboard from "@/features/tutor/pages/Courses";
import { tutorRoutes } from "@/features/tutor/router/router";
import OnboardTutor from "@/features/tutorOnboarding/pages/OnboardTutor";
import HelpAndSupport from "@/pages/faqs/Page";
import HowItWorks from "@/pages/HowItWorks/Page";
import SelectOnboardType from "@/pages/onboard/Page";
import PaymentFailure from "@/pages/payment/PaymentFailure";
import PaymentSuccess from "@/pages/payment/PaymentSuccess";
import TutorsPage from "@/pages/tutors/Page";
import { LocalizationProvider } from "@mui/x-date-pickers-v6";
import { AdapterDayjs } from "@mui/x-date-pickers-v6/AdapterDayjs";
import React from "react";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { Home } from "../pages";

export const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    errorElement: <NotFound />,
    children: [
      {
        path: "/",
        index: true,
        element: <Home />,
        errorElement: <NotFound />,
      },
      {
        path: "courses/public",
        element: <CoursesDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "forgot-password",
        element: <ForgotPassword />,
        errorElement: <NotFound />,
      },

      {
        path: "reset-password",
        element: <ResetPassword />,
        errorElement: <NotFound />,
      },
      {
        path: "how-it-works",
        element: <HowItWorks />,
        errorElement: <NotFound />,
      },
      {
        path: "help-support",
        element: <HelpAndSupport />,
        errorElement: <NotFound />,
      },
      {
        path: "tutors",
        element: <TutorsPage />,
        errorElement: <NotFound />,
      },
      {
        path: "login",
        element: <LoginPage />,
        errorElement: <NotFound />,
      },
      {
        path: "register",
        element: <RegisterPage />,
        errorElement: <NotFound />,
      },
    ],
  },

  ...tutorRoutes,
  ...studentRoutes,
  ...adminRoutes,
  {
    path: "*",
    element: <NotFound />,
    errorElement: <NotFound />,
  },
  {
    path: "onboard/tutor",
    element: <OnboardTutor />,
  },
  {
    path: "onboard/student",
    element: <OnboardStudent />,
  },
  {
    path: "onboard",
    element: <SelectOnboardType />,
  },
  {
    path: "payments/success",
    element: <PaymentSuccess />,
  },
  {
    path: "payments/failed",
    element: <PaymentFailure />,
  },
  {
    path: "conference/live",
    element: (
      <AuthGuard>
        <LiveConference />
      </AuthGuard>
    ),
  },
]);

export const AppRouter: React.FC = () => (
  <LocalizationProvider dateAdapter={AdapterDayjs}>
    <RouterProvider router={router} />
  </LocalizationProvider>
);
