import React, { ReactNode } from "react";

interface StatCardProps {
  title: string;
  value: string;
  change?: number;
  icon: ReactNode;
  bgColor: string;
  hideChange?: boolean;
}

export default function StatCard({
  title,
  value,
  change = 0,
  icon,
  bgColor,
  hideChange = false,
}: StatCardProps) {
  const isPositive = change > 0;

  return (
    <div
      className={`${bgColor} rounded-lg shadow-sm p-4 relative overflow-hidden`}
    >
      <div className="flex justify-between items-start">
        <div>
          <p
            className={`text-sm ${bgColor === "bg-orange-500" ? "text-white/80" : "text-gray-500"}`}
          >
            {title}
          </p>
          <h3
            className={`text-2xl font-bold mt-1 ${bgColor === "bg-orange-500" ? "text-white" : "text-black"}`}
          >
            {value}
          </h3>
        </div>

        <div
          className={`rounded-full p-2 ${bgColor === "bg-orange-500" ? "bg-white/20" : "bg-light-gray"}`}
        >
          {icon}
        </div>
      </div>
    </div>
  );
}
