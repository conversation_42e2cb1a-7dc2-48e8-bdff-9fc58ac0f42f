import { useGetAllTransactionsQuery } from "@/app/services/admin";
import dayjs from "dayjs";
import {
  ArrowDown,
  ArrowUp,
  ChevronLeft,
  ChevronRight,
  Eye,
  Search,
  Trash2,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export default function TransactionsTable() {
  const { t } = useTranslation();
  const { data, isLoading, isError } = useGetAllTransactionsQuery("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [selectedType, setSelectedType] = useState("All");
  const [selectedAmount, setSelectedAmount] = useState("All");
  const [selectedStatus, setSelectedStatus] = useState("All");
  const [selectedDate, setSelectedDate] = useState("All");
  const itemsPerPage = 10;

  // Define filter options based on actual data
  const types = ["All", "deposit", "withdraw"];
  const amounts = ["All", "$0", "$1-$10", "$11-$20", "$21+"];
  const statuses = ["All", "confirmed", "pending", "failed"];
  const dateRanges = ["All", "Last 7 days", "Last 30 days", "Last 90 days"];

  useEffect(() => {
    if (data?.data) {
      let filtered = [...data.data];

      // Filter by type
      if (selectedType !== "All") {
        filtered = filtered.filter(
          (transaction) => transaction.type === selectedType,
        );
      }

      // Filter by status
      if (selectedStatus !== "All") {
        filtered = filtered.filter(
          (transaction) => transaction.status === selectedStatus,
        );
      }

      // Filter by amount
      if (selectedAmount !== "All") {
        if (selectedAmount === "$0") {
          filtered = filtered.filter(
            (transaction) => parseFloat(transaction.amount) === 0,
          );
        } else if (selectedAmount === "$1-$10") {
          filtered = filtered.filter((transaction) => {
            const amount = Math.abs(parseFloat(transaction.amount));
            return amount > 0 && amount <= 10;
          });
        } else if (selectedAmount === "$11-$20") {
          filtered = filtered.filter((transaction) => {
            const amount = Math.abs(parseFloat(transaction.amount));
            return amount > 10 && amount <= 20;
          });
        } else if (selectedAmount === "$21+") {
          filtered = filtered.filter(
            (transaction) => Math.abs(parseFloat(transaction.amount)) > 20,
          );
        }
      }

      // Filter by date range
      if (selectedDate !== "All") {
        const now = dayjs();
        let startDate: any = "";
        if (selectedDate === "Last 7 days") {
          startDate = now.subtract(7, "day");
        } else if (selectedDate === "Last 30 days") {
          startDate = now.subtract(30, "day");
        } else if (selectedDate === "Last 90 days") {
          startDate = now.subtract(90, "day");
        }

        filtered = filtered.filter((transaction) => {
          const transactionDate = dayjs(transaction.created_at);
          return transactionDate.isAfter(startDate);
        });
      }

      // Search functionality
      if (searchTerm) {
        filtered = filtered.filter(
          (transaction) =>
            transaction.id.toString().includes(searchTerm) ||
            transaction.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
            transaction.amount.includes(searchTerm),
        );
      }

      setFilteredTransactions(filtered as never[]);
    }
  }, [
    data,
    selectedType,
    selectedAmount,
    selectedStatus,
    selectedDate,
    searchTerm,
  ]);

  // Calculate pagination
  const totalPages = Math.ceil(
    (filteredTransactions?.length || 0) / itemsPerPage,
  );
  const currentData = filteredTransactions?.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "confirmed":
        return "bg-green-500/20 text-green-500";
      case "pending":
        return "bg-orange-500 text-orange-500";
      case "failed":
        return "bg-red-500/20 text-red-500";
      default:
        return "bg-gray/20 text-gray-500";
    }
  };

  const getAmountColor = (amount: number) => {
    const value = amount;
    if (value < 0) return "text-red-500";
    if (value > 0) return "text-green-500";
    return "text-gray-500";
  };

  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).format("DD/MM/YYYY hh:mm A");
    } catch (e) {
      return dateString;
    }
  };

  if (isLoading)
    return (
      <div className="text-center py-8">
        {t("admin.transaction.transactions.loading")}
      </div>
    );
  if (isError)
    return (
      <div className="text-center py-8 text-red-500">
        {t("admin.transaction.transactions.error_loading_transactions")}
      </div>
    );

  return (
    <div>
      <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
        <div className="flex flex-wrap gap-2">
          <div className="text-sm text-gray-500 flex items-center">
            {t("admin.transaction.transactions.filter_by")}
          </div>

          {/* Type Filter */}
          <div className="relative">
            <select
              className="border border-gray-200 rounded px-3 py-1.5 text-sm pr-8 appearance-none bg-white"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
            >
              {types.map((type) => (
                <option key={type} value={type}>
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </option>
              ))}
            </select>
            <ChevronRight className="h-4 w-4 absolute right-2 top-1/2 transform -translate-y-1/2 -rotate-90 text-slate-600 pointer-events-none" />
          </div>

          {/* Amount Filter */}
          <div className="relative">
            <select
              className="border border-gray-200 rounded px-3 py-1.5 text-sm pr-8 appearance-none bg-white"
              value={selectedAmount}
              onChange={(e) => setSelectedAmount(e.target.value)}
            >
              {amounts.map((amount) => (
                <option key={amount} value={amount}>
                  {amount}
                </option>
              ))}
            </select>
            <ChevronRight className="h-4 w-4 absolute right-2 top-1/2 transform -translate-y-1/2 -rotate-90 text-slate-600 pointer-events-none" />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <select
              className="border border-gray-200 rounded px-3 py-1.5 text-sm pr-8 appearance-none bg-white"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              {statuses.map((status) => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </option>
              ))}
            </select>
            <ChevronRight className="h-4 w-4 absolute right-2 top-1/2 transform -translate-y-1/2 -rotate-90 text-slate-600 pointer-events-none" />
          </div>

          {/* Date Range Filter */}
          <div className="relative">
            <select
              className="border border-gray-200 rounded px-3 py-1.5 text-sm pr-8 appearance-none bg-white"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            >
              {dateRanges.map((range) => (
                <option key={range} value={range}>
                  {range}
                </option>
              ))}
            </select>
            <ChevronRight className="h-4 w-4 absolute right-2 top-1/2 transform -translate-y-1/2 -rotate-90 text-slate-600 pointer-events-none" />
          </div>
        </div>

        <div className="relative">
          <input
            type="text"
            placeholder="Search transactions..."
            className="border border-gray-200 rounded pl-9 pr-3 py-1.5 text-sm w-full md:w-auto"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-600" />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full min-w-[800px] border-collapse">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.id")}
              </th>
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.type")}
              </th>
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.amount")}
              </th>
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.date")}
              </th>
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.status")}
              </th>
              <th className="text-left py-3 px-4 font-medium text-sm">
                {t("admin.transaction.transactions.actions")}
              </th>
            </tr>
          </thead>
          <tbody>
            {currentData && currentData.length > 0 ? (
              currentData.map((transaction: any) => (
                <tr
                  key={transaction.id}
                  className="border-b border-slate-300 hover:bg-slate/20"
                >
                  <td className="py-3 px-4 text-sm">{transaction.id}</td>
                  <td className="py-3 px-4 text-sm capitalize">
                    {transaction.type === "deposit" ? (
                      <span className="flex items-center gap-1">
                        <ArrowDown className="h-4 w-4 text-green-500" />
                        {transaction.type}
                      </span>
                    ) : (
                      <span className="flex items-center gap-1">
                        <ArrowUp className="h-4 w-4 text-red-500" />
                        {transaction.type}
                      </span>
                    )}
                  </td>
                  <td
                    className={`py-3 px-4 text-sm font-medium ${getAmountColor(transaction.amount)}`}
                  >
                    ${Math.abs(parseFloat(transaction.amount)).toFixed(2)}
                  </td>
                  <td className="py-3 px-4 text-sm">
                    {formatDate(transaction.created_at)}
                  </td>
                  <td className="py-3 px-4 text-sm">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${getStatusColor(transaction.status)}`}
                    >
                      {transaction.status}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm">
                    <div className="flex gap-2">
                      <button
                        className="text-gray-500 hover:text-blue"
                        title={t("admin.transaction.transactions.view_details")}
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button
                        className="text-gray-500 hover:text-red-500"
                        title={t(
                          "admin.transaction.transactions.delete_transaction",
                        )}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="py-8 text-center text-gray-500">
                  {t("admin.transaction.transactions.no_transactions")}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredTransactions && filteredTransactions.length > 0 && (
        <div className="flex justify-between items-center mt-4 text-sm">
          <div className="text-gray-500">
            {t("admin.transaction.transactions.showing")}{" "}
            {(currentPage - 1) * itemsPerPage + 1} to{" "}
            {Math.min(currentPage * itemsPerPage, filteredTransactions.length)}{" "}
            {t("admin.transaction.transactions.of")}{" "}
            {filteredTransactions.length}
          </div>
          <div className="flex items-center gap-2">
            <button
              className="w-8 h-8 flex items-center justify-center rounded border border-slate-200 disabled:opacity-50"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
              // Show pages around current page
              let pageToShow;
              if (totalPages <= 5) {
                pageToShow = i + 1;
              } else if (currentPage <= 3) {
                pageToShow = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageToShow = totalPages - 4 + i;
              } else {
                pageToShow = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageToShow}
                  className={`w-8 h-8 flex items-center justify-center rounded ${
                    currentPage === pageToShow
                      ? "bg-blue text-white"
                      : "border border-gray-200"
                  }`}
                  onClick={() => setCurrentPage(pageToShow)}
                >
                  {pageToShow}
                </button>
              );
            })}

            <button
              className="w-8 h-8 flex items-center justify-center rounded border border-gray-200 disabled:opacity-50"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
