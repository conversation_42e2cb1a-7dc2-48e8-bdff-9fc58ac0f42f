import React from "react";
import { useTranslation } from "react-i18next";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  <PERSON>Axis,
  <PERSON>Axi<PERSON>,
} from "recharts";
const data: any[] = [
  // { name: "<PERSON>", income: 10, expenses: 15 },
  // { name: "Feb", income: 15, expenses: 20 },
  // { name: "<PERSON>", income: 20, expenses: 15 },
  // { name: "Apr", income: 30, expenses: 25 },
  // { name: "May", income: 40, expenses: 30 },
  // { name: "<PERSON>", income: 60, expenses: 40 },
  // { name: "Jul", income: 50, expenses: 45 },
  // { name: "Aug", income: 70, expenses: 50 },
  // { name: "Sep", income: 85, expenses: 60 },
  // { name: "Oct", income: 90, expenses: 65 },
  // { name: "Nov", income: 95, expenses: 70 },
  // { name: "Dec", income: 100, expenses: 75 },
];

export default function RevenueChart() {
  const { t } = useTranslation();
  return (
    <div className="h-[250px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        {data?.length == 0 ? (
          <p className="text-gray-500 grid place-items-center h-full">
            {t("admin.transaction.revenue.no_data")}
          </p>
        ) : (
          <LineChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke="#f0f0f0"
            />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} />
            <Tooltip />
            <Legend iconType="circle" />
            <Line
              type="monotone"
              dataKey="income"
              stroke="#21409A"
              activeDot={{ r: 8 }}
              strokeWidth={2}
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="expenses"
              stroke="#F15A29"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
