import { useGetDailyTransactionsQuery } from "@/app/services/admin";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  <PERSON>A<PERSON><PERSON>,
} from "recharts";

const data: any[] = [
  // { name: "Jan", balance: 20, payout: 10 },
  // { name: "Feb", balance: 18, payout: 12 },
  // { name: "<PERSON>", balance: 22, payout: 15 },
  // { name: "Apr", balance: 15, payout: 18 },
  // { name: "May", balance: 25, payout: 12 },
  // { name: "<PERSON>", balance: 22, payout: 10 },
  // { name: "<PERSON>", balance: 28, payout: 15 },
  // { name: "Aug", balance: 24, payout: 12 },
  // { name: "Sep", balance: 26, payout: 10 },
  // { name: "Oct", balance: 22, payout: 15 },
  // { name: "Nov", balance: 24, payout: 12 },
  // { name: "Dec", balance: 20, payout: 10 },
];

export default function EarningsChart() {
  const { data: transactions } = useGetDailyTransactionsQuery("");
  console.log(transactions);
  const { t } = useTranslation();
  return (
    <div className="h-[250px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        {data?.length == 0 ? (
          <p className="text-gray-500 grid place-items-center h-full">
            {t("admin.transaction.earningchart.no_data")}
          </p>
        ) : (
          <BarChart
            data={data}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            barGap={8}
          >
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke="#f0f0f0"
            />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} />
            <Tooltip />
            <Legend iconType="circle" />
            <Bar
              dataKey="balance"
              fill="#21409A"
              radius={[4, 4, 0, 0]}
              barSize={12}
            />
            <Bar
              dataKey="payout"
              fill="#FF7F00"
              radius={[4, 4, 0, 0]}
              barSize={12}
            />
          </BarChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
