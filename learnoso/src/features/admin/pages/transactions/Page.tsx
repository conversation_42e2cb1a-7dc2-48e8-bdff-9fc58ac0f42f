import { useGetFinanceSummaryQuery } from "@/app/services/admin";
import { Spinner } from "@/components/atoms";
import { useAuth } from "@/hooks";
import { CreditCard, DollarSign, PieChart, Wallet } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import EarningsChart from "./components/EarningsChart";
import RevenueChart from "./components/RevenueChart";
import StatCard from "./components/StatCard";
import TransactionsTable from "./components/Transactions";

export default function TransctionDashboard() {
  const [revenueYear, setRevenueYear] = useState("2024");
  const [earningsYear, setEarningsYear] = useState("2024");
  const { user } = useAuth();
  const { t } = useTranslation();

  const { data: summary, isLoading: isSummaryLoading } =
    useGetFinanceSummaryQuery("get summary");
  return (
    <div className="min-h-screen bg-[#F0F2F9] p-6">
      <div className="max-w-7xl mx-auto">
        <header className="mb-6">
          <h1 className="text-2xl font-medium">
            {t("admin.transaction.welcome")} ,{" "}
            <span className="text-primary font-bold">{user?.first_name}</span> !
          </h1>
          <p className="text-gray-500 text-sm">
            {t("admin.transaction.your_application")}
          </p>
        </header>

        {isSummaryLoading ? (
          <div className="flex justify-center w-full">
            <Spinner size="small" variant="primary" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              title={t("admin.transaction.total_revenue")}
              value={`$${summary?.data?.total_revenue?.toLocaleString()}`}
              change={20}
              icon={<DollarSign className="h-6 w-6 text-white" />}
              bgColor="bg-orange"
            />
            <StatCard
              title={t("admin.transaction.total_transfer")}
              value={`$${summary?.data?.total_transfers?.toLocaleString()}`}
              change={30}
              icon={<CreditCard className="h-6 w-6 text-primary" />}
              bgColor="bg-white"
            />
            <StatCard
              title={t("admin.transaction.expenses")}
              value={`$${summary?.data?.total_expenses?.toLocaleString()}`}
              change={-30}
              icon={<PieChart className="h-6 w-6 text-primary" />}
              bgColor="bg-white"
            />
            <StatCard
              title={t("admin.transaction.balance")}
              value={`$${summary?.data?.current_system_balance?.toLocaleString()}`}
              change={0}
              icon={<Wallet className="h-6 w-6 text-primary" />}
              bgColor="bg-white"
            />
          </div>
        )}
        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="font-medium">{t("admin.transaction.revenues")}</h2>
              <select
                className="border border-light-gray rounded px-2 py-1 text-sm"
                value={revenueYear}
                onChange={(e) => setRevenueYear(e.target.value)}
              >
                <option value="2024">2024</option>
                <option value="2023">2023</option>
              </select>
            </div>
            <RevenueChart />
          </div>

          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <h2 className="font-medium">
                {t("admin.transaction.total_earning")}
              </h2>
              <select
                className="border border-light-gray rounded px-2 py-1 text-sm"
                value={earningsYear}
                onChange={(e) => setEarningsYear(e.target.value)}
              >
                <option value="2024">2024</option>
                <option value="2023">2023</option>
              </select>
            </div>
            <EarningsChart />
          </div>
        </div>

        {/* Transactions Table */}
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h2 className="font-medium mb-4">
            {t("admin.transaction.recent_transactions")}
          </h2>
          <TransactionsTable />
        </div>
      </div>
    </div>
  );
}
