import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import React from "react";
import UsersHeader from "./components/UsersHeader";
import UsersTable from "./components/UsersTable";
import { useTranslation } from "react-i18next";

function Users() {
  const { t } = useTranslation();
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        <UsersHeader />

        <Tabs defaultValue="active" className="space-y-4">
          <TabsList>
            <TabsTrigger value="active">{t("admin.user.acitve")} </TabsTrigger>
            <TabsTrigger value="disabled">
              {t("admin.user.disabled")}{" "}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            <UsersTable />
          </TabsContent>

          <TabsContent value="disabled">
            <UsersTable />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

export default Users;
