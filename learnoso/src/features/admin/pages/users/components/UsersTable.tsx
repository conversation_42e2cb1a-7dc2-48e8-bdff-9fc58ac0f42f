import { useGetUsersQuery } from "@/app/services/admin";
import { <PERSON><PERSON> } from "@/components/atoms";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowBack, ArrowForward } from "@mui/icons-material";
import { Eye, Pencil, Trash2 } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  timezone: string | null;
  profile_picture: string | null;
  is_student: boolean;
  is_tutor: boolean;
  profile_status: string | null;
  created_at: string;
  updated_at: string;
  tutor_profile?: {
    id: number;
    price: number;
    currency: string;
    profile_status: string;
    profile_rejection_reason?: string;
  };
}

interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  links: Array<{
    url: string | null;
    label: string;
    active: boolean;
  }>;
  path: string;
  per_page: number;
  to: number;
  total: number;
}

const UsersTable: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [filters, setFilters] = useState({
    name: "",
    email: "",
    country: "",
    date: "",
    role: "",
    status: "",
  });
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Build query string from filters and pagination
  const buildQueryString = () => {
    const params = new URLSearchParams();
    params.append("page", currentPage.toString());

    if (filters.name) params.append("name", filters.name);
    if (filters.email) params.append("email", filters.email);
    if (filters.country) params.append("country", filters.country);
    if (filters.date) params.append("date", filters.date);
    if (filters.role) params.append("role", filters.role);
    if (filters.status) params.append("status", filters.status);
    if (searchQuery) params.append("search", searchQuery);

    return params.toString();
  };

  const query = buildQueryString();
  const { data, isLoading, isFetching, error } = useGetUsersQuery(query);

  const users: User[] = data?.data || [];
  const meta: PaginationMeta | undefined = data?.meta;

  const handleFilterChange = (key: keyof typeof filters, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when search changes
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    };
  };

  const getProfileStatusClass = (status: string | null) => {
    switch (status) {
      case "approved":
        return "bg-green-500/10 text-green-500";
      case "pending":
        return "bg-yellow-100 text-yellow-600";
      case "rejected":
        return "bg-orange-500/10 text-orange-500";
      default:
        return "bg-gray/20 text-gray-500";
    }
  };

  const getFullName = (user: User) => {
    return `${user.first_name} ${user.last_name}`;
  };

  const getUserRole = (user: User) => {
    if (user.is_tutor && user.is_student) return "Tutor & Student";
    if (user.is_tutor) return "Tutor";
    if (user.is_student) return "Student";
    return "User";
  };

  const getUserInitials = (user: User) => {
    return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`;
  };
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-wrap gap-2">
          <Select onValueChange={(value) => handleFilterChange("role", value)}>
            <SelectTrigger className="w-[150px]">
              <SelectValue
                placeholder={t("admin.user.usertable.role_placeholder")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t("admin.user.usertable.all_roles")}
              </SelectItem>
              <SelectItem value="tutor">
                {t("admin.user.usertable.tutor")}
              </SelectItem>
              <SelectItem value="student">
                {t("admin.user.usertable.student")}
              </SelectItem>
              <SelectItem value="both">
                {t("admin.user.usertable.tutors_and_students")}
              </SelectItem>
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => handleFilterChange("email", value)}>
            <SelectTrigger className="w-[150px]">
              <SelectValue
                placeholder={t("admin.user.usertable.email_placeholder")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t("admin.user.usertable.all_emails")}{" "}
              </SelectItem>
              <SelectItem value="gmail">
                {t("admin.user.usertable.gmail")}{" "}
              </SelectItem>
              <SelectItem value="yahoo">
                {t("admin.user.usertable.yahoo")}{" "}
              </SelectItem>
              <SelectItem value="example">
                {t("admin.user.usertable.example")}
              </SelectItem>
            </SelectContent>
          </Select>

          <Select
            onValueChange={(value) => handleFilterChange("country", value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue
                placeholder={t("admin.user.usertable.country_placeholder")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t("admin.user.usertable.all_countries")}
              </SelectItem>
              <SelectItem value="Cameroon">
                {t("admin.user.usertable.cameroon")}
              </SelectItem>
              <SelectItem value="Botswana">
                {t("admin.user.usertable.botswana")}
              </SelectItem>
              <SelectItem value="Cmr">
                {t("admin.user.usertable.cmr")}
              </SelectItem>
            </SelectContent>
          </Select>

          <Select
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <SelectTrigger className="w-[150px]">
              <SelectValue
                placeholder={t("admin.user.usertable.status_placeholder")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                {t("admin.user.usertable.all_statuses")}
              </SelectItem>
              <SelectItem value="pending">
                {t("admin.user.usertable.pending")}
              </SelectItem>
              <SelectItem value="approved">
                {t("admin.user.usertable.approved")}
              </SelectItem>
              <SelectItem value="rejected">
                {t("admin.user.usertable.rejected")}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative">
          <Input
            className="pl-8"
            type="search"
            placeholder={t("admin.user.usertable.search_here")}
            value={searchQuery}
            onChange={handleSearchChange}
          />
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <div className="overflow-x-auto">
          {isLoading || isFetching ? (
            <Spinner size="large" />
          ) : error ? (
            <div className="p-8 text-center text-red-500-500">
              {t("admin.user.usertable.error_loading_data")}
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.id")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.name")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.email")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.country")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.date_joined")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.role")}
                  </th>
                  <th className="py-3 px-4 text-left text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.status")}
                  </th>
                  <th className="py-3 px-4 text-center text-sm font-medium text-gray-500">
                    {t("admin.user.usertable.actions")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {users.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="py-6 text-center text-gray-500">
                      {t("admin.user.usertable.no_users_found")}
                    </td>
                  </tr>
                ) : (
                  users.map((user, index) => {
                    const formattedDate = formatDate(user.created_at);
                    const profileStatus = user.is_tutor
                      ? user.profile_status || "N/A"
                      : "N/A";

                    return (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="py-3 px-4">{user.id}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <Avatar className="h-8 w-8 mr-2">
                              <AvatarImage
                                src={user.profile_picture || undefined}
                              />
                              <AvatarFallback>
                                {getUserInitials(user)}
                              </AvatarFallback>
                            </Avatar>
                            <span>{getFullName(user)}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">{user.email}</td>
                        <td className="py-3 px-4">{user.country}</td>
                        <td className="py-3 px-4">
                          <div className="flex flex-col">
                            <span>{formattedDate.date}</span>
                            <span className="text-sm text-gray-500">
                              {formattedDate.time}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">{getUserRole(user)}</td>
                        <td className="py-3 px-4">
                          {user.is_tutor && (
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${getProfileStatusClass(profileStatus)}`}
                            >
                              {profileStatus}
                            </span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex justify-center space-x-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          )}
        </div>

        {meta && (
          <div className="flex items-center justify-between px-4 py-3 border-t">
            <div className="text-sm text-gray-500">
              {t("admin.user.usertable.showing")}
              {meta.from} {t("admin.user.usertable.to")} {meta.to}{" "}
              {t("admin.user.usertable.of")} {meta.total}{" "}
              {t("admin.user.usertable.results")}
            </div>
            <div className="flex gap-1">
              {meta.current_page > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={meta.current_page <= 1}
                  onClick={() => handlePageChange(meta.current_page - 1)}
                >
                  <ArrowBack className="text-gray-500" />
                </Button>
              )}
              {meta.links
                .filter(
                  (link) =>
                    !["&laquo; Previous", "Next &raquo;"].includes(link.label),
                )
                .map((link, index) => (
                  <Button
                    key={index}
                    variant={link.active ? "outline" : "ghost"}
                    size="sm"
                    className={`ml-2 w-8 ${link.active ? "text-primary border-primary" : ""}`}
                    onClick={() => {
                      if (link.url) {
                        const url = new URL(link.url);
                        const page = url.searchParams.get("page");
                        if (page) handlePageChange(parseInt(page));
                      }
                    }}
                    disabled={!link.url}
                  >
                    {link.label}
                  </Button>
                ))}
              {meta.current_page < meta.last_page && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePageChange(meta.current_page + 1)}
                >
                  <ArrowForward className="text-gray-500" />
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UsersTable;
