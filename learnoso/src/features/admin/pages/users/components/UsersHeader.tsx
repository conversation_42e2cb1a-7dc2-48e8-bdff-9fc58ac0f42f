import { useGetUserSummaryQuery } from "@/app/services/admin";
import { Spinner } from "@/components/atoms";
import { Card, CardContent } from "@/components/ui/card";
import { GraduationCap, Shield, User, Users } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";

interface StatCardProps {
  title: string;
  count: number;
  change: number;
  icon: React.ReactNode;
  isIncrease?: boolean;
  isMain?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  count,
  change,
  icon,
  isIncrease = true,
  isMain = false,
}) => (
  <Card className={isMain ? "bg-blue-900 text-white" : ""}>
    <CardContent className="p-6">
      <div className="flex flex-col">
        <div className="flex items-center mb-2">
          <div
            className={`w-8 h-8 rounded-full ${isMain ? "bg-blue-800" : "bg-slate-100"} flex items-center justify-center mr-2`}
          >
            {icon}
          </div>
          <span className="text-sm">{title}</span>
        </div>
        <span className="text-2xl font-bold mb-2">
          {String(count).padStart(2, "0")}
        </span>
      </div>
    </CardContent>
  </Card>
);

const UsersHeader: React.FC = () => {
  const { data, isLoading } = useGetUserSummaryQuery("user summary");
  const { t } = useTranslation();
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-primary">
          {" "}
          {t("admin.user.userheader.users")}
        </h1>
        <p className="text-gray-500">
          {t("admin.user.userheader.your_system")}
        </p>
      </div>
      {isLoading ? (
        <div className="flex justify-center w-full">
          <Spinner size="small" variant="primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <>
            <StatCard
              isMain={true}
              title={t("admin.user.userheader.total_users")}
              count={data?.data?.totalUsers as number}
              change={0}
              icon={<Users className="h-5 w-5 text-white" />}
            />
            <StatCard
              title={t("admin.user.userheader.students")}
              count={data?.data?.studentCount as number}
              change={0}
              icon={<GraduationCap className="h-5 w-5 text-black" />}
            />
            <StatCard
              title={t("admin.user.userheader.tutors")}
              count={data?.data?.tutorCount as number}
              change={0}
              icon={<GraduationCap className="h-5 w-5 text-black" />}
            />
            <StatCard
              title={t("admin.user.userheader.administrators")}
              count={data?.data?.adminCount as number}
              change={0}
              icon={<Shield className="h-5 w-5 text-black" />}
            />
            <StatCard
              title={t("admin.user.userheader.guests")}
              count={data?.data?.guestCounts as number}
              change={0}
              icon={<User className="h-5 w-5 text-black" />}
              isIncrease={false}
            />
          </>
        </div>
      )}
    </div>
  );
};

export default UsersHeader;
