import { useGetUsersQuery } from "@/app/services/admin";
import { Spinner } from "@/components/atoms";
import { Applicant, ApplicationStatus } from "@/data/applications";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import ApplicationsFilter from "../../components/partials/ApplicationsFilter";
import ApplicationsPagination from "../../components/partials/ApplicationsPagination";
import ApplicationsTable from "../../components/partials/ApplicationsTable";

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  profile_status: string | null;
  created_at: string;
  is_student: boolean;
  is_tutor: boolean;
  tutor_profile?: {
    language: string;
    id: number;
    subject: string;
  };
}

interface PaginationMeta {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

interface ApiResponse {
  data: User[];
  meta: PaginationMeta;
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
}

const TutorDashboard: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch all users with pagination
  const { data, isLoading, error, isFetching } = useGetUsersQuery(
    `role=tutor&page=${currentPage}`,
  );
  const apiResponse: ApiResponse | undefined = data;
  const allUsers = apiResponse?.data || [];
  const meta = apiResponse?.meta;

  // Transform User[] to Applicant[]
  const transformToApplicants = (users: User[]): Applicant[] => {
    return users.map((user) => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      country: user.country,
      language: user.tutor_profile?.language || "N/A",
      course: user.tutor_profile?.subject || "N/A",
      date: new Date(user.created_at).toLocaleDateString(),
      status: user.profile_status as ApplicationStatus,
      timeAgo: "", // Calculate or get from API if needed
      avatar: "", // Add avatar logic if needed
    }));
  };

  const applicants = transformToApplicants(allUsers);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const { t } = useTranslation();
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">
        {t("admin.applicationdashboard.all_applications")}{" "}
      </h1>

      {isLoading || isFetching ? (
        <Spinner />
      ) : error ? (
        <div className="text-red-500-500">
          {t("admin.applicationdashboard.error_loading_applications")}{" "}
        </div>
      ) : (
        <>
          {/* ApplicationsFilter is optional since we're not filtering */}
          <ApplicationsFilter
            onFilterChange={() => {}} // No-op since we're not filtering
            onSearch={() => {}} // No-op since we're not filtering
          />

          {/* Display all applications */}
          <ApplicationsTable applicants={applicants} status="all" />

          {/* Pagination controls */}
          {meta && (
            <ApplicationsPagination
              currentPage={currentPage}
              totalPages={meta.last_page}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </>
  );
};

export default TutorDashboard;
