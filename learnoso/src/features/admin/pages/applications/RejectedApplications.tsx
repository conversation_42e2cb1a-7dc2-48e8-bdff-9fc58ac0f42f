import { useGetUsersQuery } from "@/app/services/admin";
import { Spinner } from "@/components/atoms";
import { Applicant, ApplicationStatus } from "@/data/applications";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import ApplicationsFilter from "../../components/partials/ApplicationsFilter";
import ApplicationsPagination from "../../components/partials/ApplicationsPagination";
import ApplicationsTable from "../../components/partials/ApplicationsTable";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  profile_status: string | null;
  created_at: string;
  is_student: boolean;
  is_tutor: boolean;
  tutor_profile?: {
    language: string;
    subject: string;
  };
}

interface PaginationMeta {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

interface ApiResponse {
  data: User[];
  meta: PaginationMeta;
  links: {
    first: string;
    last: string;
    prev: string | null;
    next: string | null;
  };
}

const RejectedApplications: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    name: "",
    country: "",
    search: "",
  });

  // Fetch all users with pagination
  const { data, isLoading, error, isFetching } = useGetUsersQuery(
    `status=rejected&role=tutor&page=${currentPage}`,
  );
  const apiResponse: ApiResponse | undefined = data;
  const allUsers = apiResponse?.data || [];
  const meta = apiResponse?.meta;

  // Filter users on the client side
  const filterUsers = (users: User[]) => {
    return users.filter((user) => {
      // Filter by status (only approved users)
      if (user.profile_status !== "rejected") return false;

      // Apply additional filters
      if (
        filters.name &&
        !`${user.first_name} ${user.last_name}`
          .toLowerCase()
          .includes(filters.name.toLowerCase())
      ) {
        return false;
      }
      if (
        filters.country &&
        !user.country.toLowerCase().includes(filters.country.toLowerCase())
      ) {
        return false;
      }
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (
          !user.first_name.toLowerCase().includes(searchLower) &&
          !user.last_name.toLowerCase().includes(searchLower) &&
          !user.email.toLowerCase().includes(searchLower) &&
          !user.country.toLowerCase().includes(searchLower)
        ) {
          return false;
        }
      }

      return true;
    });
  };

  const filteredUsers = filterUsers(allUsers);

  // Transform User[] to Applicant[]
  const transformToApplicants = (users: User[]): Applicant[] => {
    return users.map((user) => ({
      id: user.id,
      name: `${user.first_name} ${user.last_name}`,
      email: user.email,
      country: user.country,
      language: user.tutor_profile?.language || "N/A",
      course: user.tutor_profile?.subject || "N/A",
      date: new Date(user.created_at).toLocaleDateString(),
      status: user.profile_status as ApplicationStatus,
      timeAgo: "", // Calculate or get from API if needed
      avatar: "", // Add avatar logic if needed
      tutor_profile: user.tutor_profile,
    }));
  };

  const applicants = transformToApplicants(filteredUsers);

  const handleFilterChange = (filterType: string, value: string) => {
    setFilters((prev) => ({ ...prev, [filterType]: value }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const { t } = useTranslation();
  return (
    <>
      <h1 className="text-2xl font-bold mb-6">
        {t("admin.rejectedapplications.rejected_applications")}{" "}
      </h1>

      {isLoading || isFetching ? (
        <Spinner size="large" />
      ) : error ? (
        <div className="text-red-500-500">
          {t("admin.rejectedapplications.error_loading_applications")}
        </div>
      ) : (
        <>
          <ApplicationsFilter
            onFilterChange={handleFilterChange}
            onSearch={(value) => handleFilterChange("search", value)}
          />

          <ApplicationsTable applicants={applicants} status="rejected" />

          {meta && (
            <ApplicationsPagination
              currentPage={currentPage}
              totalPages={meta.last_page}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </>
  );
};

export default RejectedApplications;
