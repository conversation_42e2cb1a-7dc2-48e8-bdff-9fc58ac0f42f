import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const data: any[] = [
  // { month: "Jan", student: 25, tutor: 15 },
  // { month: "Feb", student: 20, tutor: 18 },
  // { month: "Mar", student: 25, tutor: 20 },
  // { month: "Apr", student: 15, tutor: 12 },
  // { month: "May", student: 20, tutor: 15 },
  // { month: "Jun", student: 25, tutor: 15 },
  // { month: "Jul", student: 22, tutor: 18 },
  // { month: "Aug", student: 20, tutor: 15 },
  // { month: "Sep", student: 25, tutor: 20 },
  // { month: "Oct", student: 23, tutor: 16 },
  // { month: "Nov", student: 25, tutor: 18 },
  // { month: "Dec", student: 20, tutor: 15 },
];

const UserEngagementChart: React.FC = () => {
  const { t } = useTranslation();
  return (
    <Card className="col-span-1 lg:col-span-2">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">
          {t("admin.userengagementchart.user_engagement")}
        </CardTitle>
        <Select defaultValue="2024">
          <SelectTrigger className="w-[80px] h-8">
            <SelectValue
              placeholder={t("admin.userengagementchart.year_placeholder")}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="2024">2024</SelectItem>
            <SelectItem value="2023">2023</SelectItem>
            <SelectItem value="2022">2022</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="student"
                name={t("admin.userengagementchart.student")}
                fill="#21409A"
              />
              <Bar
                dataKey="tutor"
                name={t("admin.userengagementchart.tutor")}
                fill="#FF7F00"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserEngagementChart;
