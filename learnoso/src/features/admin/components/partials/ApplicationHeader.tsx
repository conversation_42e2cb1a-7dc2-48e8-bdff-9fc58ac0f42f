import { useGetTutorApplicationSummaryQuery } from "@/app/services/admin";
import { Card, CardContent } from "@/components/ui/card";
import { FileTextIcon } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  FaPersonCircleCheck,
  FaPersonCircleQuestion,
  FaPersonCircleXmark,
} from "react-icons/fa6";
import { Link, useLocation } from "react-router-dom";

const ApplicationsHeader: React.FC = () => {
  const { t } = useTranslation();
  const { pathname } = useLocation();
  const isRejected = pathname.includes("/rejected");
  const isPending = pathname.includes("/pending");
  const isAccepted = pathname.includes("/approved");
  const isBase = pathname.endsWith("/applications");
  const { data, isLoading, isError } = useGetTutorApplicationSummaryQuery(
    "tutor application summary",
  );
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="spinner-border text-primary" role="status">
          <span className="sr-only">
            {t("admin.applicationheader.loading")}{" "}
          </span>
        </div>
      </div>
    );
  }
  if (isError) {
    return <p>{t("admin.applicationheader.error_fetching")}</p>;
  }

  return (
    <div className="mb-6">
      <h1 className="text-2xl font-bold mb-2">
        {t("admin.applicationheader.tutoring")}
      </h1>
      <p className="text-gray-500 mb-6">
        {t("admin.applicationheader.recent_applicants")}
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Link to="" className="block">
          <Card
            className={`${isBase ? "bg-primary hover:bg-primary/85 text-white" : "hover:bg-slate-50"} transition-colors`}
          >
            <CardContent className="p-6">
              <div className="flex flex-col">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2">
                    <FileTextIcon
                      className={`w-8 h-8 ${isBase ? "text-white" : "text-primary"}`}
                    />
                  </div>
                  <span className="text-sm">
                    {" "}
                    {t("admin.applicationheader.total_applications")}
                  </span>
                </div>
                <span className="text-4xl font-bold mb-4">
                  {data?.data?.totalTutors}
                </span>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="pending" className="block">
          <Card
            className={`${isPending ? "bg-primary hover:bg-primary/85 text-white" : "hover:bg-slate-50"} transition-colors`}
          >
            <CardContent className="p-6">
              <div className="flex flex-col">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2">
                    <FaPersonCircleQuestion
                      className={`w-8 h-8 ${isPending ? "text-white" : "text-orange-500"}`}
                    />
                  </div>
                  <span className="text-sm">
                    {" "}
                    {t("admin.applicationheader.pending")}
                  </span>
                </div>
                <span className="text-4xl font-bold mb-4">
                  {data?.data?.pendingTutors}
                </span>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="approved" className="block">
          <Card
            className={`${isAccepted ? "bg-primary hover:bg-primary/85 text-white" : "hover:bg-slate-50"} transition-colors`}
          >
            <CardContent className="p-6">
              <div className="flex flex-col">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2">
                    <FaPersonCircleCheck
                      className={`w-8 h-8 ${isAccepted ? "text-white" : "text-green-500"}`}
                    />
                  </div>
                  <span className="text-sm">
                    {t("admin.applicationheader.approved")}
                  </span>
                </div>
                <span className="text-4xl font-bold mb-4">
                  {data?.data?.verifiedTutors}
                </span>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="rejected" className="block">
          <Card
            className={`${isRejected ? "bg-primary hover:bg-primary/85 text-white" : "hover:bg-slate-50"} transition-colors`}
          >
            <CardContent className="p-6">
              <div className="flex flex-col">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full flex items-center justify-center mr-2">
                    <FaPersonCircleXmark
                      className={`w-8 h-8 ${isRejected ? "text-white" : "text-red-500"}`}
                    />
                  </div>
                  <span className="text-sm">
                    {t("admin.applicationheader.rejected")}
                  </span>
                </div>
                <span className="text-4xl font-bold mb-4">
                  {data?.data?.rejectedTutors}
                </span>
              </div>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
};

export default ApplicationsHeader;
