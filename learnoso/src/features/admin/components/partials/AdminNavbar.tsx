import { useAuth, useLogout } from "@/hooks";
import { AnimatePresence, motion } from "framer-motion";
import {
  Bell,
  ChevronDown,
  FileText,
  Globe,
  Home,
  MessageSquare,
  RefreshCw,
  Users,
} from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaCog, FaQuestionCircle, FaSignOutAlt, FaUser } from "react-icons/fa";
import { Link, useLocation } from "react-router-dom";

interface Dropdown {
  isOpen: boolean;
  onClose: () => void;
}

const UserDropdown: React.FC<Dropdown> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  const { invokeLogout: logout } = useLogout();
  const { user } = useAuth();
  const { t } = useTranslation();

  return (
    <motion.div
      className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl z-20"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
    >
      <div className="px-4 py-3 border-b border-solid border-[#cfd5d8]">
        <div className="flex items-center">
          <img
            src={`https://ui-avatars.com/api/?name=${user?.first_name}+${user?.last_name}&background=random`}
            alt="Profile"
            className="w-10 h-10 rounded-full object-cover mr-3"
          />
          <div>
            <p className="font-semibold">
              {user?.first_name} {user?.last_name}
            </p>
            <p className="text-sm text-gray-500">{user?.email}</p>
          </div>
        </div>
      </div>
      <div className="py-2">
        <Link
          to="/account-settings"
          className="flex items-center px-4 py-2 hover:bg-slate-100"
          onClick={onClose}
        >
          <FaCog className="mr-3 text-gray-500" />
          <span>{t("admin.adminnavbar.accounting_setting")}</span>
        </Link>
        <Link
          to="/profile"
          className="flex items-center px-4 py-2 hover:bg-slate-100"
          onClick={onClose}
        >
          <FaUser className="mr-3 text-gray-500" />
          <span>{t("admin.adminnavbar.profile")}</span>
        </Link>
        <Link
          to="/help-support"
          className="flex items-center px-4 py-2 hover:bg-slate-100"
          onClick={onClose}
        >
          <FaQuestionCircle className="mr-3 text-gray-500" />
          <span>{t("admin.adminnavbar.help_and_support")}</span>
        </Link>
      </div>
      <div className="border-t border-solid border-[#cfd5d8] py-2">
        <button
          onClick={logout}
          className="flex items-center px-4 py-2 text-red-500-500 hover:bg-slate-100 w-full"
        >
          <FaSignOutAlt className="mr-3 text-red-500" />
          <span>{t("admin.adminnavbar.logout")}</span>
        </button>
      </div>
    </motion.div>
  );
};

const Navbar = () => {
  const { pathname } = useLocation();
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { user } = useAuth();
  const { t } = useTranslation();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // @ts-ignore
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsUserDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const menuItems = [
    {
      name: t("admin.adminnavbar.dashboard"),
      path: "/admin/dashboard",
      icon: <Home className="h-5 w-5" />,
    },
    {
      name: t("admin.adminnavbar.users"),
      path: "/admin/users",
      icon: <Users className="h-5 w-5" />,
    },
    {
      name: t("admin.adminnavbar.application"),
      path: "/admin/applications",
      icon: <FileText className="h-5 w-5" />,
    },
    {
      name: t("admin.adminnavbar.transactions"),
      path: "/admin/transactions",
      icon: <RefreshCw className="h-5 w-5" />,
    },
  ];

  return (
    <div className="w-full border-b border-slate-200 bg-white">
      <div className="container mx-auto px-4 lg:px-6">
        <div className="flex justify-between h-16">
          {/* Logo area */}
          <div className="flex items-center flex-shrink-0">
            <Link to="/" className="flex items-center">
              <div className="flex items-center">
                <img src="/ico.svg" alt="Learnoso Logo" className="h-8 w-8" />
                <span className="ml-2 text-xl font-semibold text-primary">
                  {t("common.app-name")}
                </span>
              </div>
            </Link>
          </div>

          {/* Navigation links */}
          <div className="hidden md:flex justify-center flex-1">
            <div className="flex space-x-8">
              {menuItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className={`inline-flex flex-col items-center px-1 pt-1 border-b-2 text-sm font-medium ${
                    pathname.includes(item.path)
                      ? "border-primary text-primary"
                      : "border-transparent text-black hover:text-gray-500 hover:border-gray"
                  }`}
                >
                  <div
                    className={`p-2 rounded-lg ${
                      pathname.includes(item.path) ? "bg-light-gray" : ""
                    }`}
                  >
                    {item.icon}
                  </div>
                  <span>{item.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Right side icons */}
          <div className="flex items-center space-x-5">
            <button className="text-black hover:text-gray-500">
              <Globe className="h-5 w-5" />
            </button>
            <button className="text-black hover:text-gray-500">
              <MessageSquare className="h-5 w-5" />
            </button>
            <button className="relative text-black hover:text-gray-500">
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red"></span>
            </button>

            {/* User profile dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                className="flex items-center focus:outline-none"
                onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
              >
                <div className="h-9 w-9 rounded-full overflow-hidden border border-slate-200">
                  <img
                    src={`https://ui-avatars.com/api/?name=${user?.first_name}+${user?.last_name}&background=random`}
                    alt="Profile"
                    className="w-10 h-10 rounded-full object-cover mr-3"
                  />
                </div>
                <ChevronDown
                  className={`h-4 w-4 ml-1 text-black transition-transform duration-200 ${isUserDropdownOpen ? "rotate-180" : ""}`}
                />
              </button>

              <AnimatePresence>
                {isUserDropdownOpen && (
                  <UserDropdown
                    isOpen={isUserDropdownOpen}
                    onClose={() => setIsUserDropdownOpen(false)}
                  />
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Navbar;
