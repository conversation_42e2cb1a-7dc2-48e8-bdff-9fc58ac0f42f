import Navbar from "@/features/admin/components/partials/AdminNavbar";
import AuthGuard from "@/features/auth/components/AuthGaurd";
import { RoleGuard } from "@/features/auth/components/RoleGuard";
import React, { Outlet } from "react-router-dom";
function AdminLayout() {
  return (
    <AuthGuard>
      <RoleGuard allowedRoles={["admin"]}>
        <div className="min-h-screen bg-gray-100">
          <Navbar />
          <main className="container mx-auto px-4 py-8">
            <Outlet />
          </main>
        </div>
      </RoleGuard>
    </AuthGuard>
  );
}

export default AdminLayout;
