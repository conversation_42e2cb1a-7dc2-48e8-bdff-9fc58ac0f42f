import {
  Pa<PERSON><PERSON>,
  Pa<PERSON>ationContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>psis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import React from "react";
import { useTranslation } from "react-i18next";

interface ApplicationsPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const ApplicationsPagination: React.FC<ApplicationsPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];

    // Always show first page
    pages.push(1);

    // If we have more than 5 pages, we need to handle ellipsis
    if (totalPages > 5) {
      // If current page is 1 or 2, show pages 1-3 + ellipsis + last page
      if (currentPage < 3) {
        pages.push(2, 3);
        pages.push("ellipsis");
        pages.push(totalPages);
      }
      // If current page is close to the last page, show first page + ellipsis + last 3 pages
      else if (currentPage > totalPages - 2) {
        pages.push("ellipsis");
        pages.push(totalPages - 2, totalPages - 1, totalPages);
      }
      // If current page is in the middle, show first page + ellipsis + current-1, current, current+1 + ellipsis + last page
      else {
        pages.push("ellipsis");
        pages.push(currentPage - 1, currentPage, currentPage + 1);
        pages.push("ellipsis");
        pages.push(totalPages);
      }
    } else {
      // If we have 5 or fewer pages, show all pages
      for (let i = 2; i <= totalPages; i++) {
        pages.push(i);
      }
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();
  const { t } = useTranslation();

  return (
    <div className="mt-4 flex w-full items-center justify-between">
      <div className="text-sm text-gray-500 w-full">
        {t("admin.applicationspagination.showing")} {currentPage}{" "}
        {t("admin.applicationspagination.out_of")} {totalPages}
      </div>

      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
              className={
                currentPage === 1 ? "pointer-events-none opacity-50" : ""
              }
            />
          </PaginationItem>

          {pageNumbers.map((page, index) =>
            page === "ellipsis" ? (
              <PaginationItem
                key={`ellipsis-${index}`}
                className="cursor-pointer"
              >
                <PaginationEllipsis />
              </PaginationItem>
            ) : (
              <PaginationItem key={`page-${page}`}>
                <PaginationLink
                  isActive={currentPage === page}
                  onClick={() => onPageChange(page as number)}
                  className={`"cursor-pointer ${currentPage === page ? "text-primary border-primary" : ""}`}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ),
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() =>
                currentPage < totalPages && onPageChange(currentPage + 1)
              }
              className={
                currentPage === totalPages
                  ? "pointer-events-none opacity-50"
                  : ""
              }
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
};

export default ApplicationsPagination;
