import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countries, courses, languages } from "@/data/applications";
import { Search } from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";

interface ApplicationsFilterProps {
  onFilterChange: (filterType: string, value: string) => void;
  onSearch: (value: string) => void;
}

const ApplicationsFilter: React.FC<ApplicationsFilterProps> = ({
  onFilterChange,
  onSearch,
}) => {
  const { t } = useTranslation();
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
      <div className="flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="flex items-center">
          <span className="text-sm font-medium mr-4">
            {t("admin.applicationfilter.filter_by")}
          </span>
          <div className="flex flex-wrap gap-2">
            <Select onValueChange={(value) => onFilterChange("name", value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue
                  placeholder={t("admin.applicationfilter.names_placeholder")}
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("admin.applicationfilter.all_names")}
                </SelectItem>
                {/* <SelectItem value="stephanie">Stephanie Jones</SelectItem>
                <SelectItem value="john">John Smith</SelectItem>
                <SelectItem value="maria">Maria Garcia</SelectItem> */}
              </SelectContent>
            </Select>

            <Select onValueChange={(value) => onFilterChange("course", value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue
                  placeholder={t("admin.applicationfilter.course_placeholder")}
                />
              </SelectTrigger>
              <SelectContent>
                {courses.map((course) => (
                  <SelectItem key={course} value={course.toLowerCase()}>
                    {course}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select onValueChange={(value) => onFilterChange("country", value)}>
              <SelectTrigger className="w-[150px]">
                <SelectValue
                  placeholder={t("admin.applicationfilter.country_placeholder")}
                />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country) => (
                  <SelectItem key={country} value={country.toLowerCase()}>
                    {country}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              onValueChange={(value) => onFilterChange("language", value)}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue
                  placeholder={t(
                    "admin.applicationfilter.language_placeholder",
                  )}
                />
              </SelectTrigger>
              <SelectContent>
                {languages.map((language) => (
                  <SelectItem key={language} value={language.toLowerCase()}>
                    {language}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="relative w-full md:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-600" />
          <Input
            className="pl-10 w-full md:w-[250px]"
            placeholder={t("admin.applicationfilter.search_here")}
            onChange={(e) => onSearch(e.target.value)}
          />
        </div>
      </div>
    </div>
  );
};

export default ApplicationsFilter;
