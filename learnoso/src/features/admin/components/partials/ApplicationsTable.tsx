import {
  useApproveTutorMutation,
  useRejectTutorMutation,
} from "@/app/services/admin";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Applicant } from "@/data/applications";
import { RequestInterceptor } from "@/lib/api/interceptor";
import {
  CheckCircle,
  Eye,
  Loader2,
  RotateCcw,
  Trash2,
  XCircle,
} from "lucide-react";
import React from "react";
import { useTranslation } from "react-i18next";

interface ApplicationsTableProps {
  applicants: Applicant[];
  status: "all" | "pending" | "approved" | "rejected";
}

const ApplicationsTable: React.FC<ApplicationsTableProps> = ({
  applicants,
  status,
}) => {
  const getStatusClass = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-orange-500 text-orange-500";
      case "approved":
        return "bg-green-500/20 text-green-500";
      case "rejected":
        return "bg-red-500/20 text-red-500";
      default:
        return "bg-gray/20 text-gray-500";
    }
  };

  const [rejectTutor, { isLoading: isRejectingTutor }] =
    useRejectTutorMutation();
  const [approveTutor, { isLoading: isApprovingTutor }] =
    useApproveTutorMutation();

  const handleAction = async (
    type: "reject" | "approve" | "revert",
    applicant: Applicant,
  ) => {
    let action;
    switch (type) {
      case "approve":
        action = approveTutor;
        break;
      case "reject":
        action = rejectTutor;
        break;
      default:
        return;
    }

    try {
      console.log(applicant);
      await RequestInterceptor.handleRequest(
        () =>
          action({
            id: applicant?.tutor_profile?.id as number,
            reason: "",
          }).unwrap(),
        {
          showToast: true,
          onSuccess: () => {
            window.location.reload();
          },
        },
      );
    } catch (error) {
      console.error("Failed to apply action", error);
    }
  };

  const renderActions = (applicant: Applicant) => {
    const { t } = useTranslation();
    switch (status) {
      case "pending":
        return (
          <>
            <Button
              onClick={() => handleAction("approve", applicant)}
              size="sm"
              variant="outline"
              disabled={isRejectingTutor || isApprovingTutor}
              className="mr-2 bg-green-50025 hover:bg-green-50040 hover:text-green-500/90 text-green-500"
            >
              {isApprovingTutor ? (
                <Loader2 className="animate-spin text-orange-500" />
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-1" />
                  {t("admin.applicaitontable.approve")}
                </>
              )}
            </Button>
            <Button
              onClick={() => handleAction("reject", applicant)}
              disabled={isRejectingTutor || isApprovingTutor}
              size="sm"
              variant="outline"
              className="bg-red-500/25 text-red-500 hover:bg-red-50045 hover:text-red-500/90"
            >
              {isRejectingTutor ? (
                <Loader2 className="animate-spin text-orange-500" />
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-1" />
                  {t("admin.applicaitontable.reject")}
                </>
              )}
            </Button>
          </>
        );
      case "approved":
        return (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleAction("reject", applicant)}
            disabled={isRejectingTutor || isApprovingTutor}
            className="bg-red-500/25 text-red-500 hover:bg-red-50045 hover:text-red-500/90"
          >
            {isRejectingTutor ? (
              <Loader2 className="animate-spin text-orange-500" />
            ) : (
              <>
                <RotateCcw className="h-4 w-4 mr-1" />
                {t("admin.applicaitontable.revert")}
              </>
            )}
          </Button>
        );
      case "rejected":
        return (
          <>
            <Button
              onClick={() => handleAction("approve", applicant)}
              size="sm"
              variant="outline"
              disabled={isRejectingTutor || isApprovingTutor}
              className="mr-2  bg-green-50025 text-green-500 hover:bg-green-50040 hover:text-green-500/90"
            >
              {isApprovingTutor ? (
                <Loader2 className="animate-spin text-orange-500" />
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-1" />
                  {t("admin.applicaitontable.approve")}
                </>
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleAction("reject", applicant)}
              disabled={isRejectingTutor || isApprovingTutor}
              className="bg-red-500/25 text-red-500 hover:bg-red-500/45 hover:text-red-500/90"
            >
              {isRejectingTutor ? (
                <Loader2 className="animate-spin text-orange-500" />
              ) : (
                <>
                  <RotateCcw className="h-4 w-4 mr-1" />
                  {t("admin.applicaitontable.revert")}
                </>
              )}
            </Button>
          </>
        );
      default:
        return (
          <>
            <button className="p-1 rounded-full hover:bg-gray/20">
              <Eye className="h-5 w-5 text-gray-500" />
            </button>
            <button className="p-1 rounded-full hover:bg-gray/20">
              <Trash2 className="h-5 w-5 text-gray-500" />
            </button>
          </>
        );
    }
  };
  const { t } = useTranslation();
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-slate-50 text-dark text-sm">
            <tr>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.sn")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.name")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.email")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.country")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.language")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.course")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.date")}{" "}
              </th>
              <th className="py-3 px-4 text-left">
                {" "}
                {t("admin.applicaitontable.status")}{" "}
              </th>
              <th className="py-3 px-4 text-center">
                {" "}
                {t("admin.applicaitontable.actions")}{" "}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray/20">
            {applicants.length == 0 ? (
              <tr>
                <td className="py-3 px-4">
                  <span className="text-xs text-gray-500">
                    {t("admin.applicaitontable.no_applicant")}
                  </span>
                </td>
              </tr>
            ) : (
              applicants.map((applicant, index) => (
                <tr
                  key={applicant.id}
                  className="hover:bg-slate-100 cursor-pointer"
                >
                  <td className="py-3 px-4">
                    {String(index + 1).padStart(2, "0")}
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8 mr-2">
                        <AvatarImage
                          src={`https://source.unsplash.com/random/100x100?face=${applicant.id}`}
                          alt={applicant.name}
                        />
                        <AvatarFallback>
                          {applicant.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span>{applicant.name}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">{applicant.email}</td>
                  <td className="py-3 px-4">{applicant.country}</td>
                  <td className="py-3 px-4">{applicant.language}</td>
                  <td className="py-3 px-4">{applicant.course}</td>
                  <td className="py-3 px-4">
                    <div className="flex flex-col">
                      <span>{applicant.date}</span>
                      <span className="text-xs text-gray-500">
                        {applicant.timeAgo}
                      </span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusClass(applicant.status ?? "pending")}`}
                    >
                      {applicant.status ?? "pending"}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex justify-center space-x-2">
                      {renderActions(applicant)}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ApplicationsTable;
