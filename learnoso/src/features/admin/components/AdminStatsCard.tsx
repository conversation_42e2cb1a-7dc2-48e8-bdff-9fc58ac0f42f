import { Card, CardContent } from "@/components/ui/card";
import React from "react";

interface AdminStatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  increase: number;
  className?: string;
}

const AdminStatsCard: React.FC<AdminStatsCardProps> = ({
  title,
  value,
  icon,
  increase,
  className,
}) => {
  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex flex-col">
          <div className="flex items-center mb-2">
            <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-2">
              {icon}
            </div>
            <span className="text-sm">{title}</span>
          </div>
          <span className="text-2xl font-bold mb-2">{value}</span>
          {/* <div className="flex items-center text-xs">
            <span className="flex items-center text-green-500 mr-1">
              <ArrowUp className="h-3 w-3 mr-1" />
              {increase}%
            </span>
            <span className="text-gray-500">Since last 30 Days</span>
          </div> */}
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminStatsCard;
