import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import React from "react";

import { t } from "i18next";

const notifications = [
  {
    id: 1,
    type: t("admin.notification.application"),
    title: t("admin.notification.design"),
    time: "10:24 pm",
    status: t("admin.notification.pending"),
  },
  {
    id: 2,
    type: t("admin.notification.course_completion"),
    title: t("admin.notification.design"),
    time: "10:24 pm",
    user: "<PERSON>",
  },
  {
    id: 3,
    type: t("admin.notification.withdrawal_request"),
    title: "$120.00",
    time: "10:24 pm",
    status: t("admin.notification.completed"),
    charges: "$1",
  },
  {
    id: 4,
    type: t("admin.notification.course_completion"),
    title: t("admin.notification.design"),
    time: "10:24 pm",
    user: "<PERSON>",
  },
  {
    id: 5,
    type: t("admin.notification.withdrawal_request"),
    title: "$120.00",
    time: "10:24 pm",
    status: t("admin.notification.completed"),
    charges: "$1",
  },
];
// const notifications = t("admin.notification.notifications", {
//   returnObjects: true,
// }) as Array<{
//   id: number;
//   title: string;
//   type: string;
//   status: string;
//   time: boolean;
//   user: string;
//   charges: string;
// }>;

const NotificationsPanel: React.FC = () => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">
          {t("admin.notification.notifications")}
          <span className="ml-2 text-xs bg-green-500 text-white px-2 py-0.5 rounded-full">
            16
          </span>
        </CardTitle>
        <button className="text-sm text-blue-600">
          {t("admin.notification.view_all")}{" "}
        </button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {true ? (
            <p className="text-gray-500">
              {t("admin.notification.recent_changes")}
            </p>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className="border-b pb-4 last:border-0"
              >
                <div className="flex justify-between items-start mb-1">
                  <span className="text-sm text-gray-500">
                    {notification.type}
                  </span>
                  <span className="text-xs text-slate-600">
                    {notification.time}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">{notification.title}</span>
                  {notification.status && (
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        notification.status === "Pending"
                          ? "bg-orange-100 text-orange-500"
                          : "bg-green-100 text-green-500"
                      }`}
                    >
                      {notification.status}
                    </span>
                  )}
                </div>
                {notification.user && (
                  <div className="text-sm text-gray-500">
                    {notification.user}
                  </div>
                )}
                {notification.charges && (
                  <div className="text-sm text-gray-500">
                    {t("admin.notification.charges")} {notification.charges}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationsPanel;
