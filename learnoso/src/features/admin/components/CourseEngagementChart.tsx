import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const data: any[] = [
  // { month: "Jan", complete: 30, incomplete: 25 },
  // { month: "Feb", complete: 35, incomplete: 30 },
  // { month: "Mar", complete: 45, incomplete: 35 },
  // { month: "Apr", complete: 50, incomplete: 40 },
  // { month: "May", complete: 65, incomplete: 45 },
  // { month: "Jun", complete: 70, incomplete: 50 },
  // { month: "Jul", complete: 60, incomplete: 45 },
  // { month: "Aug", complete: 75, incomplete: 55 },
  // { month: "Sep", complete: 85, incomplete: 60 },
  // { month: "Oct", complete: 80, incomplete: 65 },
  // { month: "Nov", complete: 90, incomplete: 70 },
  // { month: "Dec", complete: 95, incomplete: 75 },
];

const CourseEngagementChart: React.FC = () => {
  const { t } = useTranslation();
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">
          {t("admin.courseengagementchart.course_engegement")}
        </CardTitle>
        <Select defaultValue="2024">
          <SelectTrigger className="w-[80px] h-8">
            <SelectValue
              placeholder={t("admin.courseengagementchart.year_placeholder")}
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="2024">2024</SelectItem>
            <SelectItem value="2023">2023</SelectItem>
            <SelectItem value="2022">2022</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="complete"
                name="Complete"
                stroke="#21409A"
              />
              <Line
                type="monotone"
                dataKey="incomplete"
                name="Incomplete"
                stroke="#FF7F00"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default CourseEngagementChart;
