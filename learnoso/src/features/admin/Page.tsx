import {
  BookOpen,
  Calendar,
  DollarSignIcon as Dollar,
  GraduationCap,
  Users,
} from "lucide-react";
import CourseEngagementChart from "./components/CourseEngagementChart";
import UserEngagementChart from "./components/UserEngagementChart";

import { useGetDashboardSummaryQuery } from "@/app/services/admin";
import { Spinner } from "@/components/atoms";
import { useAuth } from "@/hooks";
import React from "react";
import { useTranslation } from "react-i18next";
import AdminStatsCard from "./components/AdminStatsCard";
import NotificationsPanel from "./components/Notifications";
import RecentApplications from "./components/RecentApplication";

function AdminDashboard() {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { data, isLoading, status } = useGetDashboardSummaryQuery("dashboard");
  console.log(data);
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto space-y-8">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-primary">
            {t("admin.welcome")} , {user?.first_name}!
          </h1>
          <p className="text-gray-500"> {t("admin.overview")}</p>
        </div>

        {/* Stats Cards */}
        {isLoading ? (
          <Spinner />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <AdminStatsCard
              title={t("admin.total_income")}
              value={`$${data?.data?.total_revenue?.toLocaleString()}`}
              icon={<Dollar className="h-5 w-5 text-white" />}
              increase={30}
              className="bg-blue-900 text-white hover:scale-105 transition-transform cursor-pointer"
            />
            <AdminStatsCard
              title={t("admin.students")}
              value={`${data?.data?.students}`}
              icon={<Users className="h-5 w-5 text-gray-500" />}
              className="hover:scale-105 transition-transform cursor-pointer"
              increase={30}
            />
            <AdminStatsCard
              title={t("admin.tutors")}
              value={`${data?.data?.tutors}`}
              icon={<GraduationCap className="h-5 w-5 text-gray-500" />}
              className="hover:scale-105 transition-transform cursor-pointer"
              increase={30}
            />
            <AdminStatsCard
              title={t("admin.total_courses")}
              value={`${data?.data?.courses}`}
              icon={<BookOpen className="h-5 w-5 text-gray-500" />}
              className="hover:scale-105 transition-transform cursor-pointer"
              increase={30}
            />
            <AdminStatsCard
              title={t("admin.total_sessions")}
              value={`${data?.data?.sessions}`}
              icon={<Calendar className="h-5 w-5 text-gray-500" />}
              className="hover:scale-105 transition-transform cursor-pointer"
              increase={30}
            />
          </div>
        )}
        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <UserEngagementChart />
          <CourseEngagementChart />
        </div>

        {/* Applications and Notifications */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <RecentApplications />
          <NotificationsPanel />
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
