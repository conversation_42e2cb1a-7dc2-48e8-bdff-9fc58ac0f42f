import NotFound from "@/components/partials/NotFound";
import { CustomRouteObject } from "@/types";
import React from "react";
import AdminLayout from "../components/partials/Layout";
import AdminDashboard from "../Page";
import TutorDashboard from "../pages/applications/ApplicationsDashboard";
import ApplicationLayout from "../pages/applications/ApplicationsLayout";
import ApprovedApplications from "../pages/applications/ApprovedApplications";
import PendingApplications from "../pages/applications/PendingApplications";
import RejectedApplications from "../pages/applications/RejectedApplications";
import TransactionDashboard from "../pages/transactions/Page";
import Users from "../pages/users/Page";

export const adminRoutes: CustomRouteObject[] = [
  {
    element: <AdminLayout />,
    path: "/admin",
    errorElement: <NotFound />,
    children: [
      {
        path: "",
        index: true,
        element: <AdminDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "dashboard",
        element: <AdminDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "transactions",
        element: <TransactionDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "users",
        element: <Users />,
        errorElement: <NotFound />,
      },
      {
        path: "applications",
        element: <ApplicationLayout />,
        errorElement: <NotFound />,
        children: [
          {
            index: true,
            element: <TutorDashboard />,
            errorElement: <NotFound />,
          },
          {
            path: "pending",
            element: <PendingApplications />,
            errorElement: <NotFound />,
          },
          {
            path: "approved",
            element: <ApprovedApplications />,
            errorElement: <NotFound />,
          },
          {
            path: "rejected",
            element: <RejectedApplications />,
            errorElement: <NotFound />,
          },
        ],
      },
    ],
  },
];
