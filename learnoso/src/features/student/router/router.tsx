import NotFound from "@/components/partials/NotFound";
import { CustomRouteObject } from "@/types";
import React from "react";
import StudentLayout from "../components/partials/StudentLayout";
import CheckoutTrialLesson from "../pages/CheckoutTrialLesson";
import StudentDashboard from "../pages/StudentDashboard";

import { PastSessions } from "../components/organs/PastSessions";
import { UpcomingSessions } from "../components/organs/UpcomingSessions";
import { StudentSessions } from "../pages/StudentSessions";

import EditProfile from "../pages/EditProfile";
import UserProfile from "../pages/Profile";
import ResourcesPage from "../pages/Resources";
import TutorDetails from "../pages/TutorDetails";
import FundWallet from "../pages/Wallet";
export const studentRoutes: CustomRouteObject[] = [
  {
    element: <StudentLayout />,
    path: "/student",
    errorElement: <NotFound />,
    children: [
      {
        path: "",
        index: true,
        element: <StudentDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "dashboard",
        element: <StudentDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "profile",
        element: <UserProfile />,
        errorElement: <NotFound />,
      },
      {
        path: "resources",
        element: <ResourcesPage />,
        errorElement: <NotFound />,
      },
      {
        path: "account-settings",
        element: <EditProfile />,
        errorElement: <NotFound />,
      },
      {
        path: "sessions",
        element: <StudentSessions />,
        errorElement: <NotFound />,
        children: [
          {
            path: "",
            index: true,
            element: <UpcomingSessions />,
            errorElement: <NotFound />,
          },
          {
            path: "upcoming",
            element: <UpcomingSessions />,
            errorElement: <NotFound />,
          },
          {
            path: "past",
            element: <PastSessions />,
            errorElement: <NotFound />,
          },
        ],
      },
      {
        path: "tutors/:id/info/:tutorId",
        element: <TutorDetails />,
      },
      {
        path: "tutors/:id/checkout-trial-lesson",
        element: <CheckoutTrialLesson />,
      },
      {
        path: "wallet",
        element: <FundWallet />,
      },
    ],
  },
];
