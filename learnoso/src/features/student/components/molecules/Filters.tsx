import { t } from "i18next";
import React from "react";
import { FaClock } from "react-icons/fa";

// Availability filters
interface AvailabilityFilterProps {
  filters: {
    availability: {
      day_of_the_week: string;
      from: string;
      to: string;
    };
  };
  handleAvailabilityChange: (field: string, value: string) => void;
}

export function AvailabilityFilter({
  filters,
  handleAvailabilityChange,
}: AvailabilityFilterProps) {
  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-black ">
        Availability
      </label>
      <div className="space-y-4">
        <select
          value={filters.availability.day_of_the_week}
          onChange={(e) =>
            handleAvailabilityChange("day_of_the_week", e.target.value)
          }
          className="block w-full px-3 py-2 text-sm bg-white border border-slate-500 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">
            {" "}
            {t("student.molecules.filters.day_of_the_week")}
          </option>
          <option value="monday">
            {t("student.molecules.filters.monday")}
          </option>
          <option value="tuesday">
            {" "}
            {t("student.molecules.filters.tuesday")}
          </option>
          <option value="wednesday">
            {" "}
            {t("student.molecules.filters.wednesday")}
          </option>
          <option value="thursday">
            {t("student.molecules.filters.thursday")}
          </option>
          <option value="friday">
            {t("student.molecules.filters.friday")}
          </option>
          <option value="saturday">
            {t("student.molecules.filters.saturday")}
          </option>
          <option value="sunday">
            {t("student.molecules.filters.sunday")}
          </option>
        </select>
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <input
              type="time"
              value={filters.availability.from}
              onChange={(e) => handleAvailabilityChange("from", e.target.value)}
              className="block w-full pl-8 pr-3 py-2 text-sm bg-white border border-slate-500 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <FaClock className="absolute left-2 top-2.5 h-4 w-4 text-slate-600 dark:text-gray-500" />
          </div>
          <span className="text-sm text-gray-500 dark:text-slate-600">to</span>
          <div className="relative flex-1">
            <input
              type="time"
              value={filters.availability.to}
              onChange={(e) => handleAvailabilityChange("to", e.target.value)}
              className="block w-full pl-8 pr-3 py-2 text-sm bg-white border border-slate-400 dark:border-slate-700 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <FaClock className="absolute left-2 top-2.5 h-4 w-4 text-slate-600 dark:text-gray-500" />
          </div>
        </div>
      </div>
    </div>
  );
}
