// Session card file
import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

interface SessionCardProps {
  initial: string;
  tutor: string;
  date: string;
  isJustNow?: boolean;
  title: string;
}

export const SessionCard = ({
  initial,
  tutor,
  date,
  title,
  isJustNow = false,
}: SessionCardProps) => (
  <motion.div
    className="flex items-center bg-slate-100 rounded-lg p-3 mb-2 cursor-pointer"
    initial="hidden"
    animate="visible"
    variants={fadeInUp}
  >
    <div
      className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
        isJustNow ? "bg-green-500" : "bg-pink-500"
      }`}
    >
      {initial}
    </div>
    <div className="ml-3 flex-grow">
      <div className="text-sm font-semibold">{title}</div>
      <div className="text-xs text-gray-500">
        {" "}
        {t("student.molecules.sessioncard.on_the")} {date}
      </div>
    </div>
    {isJustNow && (
      <div className="text-xs text-gray-500">
        {t("student.molecules.sessioncard.just_now")}
      </div>
    )}
  </motion.div>
);
