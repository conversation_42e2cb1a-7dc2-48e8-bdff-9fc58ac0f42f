import { LearnosoButton } from "@/components/atoms";
import { useAuth } from "@/hooks";
import { AESEncrypt, DateWizard, DEFAULT_USER_PROFILE } from "@/lib/util";
import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useEffect } from "react";
import { FaCalendar, FaTv } from "react-icons/fa";
import { FaBarsProgress } from "react-icons/fa6";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { UpcomingSessionCardProps } from "../../@types";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

export const UpcomingSessionCard: React.FC<UpcomingSessionCardProps> = ({
  title,
  image,
  status,
  disabled = false,
  starts_at,
  ends_at,
  id,
  student,
  tutor,
  agora_token,
  channel_name,
  isPassed,
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const constructedQuery = `/conference/live/?title=${title?.replace(/ /g, "-")}&user=${user?.first_name}&lesson-id=${AESEncrypt.encrypt(id)}&tutor_id=${tutor.id}&student_id=${student.id}&starts_at=${starts_at}&ends_at=${ends_at}&status=${status}&agora_token=${AESEncrypt.encrypt(agora_token)}&channel_name=${AESEncrypt.encrypt(channel_name)}`;
  useEffect(() => {
    window.localStorage.setItem("learnoso-agora-token", agora_token);
    window.localStorage.setItem("learnoso-agora-channel", channel_name);
    window.localStorage.setItem("learnoso-lesson-id", `${id}`);
  });

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-4 mb-4"
      initial="hidden"
      animate="visible"
      variants={fadeInUp}
    >
      <div className="flex">
        <motion.img
          src={image ?? DEFAULT_USER_PROFILE}
          alt={title}
          className="w-24 h-24 rounded-lg object-cover mr-4"
          whileHover={{ scale: 1.05 }}
        />
        <div className="flex-grow">
          <div className="flex justify-between items-start">
            <h3 className="text-lg font-semibold">{title}</h3>
            <div className="text-right">
              <div className="font-bold text-lg">
                <span className="text-green-500">{"paid"}</span>
              </div>
              {/* <div className="text-sm text-gray-500">{"20min"} lesson</div> */}
            </div>
          </div>
          <div className="text-sm text-gray-500 mb-2">
            <div className="flex items-center mb-1">
              <FaBarsProgress size={16} className="mr-2" />
              <span className="text-red-500">{status}</span>
            </div>
            <div className="flex items-center">
              <FaCalendar size={16} className="mr-2" />
              {DateWizard.toLocaleDateTime(starts_at) ?? "20 nov 2024"} -{" "}
              {DateWizard.toLocaleDateTime(ends_at) ?? "24:00"}
            </div>
          </div>
          <Link to={""} className="text-gray-500/20 cursor-not-allowed text-sm">
            {t("student.molecules.upcoming_sessions.resources")}
          </Link>
        </div>
      </div>
      <div className="flex justify-end mt-4 space-x-2">
        <LearnosoButton
          title="Join Class"
          icon={<FaTv />}
          width="w-fit"
          animated
          disabled={disabled}
          variant={disabled || isPassed ? "gray" : "primary"}
          type="button"
          action={() => {
            if (isPassed) {
              toast.error(
                "This session has expired. Please Reschedule your session.",
              );
            } else {
              navigate(constructedQuery);
            }
          }}
        />
        <LearnosoButton
          title="Reschedule"
          icon={<FaCalendar />}
          width="w-fit"
          animated
          to=""
          variant="orange"
          action={() => toast.info("Feature coming soon")}
        />
      </div>
    </motion.div>
  );
};
