import { useGetWalletBalanceQuery } from "@/app/services/payment/payment.service";
import { LearnosoButton } from "@/components/atoms";
import { useAuth, useLogout } from "@/hooks";
import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useEffect, useRef, useState } from "react";
import {
  FaArrowRight,
  FaBell,
  FaBook,
  FaCheck,
  FaCircle,
  FaCog,
  FaComments,
  FaDesktop,
  FaEllipsisV,
  FaGlobe,
  FaGraduationCap,
  FaHome,
  FaQuestionCircle,
  FaSignOutAlt,
  FaUser,
  FaUserTie,
  FaWallet,
} from "react-icons/fa";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { Wallet } from "../../@types";

interface NavItemProps {
  icon: JSX.Element;
  text: string;
  path: string;
}

const NavItem = ({ icon, text, path }: NavItemProps) => {
  const { pathname } = useLocation();
  return (
    <Link
      to={path}
      className={`flex flex-col items-center ${
        pathname === path ? "text-blue-600" : "text-gray-500"
      } hover:text-blue-600 cursor-pointer`}
    >
      {icon}
      <span className="text-sm mt-1">{text}</span>
    </Link>
  );
};

// User dropdown component
const UserDropdown = ({ isOpen, onClose }: { isOpen: any; onClose: any }) => {
  if (!isOpen) return null;
  const { invokeLogout: logout } = useLogout();
  const { user } = useAuth();
  return (
    <motion.div
      className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-xl z-20"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
    >
      <div className="px-4 py-3 border-b border-solid border-[#cfd5d8]">
        <div className="flex items-center">
          <img
            src={`https://ui-avatars.com/api/?name=${user!.first_name}+${user!.last_name}&background=random`}
            alt="Profile"
            className="w-10 h-10 rounded-full object-cover mr-3"
          />
          <div>
            <p className="font-semibold">
              {user?.first_name} {user?.last_name}
            </p>
            <p className="text-sm text-gray-500">{user?.email}</p>
          </div>
        </div>
      </div>
      <div className="py-2">
        <Link
          to={`/student/account-settings`}
          className="flex items-center px-4 py-2 hover:bg-slate-100"
        >
          <FaCog className="mr-3 text-gray-500" />
          <span>Account Settings</span>
        </Link>
        {/* <Link to="#" className="flex items-center px-4 py-2 hover:bg-slate-100">
          <FaDollarSign className="mr-3 text-gray-500" />
          <span>Payment Method</span>
        </Link> */}
        {/* <Link to="#" className="flex items-center px-4 py-2 hover:bg-slate-100">
          <FaUserFriends className="mr-3 text-gray-500" />
          <span>Refer a Friend</span>
        </Link> */}
        <Link
          to={`/student/profile`}
          className="flex items-center px-4 py-2 hover:bg-slate-100"
        >
          <FaUser className="mr-3 text-gray-500" />
          <span>Profile</span>
        </Link>
        <Link
          to="/help-support"
          className="flex items-center px-4 py-2 hover:bg-slate-100"
        >
          <FaQuestionCircle className="mr-3 text-gray-500" />
          <span>Help and Support</span>
        </Link>
      </div>
      <div className="border-t border-solid border-[#cfd5d8] py-2">
        <button
          onClick={logout}
          className="flex items-center px-4 py-2 text-red-500-500 hover:bg-slate-100 w-full"
        >
          <FaSignOutAlt className="mr-3 text-red-500" />
          <span>Logout</span>
        </button>
      </div>
    </motion.div>
  );
};

const NotificationItem = ({
  notification,
  onMarkAsRead,
  onViewMore,
}: {
  notification: any;
  onMarkAsRead: any;
  onViewMore: any;
}) => {
  const [showOptions, setShowOptions] = useState(false);

  return (
    <div className="flex items-start p-3 hover:bg-slate-100 relative">
      <img
        src={notification.userIcon}
        alt="User"
        className="w-10 h-10 rounded-full mr-3"
      />
      <div className="flex-grow">
        <p className="font-semibold">{notification.title}</p>
        <p className="text-sm text-gray-500">{notification.description}</p>
      </div>
      <div className="flex items-center">
        {!notification.read && (
          <FaCircle className="text-blue-500 mr-2" size={8} />
        )}
        {!notification.read && (
          <button
            onClick={() => onMarkAsRead(notification.id)}
            className="text-gray-500 hover:text-gray-500-dark mr-2"
          >
            <FaCheck />
          </button>
        )}

        <button
          onClick={() => setShowOptions(!showOptions)}
          className="text-gray-500 hover:text-dark"
        >
          <FaEllipsisV />
        </button>
      </div>
      {showOptions && (
        <motion.div
          className="absolute right-0 mt-8 w-48 bg-white rounded-lg shadow-xl z-30"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          <button
            onClick={() => onViewMore(notification.id, "turnOff")}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100"
          >
            {t(
              "student.partials.studentheader.turn_off_notifications_for_this_type",
            )}
          </button>
          <button
            onClick={() => onViewMore(notification.id, "mute")}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100"
          >
            {t("student.partials.studentheader.muted_for_24hours")}
          </button>
          <button
            onClick={() => onViewMore(notification.id, "settings")}
            className="block w-full text-left px-4 py-2 hover:bg-gray-100"
          >
            {t("student.partials.studentheader.notification_settings")}
          </button>
        </motion.div>
      )}
    </div>
  );
};

type UserNotification = {
  id: number;
  userIcon: string;
  title: string;
  description: string;
  read: boolean;
};

const NotificationDropdown = ({
  isOpen,
  onClose,
}: {
  isOpen: any;
  onClose: any;
}) => {
  const [notifications, setNotifications] = useState<UserNotification[]>([]);

  const handleMarkAsRead = (id: any) => {
    setNotifications(
      notifications.map((notif) =>
        notif.id === id ? { ...notif, read: true } : notif,
      ),
    );
  };

  const handleViewMore = (id: any, action: any) => {
    console.log(`Performing ${action} for notification ${id}`);
    // Implement the logic for each action here
  };

  if (!isOpen) return null;

  return (
    <motion.div
      className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl z-20 notification-dropdown"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
    >
      <div className="px-4 py-3 border-b border-solid border-[#cfd5d8]">
        <h3 className="font-semibold">
          {" "}
          {t("student.partials.studentheader.notifications")}
        </h3>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {notifications?.length == 0 ? (
          <p className="text-gray-500 p-2 px-4">
            {" "}
            {t("student.partials.studentheader.nothing_here")}
          </p>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={handleMarkAsRead}
              onViewMore={handleViewMore}
            />
          ))
        )}
      </div>
      {/* <div className="px-4 py-3 border-t border-solid border-[#cfd5d8] text-center">
        <Link
          to="/all-notifications"
          className="text-blue-500 hover:text-blue-600"
        >
          View all notifications
        </Link>
      </div> */}
    </motion.div>
  );
};

const StudentHeader: React.FC = () => {
  const { pathname } = useLocation();
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] =
    useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();
  const userHasTutorRole = (() => user?.roles.includes("tutor"))();
  const {
    data: walletData,
    isLoading: isLoadingWallet,
    status: walletQueryStatus,
  } = useGetWalletBalanceQuery("get wallet");
  const [wallet, setWallet] = useState<Wallet>();
  useEffect(() => {
    switch (walletQueryStatus) {
      case "fulfilled":
        setWallet(walletData?.data as Wallet);
    }
  }, [walletQueryStatus]);

  const userDropdownRef = useRef<HTMLDivElement | null>(null);
  const notificationDropdownRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userDropdownRef.current &&
        !userDropdownRef.current.contains(event.target as Node)
      ) {
        setIsUserDropdownOpen(false);
      }
      if (
        notificationDropdownRef.current &&
        !notificationDropdownRef.current.contains(event.target as Node)
      ) {
        setIsNotificationDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const comingSoon = () => toast.info("Coming soon...");

  return (
    <header className="bg-white shadow-sm">
      <div className="mx-auto px-28 py-4 flex justify-between items-center">
        <Link to={"/"} className="flex items-center">
          <svg
            width="36"
            height="36"
            viewBox="0 0 36 36"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.9763 16.2472C20.9763 16.4518 20.8978 16.6601 20.7424 16.8167C19.6584 17.8988 10.6257 26.9024 6.36112 31.1524C2.52695 27.9649 0.0678615 23.1899 0 17.8418C0 17.7653 0 17.6905 0 17.614C0 17.5517 0 17.4894 0 17.4271C0.00535748 17.1103 0.0160729 16.7953 0.0339312 16.4821C0.0339312 16.4696 0.0339316 16.459 0.0357174 16.4465C0.039289 16.3824 0.0446465 16.3201 0.0482181 16.2579C0.473245 10.682 3.50379 5.8304 7.92908 2.91164L20.6942 15.6349L20.7353 15.6759C20.8942 15.8343 20.9728 16.0372 20.9728 16.2472H20.9763Z"
              fill="#FF7F00"
            />
            <path
              d="M35.3611 17.614C35.3611 17.7101 35.3611 17.8045 35.3593 17.9006C35.3593 17.9593 35.3575 18.0162 35.3557 18.0732C35.3557 18.1479 35.3522 18.2227 35.3486 18.2974C35.3468 18.3562 35.3432 18.4149 35.3415 18.4736C35.3361 18.5662 35.3325 18.6605 35.3254 18.7548C35.3165 18.8954 35.3057 19.036 35.2932 19.1748C35.2807 19.3297 35.2629 19.4845 35.2468 19.6376C35.2379 19.7088 35.2307 19.7782 35.2218 19.8494C35.2111 19.9277 35.2022 20.0042 35.1915 20.0807C35.1915 20.0861 35.1897 20.0932 35.1897 20.1003C35.17 20.2338 35.1504 20.3673 35.1289 20.5007C33.7467 28.8602 26.4623 35.2369 17.6832 35.2369C14.433 35.2369 11.3881 34.3631 8.77191 32.8396C14.5901 27.0413 21.4227 20.2284 25.3194 16.3433C25.3265 16.3344 25.3265 16.3237 25.3194 16.3148L10.4809 1.52345C12.6811 0.544598 15.1205 0 17.685 0C26.4641 0 33.7467 6.37678 35.1307 14.7362C35.1522 14.8679 35.1718 15.0014 35.1915 15.1349C35.1915 15.1402 35.1915 15.1473 35.1932 15.1544C35.2057 15.2399 35.2165 15.3253 35.2272 15.4107C35.2343 15.4712 35.2415 15.5317 35.2486 15.594C35.2647 15.7471 35.2825 15.9001 35.295 16.0568C35.3075 16.1991 35.3182 16.3415 35.3272 16.4874C35.3325 16.58 35.3379 16.6743 35.3433 16.7686C35.3468 16.8309 35.3486 16.8932 35.3522 16.9555C35.354 17.0196 35.3575 17.0854 35.3575 17.1495C35.3575 17.21 35.3593 17.2705 35.3611 17.3328C35.3611 17.4271 35.3629 17.5233 35.3629 17.6176L35.3611 17.614Z"
              fill="#21409A"
            />
          </svg>
          <span className="ml-2 text-xl text-primary font-bold">learnoso</span>
        </Link>
        <nav>
          <ul className="flex space-x-6">
            <NavItem
              icon={<FaHome size={20} />}
              text="Dashboard"
              path="/student"
            />
            <NavItem
              icon={<FaGraduationCap size={20} />}
              text="My Sessions"
              path="/student/sessions"
            />
            <NavItem
              icon={<FaBook size={20} />}
              text="Resources"
              path="/student/resources"
            />
            <NavItem
              icon={<FaDesktop size={20} />}
              text="Classroom"
              path="/conference/live"
            />
          </ul>
        </nav>
        <div className="flex items-center space-x-4">
          {!userHasTutorRole ? (
            <LearnosoButton
              title="Become a Tutor"
              width="w-fit"
              animated
              icon={<FaGraduationCap />}
              action={() => navigate("/onboard/tutor")}
            />
          ) : (
            <LearnosoButton
              title="Visit Tutor Dashboard"
              width="w-fit"
              animated
              icon={<FaUserTie />}
              action={() => navigate("/tutor")}
            />
          )}
          <div
            className="
    flex items-center justify-between 
    bg-primary/10 
    border border-primary/20 
    p-3 
    rounded-xl 
    shadow-md 
    hover:shadow-lg 
    transition-all 
    duration-300 
    cursor-pointer 
    group
  "
            onClick={() => navigate("/student/wallet")}
          >
            <div className="flex items-center space-x-3">
              <FaWallet
                className="
      text-primary 
      text-2xl 
      group-hover:animate-pulse
    "
              />
              <span
                className="
      text-lg 
      font-bold 
      text-primary 
      group-hover:text-primary/80 
      transition-colors
    "
              >
                {new Number(wallet?.balance).toLocaleString()}{" "}
                {wallet?.currency}
              </span>
            </div>
            <FaArrowRight
              className="
    text-primary/50 
    group-hover:translate-x-1 
    transition-transform
    ml-1
  "
            />
          </div>

          <FaGlobe
            onClick={comingSoon}
            className="h-8 w-8 text-dark cursor-pointer"
          />
          <FaComments
            onClick={comingSoon}
            className="h-8 w-8 text-dark cursor-pointer"
          />
          <div className="relative" ref={notificationDropdownRef}>
            <FaBell
              className="h-8 w-8 text-dark cursor-pointer"
              onClick={() =>
                setIsNotificationDropdownOpen(!isNotificationDropdownOpen)
              }
            />
            <NotificationDropdown
              isOpen={isNotificationDropdownOpen}
              onClose={() => setIsNotificationDropdownOpen(false)}
            />
          </div>
          <div className="relative" ref={userDropdownRef}>
            <img
              src={`https://ui-avatars.com/api/?name=${user!.first_name}+${user!.last_name}&background=random`}
              alt="Profile"
              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
              className="w-9 h-9  cursor-pointer rounded-full object-cover "
            />
            <UserDropdown
              isOpen={isUserDropdownOpen}
              onClose={() => setIsUserDropdownOpen(false)}
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default StudentHeader;
