import AuthGuard from "@/features/auth/components/AuthGaurd";
import { RoleGuard } from "@/features/auth/components/RoleGuard";
import React from "react";
import { Outlet } from "react-router-dom";
import StudentHeader from "./StudentHeader";

function StudentLayout() {
  return (
    <AuthGuard>
      <RoleGuard allowedRoles={["student"]}>
        <StudentHeader />
        <div className="bg-slate-100 min-h-screen container mx-auto px-28 py-8">
          <Outlet />
        </div>
      </RoleGuard>
    </AuthGuard>
  );
}

export default StudentLayout;
