import React from "react";
import { useOutletContext } from "react-router-dom";
import { UpcomingSessionCard } from "../molecules/UpcomingSessionCard";
interface LessonContext {
  upcomingSessions: any[];
  pastSessions: any[];
}
export const PastSessions = () => {
  const { pastSessions } = useOutletContext<LessonContext>();

  return (
    <>
      {pastSessions?.map((lesson: any) => (
        <UpcomingSessionCard {...lesson} isPassed />
      ))}
    </>
  );
};
