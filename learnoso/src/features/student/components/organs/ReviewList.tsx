import { useGetReviewsByTutorIdQuery } from "@/app/services/reviews/review.service";
import dayjs from "dayjs";
import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import ReviewStars from "./ReviewStars";
interface IReview {
  tutor_id: number;
  student_id: number;
  rating: number;
  review: string;
  created_at?: string;
  updated_at?: string;
  student?: {
    user: {
      first_name: string;
      last_name: string;
    };
  };
}

interface IPaginatedResponse {
  current_page: number;
  data: IReview[];
  last_page: number;
  total: number;
}

const ReviewsList: React.FC = () => {
  const [reviews, setReviews] = useState<IReview[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const loaderRef = useRef<HTMLDivElement>(null);
  const { tutorId } = useParams();

  const {
    data: remoteReviews,
    isLoading: isFetchingTutorReviews,
    status: fetchTutorReviewQueryStatus,
  } = useGetReviewsByTutorIdQuery({
    tutorId: +tutorId!,
    page: currentPage,
  });

  useEffect(() => {
    switch (fetchTutorReviewQueryStatus) {
      case "fulfilled":
        const { data: paginatedData } =
          remoteReviews?.data as IPaginatedResponse;

        if (currentPage === 1) {
          setReviews(paginatedData);
        } else {
          setReviews((prev) => [...prev, ...paginatedData]);
        }

        // Check if we've reached the last page
        setHasMore(
          remoteReviews?.data.current_page < remoteReviews?.data.last_page,
        );
    }
  }, [fetchTutorReviewQueryStatus, remoteReviews]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const first = entries[0];
        if (first.isIntersecting && hasMore && !isFetchingTutorReviews) {
          setCurrentPage((prev) => prev + 1);
        }
      },
      { threshold: 1.0 },
    );

    const currentLoader = loaderRef.current;
    if (currentLoader) {
      observer.observe(currentLoader);
    }

    return () => {
      if (currentLoader) {
        observer.unobserve(currentLoader);
      }
    };
  }, [hasMore, isFetchingTutorReviews]);

  if (reviews.length === 0 && !isFetchingTutorReviews) {
    return (
      <div className="bg-white p-4 rounded-lg shadow text-center">
        No reviews yet
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {reviews.map((review, index) => (
        <div
          key={`${review.student_id}-${index}`}
          className="bg-white p-4 rounded-lg shadow"
        >
          <div className="flex justify-between items-start mb-2">
            <div>
              <h4 className="font-semibold">
                {review.student?.user.first_name}{" "}
                {review.student?.user.last_name}
              </h4>
              <ReviewStars rating={review.rating} />
            </div>
            <span className="text-sm text-gray-500">
              {dayjs(review.created_at).format("MMM D, YYYY")}
            </span>
          </div>
          <p className="text-gray-500-700">{review.review}</p>
        </div>
      ))}

      {/* Loader element for intersection observer */}
      <div ref={loaderRef} className="h-10 flex items-center justify-center">
        {isFetchingTutorReviews && (
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
        )}
      </div>
    </div>
  );
};

export default ReviewsList;
