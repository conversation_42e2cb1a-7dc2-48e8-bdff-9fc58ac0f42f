import { motion } from "framer-motion";
import React, { useEffect, useMemo, useState } from "react";

import { useGetAllTutorsQuery } from "@/app/services/tutor/tutor.service";
import { Spinner } from "@/components/atoms";
import { TutorCard } from "@/components/molecules/TutorCard";
import { DEFAULT_USER_PROFILE } from "@/lib/util";
import { t } from "i18next";
import { FaSearch } from "react-icons/fa";
import { ITutorUser } from "../../@types";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.2 } },
};
const childVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },
};

interface TutorsProps {
  language?: string;
  subject?: string;
  cost?: { min: number; max: number };
}

function Tutors({ language, subject, cost }: TutorsProps) {
  const [page, setPage] = useState(1);
  const [tutors, setTutors] = useState<ITutorUser[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [hasMore, setHasMore] = useState(true);

  const { data, isLoading } = useGetAllTutorsQuery(`page=${page}`, {
    skip: !hasMore,
  });

  useEffect(() => {
    if (data?.data) {
      setTutors((prevTutors) => {
        // Prevent duplicate tutors when loading more pages
        const newTutors = data.data?.data.filter(
          (newTutor: ITutorUser) =>
            !prevTutors.some(
              (existingTutor) => existingTutor.id === newTutor.id,
            ),
        );
        return [...prevTutors, ...newTutors];
      });

      // Check if we've reached the last page
      if (data?.data?.current_page >= data?.data?.last_page) {
        setHasMore(false);
      }
    }
  }, [data]);
  // Memoized filtered tutors to improve performance
  const filteredTutors = useMemo(() => {
    // If no filters are applied, return the default data (all tutors)
    if (
      !searchTerm &&
      !language &&
      !subject &&
      (!cost || (cost.min === 0 && cost.max === 0))
    ) {
      return tutors; // No filters, return all tutors
    }

    return tutors.filter((tutor) => {
      const name = `${tutor.first_name} ${tutor.last_name}`.toLowerCase();
      const lessons =
        tutor?.courses?.map((course) => course.name.toLowerCase()).join(" ") ??
        "";
      const searchTermLower = searchTerm?.toLowerCase() || "";

      // 1. Search Term: Takes priority over all other filters.
      if (searchTermLower) {
        return (
          name.includes(searchTermLower) || lessons.includes(searchTermLower)
        );
      }

      // 2. Language Filter
      const matchesLanguage = language
        ? tutor.tutor?.native_language?.toLowerCase() === language.toLowerCase()
        : true;

      // 3. Subject Filter
      const matchesSubject = subject
        ? tutor.courses.some((course) =>
            course.name.toLowerCase().includes(subject.toLowerCase()),
          )
        : true;

      // 4. Cost Filter
      const matchesCost = cost
        ? (tutor.tutor?.price ?? 0) >= cost.min &&
          (tutor.tutor?.price ?? 0) <= cost.max
        : true;

      // Combine all filters
      return matchesLanguage && matchesSubject && matchesCost;
    });
  }, [tutors, searchTerm, language, subject, cost]);

  // Handle Page Change
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > data?.data?.last_page) return;
    setPage(newPage);
    setHasMore(newPage < data?.data?.last_page);
  };

  // Handle Search Input Change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1); // Reset to first page when search is changed
  };

  return (
    <div className="col-span-2">
      <motion.div
        className="bg-primary text-white rounded-lg p-8 mb-8"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-3xl font-bold mb-2">
          {t("student.organs.tutors.find_the_perfect_tutor")}
        </h1>
        <p className="mb-4">
          {t("student.organs.tutors.looking_for_a_personalized_tutor")}
        </p>
        <div className="flex gap-2">
          <motion.input
            type="text"
            placeholder={t("student.organs.tutors.search_for_a_tutor")}
            className="flex-grow rounded-l-lg px-4 py-2 text-gray-500"
            value={searchTerm}
            onChange={handleSearchChange}
            whileFocus={{ scale: 1.02 }}
          />

          <motion.button
            className="bg-white text-primary rounded-r-lg px-6 py-2 font-semibold flex items-center gap-2"
            whileHover={{ scale: 1.05 }}
          >
            <FaSearch />
            {t("student.organs.tutors.search")}
          </motion.button>
        </div>
      </motion.div>

      <h2 className="text-2xl font-semibold mb-4">
        {" "}
        {t("student.organs.tutors.recommended_for_you")}{" "}
      </h2>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-4"
      >
        {filteredTutors.map((tutor) => (
          <motion.div key={tutor.id} variants={childVariants}>
            <TutorCard
              id={tutor.id}
              tutorId={tutor.tutor!.id!}
              name={`${tutor.first_name} ${tutor.last_name}`}
              description={tutor.tutor?.short_description ?? ""}
              image={
                (tutor.tutor?.profile_picture &&
                  !tutor.tutor?.profile_picture.includes("placeholder") &&
                  tutor.tutor?.profile_picture) ||
                DEFAULT_USER_PROFILE
              }
              languages={
                tutor.tutor?.native_language ?? tutor.languages[0] ?? "English"
              }
              lessons={0}
              students={tutor.tutor?.students?.length ?? 0}
              activeStudents={0}
              subject={tutor.courses[0].name}
              price={tutor.tutor?.price ?? 0}
              currency={tutor.tutor?.currency}
            />
          </motion.div>
        ))}

        {isLoading && <Spinner />}
      </motion.div>

      {/* Pagination Controls */}
      {filteredTutors?.length > 0 ? (
        <div className="flex justify-center gap-4 mt-6">
          <button
            onClick={() => handlePageChange(page - 1)}
            disabled={page === 1}
            className="bg-blue-700 text-white py-2 px-4 rounded-lg disabled:opacity-50"
          >
            {t("student.organs.tutors.previous")}
          </button>
          <span className="text-xl font-semibold">
            {t("student.organs.tutors.page")} {page}{" "}
            {t("student.organs.tutors.of")} {data?.data?.last_page}
          </span>
          <button
            onClick={() => handlePageChange(page + 1)}
            disabled={page === data?.data?.last_page}
            className="bg-blue-700 text-white py-2 px-4 rounded-lg disabled:opacity-50"
          >
            {t("student.organs.tutors.next")}
          </button>
        </div>
      ) : (
        <p className="text-gray-500 text-center">
          {" "}
          {t("student.organs.tutors.no_tutors")}{" "}
        </p>
      )}
    </div>
  );
}

export default Tutors;
