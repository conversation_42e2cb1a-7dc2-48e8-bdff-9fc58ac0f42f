import { t } from "i18next";
import React from "react";
import { FaCalendarPlus } from "react-icons/fa";
import { Link, useOutletContext } from "react-router-dom";
import { UpcomingSessionCard } from "../molecules/UpcomingSessionCard";

// Lesson context
interface LessonContext {
  upcomingSessions: any[];
  pastSessions: any[];
}
export const UpcomingSessions: React.FC = () => {
  const { upcomingSessions } = useOutletContext<LessonContext>();

  return (
    <>
      {upcomingSessions?.length > 0 ? (
        upcomingSessions?.map((lesson: any) => (
          <UpcomingSessionCard {...lesson} />
        ))
      ) : (
        <>
          <div className="text-center text-slate-500">
            {" "}
            {t("student.organs.upcoming_sessions.no_upcoming_session")}
          </div>
          <Link
            to={"/student"}
            className="flex gap-2 items-center justify-center mt-4 text-primary"
          >
            <FaCalendarPlus />
            {t("student.organs.upcoming_sessions.book_a_lesson")}
          </Link>
        </>
      )}
    </>
  );
};
