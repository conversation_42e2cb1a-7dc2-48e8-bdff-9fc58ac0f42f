import React from "react";
import { FaStar } from "react-icons/fa";

interface ReviewStarsProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
}

export const ReviewStars: React.FC<ReviewStarsProps> = ({
  rating,
  onRatingChange,
  size = "md",
  disabled = false,
}) => {
  // Define star sizes
  const starSizes = {
    sm: 16,
    md: 20,
    lg: 24,
  };

  // Handle hover state for interactive mode
  const [hoverRating, setHoverRating] = React.useState(0);

  return (
    <div className="flex gap-1 items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <FaStar
          key={star}
          size={starSizes[size]}
          className={`
            ${(hoverRating || rating) >= star ? "text-yellow-400" : "text-gray-500-300"}
            ${!disabled && onRatingChange ? "cursor-pointer transition-colors duration-150" : ""}
            ${disabled ? "opacity-75" : ""}
          `}
          onClick={() => {
            if (!disabled && onRatingChange) {
              onRatingChange(star);
            }
          }}
          onMouseEnter={() => {
            if (!disabled && onRatingChange) {
              setHoverRating(star);
            }
          }}
          onMouseLeave={() => {
            if (!disabled && onRatingChange) {
              setHoverRating(0);
            }
          }}
        />
      ))}
    </div>
  );
};

export default ReviewStars;
