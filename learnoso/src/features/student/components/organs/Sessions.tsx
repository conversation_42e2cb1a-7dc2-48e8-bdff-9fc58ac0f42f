import { useGetStudentLessonsQuery } from "@/app/services/lesson/lesson.service";
import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { SessionCard } from "../molecules/SessionCard";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const childVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 },
  },
};

function Sessions() {
  const { data: lessonsData, isLoading: isFetchingStudentLessons } =
    useGetStudentLessonsQuery("student-lessons");

  // Check if lessons data is being fetched.
  if (isFetchingStudentLessons) {
    return <div>Loading...</div>;
  }

  // If no lessons are available, show a message
  if (!lessonsData || lessonsData.length === 0) {
    return (
      <div> {t("student.organs.sessions.no_upcoming_sessions_found")}</div>
    );
  }

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md p-4"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">
          {t("student.organs.sessions.upcoming_sessions")}
        </h3>
        <a href="#" className="text-blue-600 text-sm">
          {t("students.organs.session.view_all")}
        </a>
      </div>
      <p className="text-sm text-gray-500 mb-4">
        {t("student.organs.sessions.you_can_reschedule")}
      </p>

      {/* Loop over lessonsData and display SessionCard for each lesson */}
      <motion.div variants={childVariants} whileHover={{ scale: 1.05 }}>
        {[...lessonsData.data]?.slice(0, 5).map((lesson: any) => {
          const tutor = `${lesson.tutor.first_name} ${lesson.tutor.last_name}`;
          const sessionDate = new Date(lesson.starts_at).toLocaleString(); // Convert the date to a readable format
          return (
            <SessionCard
              title={lesson.title}
              key={lesson.id}
              initial={lesson.course.name[0]}
              tutor={tutor}
              date={sessionDate}
              isJustNow={lesson.attended === 0}
            />
          );
        })}
      </motion.div>
    </motion.div>
  );
}

export default Sessions;
