export interface ITutorUser {
  id: number;
  first_name: string;
  last_name: string;
  country: string;
  email: string;
  email_verified_at: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  courses: Course[];
  tutor?: Tutor;
  languages: string[];
  educations: Education[];
}

interface Course {
  id: number;
  name: string;
  description: string;
  deleted_at: string | null;
  pivot: {
    user_id: number;
    course_id: number;
  };
}

interface Tutor {
  id: number;
  user_id: number;
  profile_picture: string | null;
  bio: string | null;
  phone_number: string | null;
  city: string | null;
  native_language: string | null;
  rating: number | null;
  price: number;
  availability: Availability[];
  currency: string;
  is_active: boolean;
  short_description: string;
  motivation_to_students: string;
  created_at: string;
  updated_at: string;
  video_url: string;
  timezone: string;
  students: Student[];
}

interface Student {
  id: number;
  user_id: number;
  budget: number;
  courses_of_preference: number[];
  reasons_for_learning: string[];
  timezone: string;
  currency: string;
  prefered_language: string;
  availability_times: string[];
  created_at: string;
  updated_at: string;
  pivot: {
    tutor_id: number;
    student_id: number;
  };
  user: {
    id: number;
    first_name: string;
    last_name: string;
    country: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  };
}

interface Education {
  id: number;
  user_id: number;
  subject: string;
  certificate: string;
  institution: string;
  start_date: string;
  end_date: string;
  description: string;
  url: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

interface Course {
  name: string;
  id: number;
}

interface UpcomingSessionCardProps {
  title: string;
  image: string;
  type: "one-on-one" | "group";
  status: "complete" | "incomplete";
  date: string;
  startTime: string;
  disabled?: boolean;
  starts_at: string;
  ends_at: string;
  id: number;
  tutor: ITutorUser;
  student: Student;
  agora_token: string;
  channel_name: string;
  duration?: any;
  price?: number;
  videoLink?: string;
  tutor?: ITutorUser;
  user?: { first_name: string; last_name: string; email: string; id: number };
  course: Course;
  isPassed?: boolean;
}

export interface Wallet {
  description: string | null;
  balance: string;
  decimal_places: number;
  holder_name: string;
  email: string;
  currency: string;
}
