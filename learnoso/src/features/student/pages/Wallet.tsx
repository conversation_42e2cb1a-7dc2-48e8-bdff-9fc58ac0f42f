import {
  useGetWalletBalanceQuery,
  useInitiatePaymentMutation,
} from "@/app/services/payment/payment.service";
import { useFetchCurrenciesQuery } from "@/app/services/util/util.service";
import { Spinner } from "@/components/atoms";
import { RequestInterceptor } from "@/lib/api/interceptor";
import React, { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  FaCheckCircle,
  FaCreditCard,
  FaMobileAlt,
  FaPaypal,
  FaStripe,
  FaWallet,
} from "react-icons/fa";
import { toast } from "react-toastify";

type PaymentMethodType = "master-card" | "mobile-money";
type PaymentProcessor = "stripe" | "paypal";

const FundWallet: React.FC = () => {
  const [paymentMethod, setPaymentMethod] =
    useState<PaymentMethodType>("master-card");
  const [paymentProcessor, setPaymentProcessor] =
    useState<PaymentProcessor>("stripe");
  const [status, setStatus] = useState<string>("pending");

  const { data: _currencies, isLoading: isFetchingCurrencies } =
    useFetchCurrenciesQuery("fetch-currencies");
  const [currencyKeys, setCurrencyKeys] = useState<string[]>([]);

  useEffect(() => {
    if (_currencies?.data) {
      const keys = _currencies.data.map(
        (currency: Record<string, string>) => Object.keys(currency)[0],
      );
      setCurrencyKeys(keys);
    }
  }, [_currencies]);

  const [initiatePayment, { isLoading: isInitiatingPayment }] =
    useInitiatePaymentMutation();
  const { data: walletData, isLoading: isLoadingWallet } =
    useGetWalletBalanceQuery("get wallet");

  const {
    register: registerCard,
    handleSubmit: handleSubmitCard,
    formState: { errors: errorsCard, isValid: isValidCard },
    watch: watchCard,
    setValue: setValueCard,
    control: controlCard,
  } = useForm({
    defaultValues: {
      "save-card": false,
      "card-number": "",
      cvc: "",
      mm: "",
      yy: "",
      currency: "USD",
      amount: "",
    },
    mode: "onChange",
  });

  const {
    register: registerMomo,
    handleSubmit: handleSubmitMomo,
    formState: { errors: errorsMomo, isValid: isValidMomo },
    control: controlMomo,
  } = useForm({
    defaultValues: {
      "network-provider": "mtn",
      "account-number": "",
      amount: "",
    },
    mode: "onChange",
  });

  const formatCreditCard = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    return parts.length ? parts.join(" ") : value;
  };

  const creditCardValue = watchCard("card-number");

  React.useEffect(() => {
    setValueCard("card-number", formatCreditCard(creditCardValue));
  }, [creditCardValue, setValueCard]);

  const onSubmit = async (data: any) => {
    setStatus("processing");
    if (paymentMethod === "master-card") {
      const paymentInfo = {
        currency: data.currency,
        description: `Fund wallet`,
        payment_method: paymentProcessor,
        total: parseFloat(data.amount),
      };
      try {
        const response = await RequestInterceptor.handleRequest(
          () => initiatePayment(paymentInfo).unwrap(),
          {
            onSuccess: () => {
              setStatus("pending");
            },
            onError: () => {
              setStatus("pending");
            },
          },
          "Fund Wallet",
        );
        if (response.data && response.data.url) {
          window.open(response.data.url, "_blank", "noopener,noreferrer");
        }
      } catch (error) {
        console.error("Payment initiation failed.", error);
        toast.error("Payment initiation failed. Please try again.");
      }
    } else {
      toast.info("Mobile Money not available", { position: "top-right" });
    }
    setStatus("pending");
  };
  const { t } = useTranslation();

  return (
    <div className="min-h-screen bg-light-gray p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden">
        {/* Header Section */}
        <div className="bg-primary text-white p-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">
              {" "}
              {t("student.wallet.fund_your_wallet")}
            </h1>
            <p className="text-sm opacity-80">
              {t("student.wallet.choose_your_preferred_payment_method")}
            </p>
          </div>
          {!isLoadingWallet && (
            <div className="flex items-center space-x-4">
              <FaWallet className="text-3xl" />
              <div>
                <p className="font-semibold text-lg">
                  {walletData?.data?.currency} {walletData?.data?.balance}
                </p>
                <p className="text-xs opacity-70">
                  {" "}
                  {t("student.wallet.current_balance")}{" "}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Payment Method Selector */}
        <div className="flex border-b p-4 bg-light-gray">
          <button
            onClick={() => setPaymentMethod("master-card")}
            className={`
              flex-1 flex items-center justify-center py-3 space-x-2 
              ${
                paymentMethod === "master-card"
                  ? "bg-primary text-white"
                  : "bg-white text-dark hover:bg-light-gray"
              }
              transition-all duration-300 ease-in-out
            `}
          >
            <FaCreditCard />
            <span>Credit Card</span>
          </button>
          <button
            onClick={() => setPaymentMethod("mobile-money")}
            className={`
              flex-1 flex items-center justify-center py-3 space-x-2 
              ${
                paymentMethod === "mobile-money"
                  ? "bg-primary text-white"
                  : "bg-white text-dark hover:bg-light-gray"
              }
              transition-all duration-300 ease-in-out
            `}
          >
            <FaMobileAlt />
            <span>Mobile Money</span>
          </button>
        </div>

        {/* Payment Form */}
        <div className="p-8">
          {paymentMethod === "master-card" ? (
            <form onSubmit={handleSubmitCard(onSubmit)} className="space-y-6">
              {/* Payment Processor Selection */}
              <div className="space-y-2">
                <label className="block text-dark font-semibold mb-3">
                  {t("student.wallet.select_payment_processor")}
                </label>
                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={() => setPaymentProcessor("stripe")}
                    className={`
                      flex-1 flex items-center justify-center p-4 rounded-lg border-2
                      ${
                        paymentProcessor === "stripe"
                          ? "border-primary bg-primary/5"
                          : "border-gray/30"
                      }
                      transition-all duration-300 ease-in-out
                    `}
                  >
                    <FaStripe
                      className={`text-2xl ${paymentProcessor === "stripe" ? "text-primary" : "text-gray-500"}`}
                    />
                    <span
                      className={`ml-2 font-medium ${paymentProcessor === "stripe" ? "text-primary" : "text-gray-500"}`}
                    >
                      {t("student.wallet.pay_with_stripe")}
                    </span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setPaymentProcessor("paypal")}
                    className={`
                      flex-1 flex items-center justify-center p-4 rounded-lg border-2
                      ${
                        paymentProcessor === "paypal"
                          ? "border-primary bg-primary/5"
                          : "border-gray/30"
                      }
                      transition-all duration-300 ease-in-out
                    `}
                  >
                    <FaPaypal
                      className={`text-2xl ${paymentProcessor === "paypal" ? "text-primary" : "text-gray-500"}`}
                    />
                    <span
                      className={`ml-2 font-medium ${paymentProcessor === "paypal" ? "text-primary" : "text-gray-500"}`}
                    >
                      {t("student.wallet.pay_with_paypal")}
                    </span>
                  </button>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-dark font-semibold">
                    {t("student.wallet.currency")}
                  </label>
                  <Controller
                    name="currency"
                    control={controlCard}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                      >
                        {isFetchingCurrencies ? (
                          <option value={"usd"}>
                            {" "}
                            {t("student.wallet.loading_currency")}{" "}
                          </option>
                        ) : (
                          currencyKeys.map((currency) => (
                            <option key={currency} value={currency}>
                              {currency}
                            </option>
                          ))
                        )}
                      </select>
                    )}
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-dark font-semibold">
                    {t("student.wallet.amount")}
                  </label>
                  <input
                    type="number"
                    {...registerCard("amount", {
                      required: t("student.wallet.amount_is_required"),
                    })}
                    placeholder={t("student.wallet.enter_amount")}
                    className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                  />
                  {errorsCard.amount && (
                    <p className="text-red-500 text-sm">
                      {errorsCard.amount.message}
                    </p>
                  )}
                </div>
              </div>

              <button
                type="submit"
                disabled={!isValidCard || isInitiatingPayment}
                className="
                  w-full py-4 bg-green-500 text-white rounded-lg 
                  hover:bg-green-500/90 transition-colors 
                  flex items-center justify-center space-x-2
                  disabled:opacity-50 disabled:cursor-not-allowed
                "
              >
                {isInitiatingPayment ? (
                  <Spinner />
                ) : (
                  <>
                    <FaCheckCircle />
                    <span>
                      {t("student.wallet.pay_with")}{" "}
                      {paymentProcessor === "stripe" ? "Stripe" : "PayPal"}
                    </span>
                  </>
                )}
              </button>
            </form>
          ) : (
            <form onSubmit={handleSubmitMomo(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <label className="block text-dark font-semibold">
                  {" "}
                  {t("student.wallet.amount_mobile_money")}{" "}
                </label>
                <input
                  type="number"
                  {...registerMomo("amount", {
                    required: t(
                      "student. wallet.amount_is_required_mobile_money",
                    ),
                  })}
                  placeholder={t("student.wallet.enter_amount_mobile_money")}
                  className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                />
                {errorsMomo.amount && (
                  <p className="text-red-500 text-sm">
                    {errorsMomo.amount.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-dark font-semibold">
                  {t("student.wallet.provider_mobile_money")}
                </label>
                <select
                  {...registerMomo("network-provider")}
                  className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none capitalize"
                >
                  <option value="mtn"> {t("student.wallet.mtn")} </option>
                  <option value="orange"> {t("student.wallet.orange")} </option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="block text-dark font-semibold">
                  {t("student.wallet.phone_number")}
                </label>
                <input
                  {...registerMomo("account-number", {
                    required: t("student.wallet.phone_number_is_required"),
                    pattern: {
                      value: /^(6[5-9][0-9]{7})$/,
                      message:
                        "Must be a valid MTN or Orange number (e.g., *********)",
                    },
                  })}
                  maxLength={9}
                  type="text"
                  placeholder={t("student.wallet.place_holder")}
                  className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                />
                {errorsMomo["account-number"] && (
                  <p className="text-red-500 text-sm">
                    {errorsMomo["account-number"].message}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={!isValidMomo || isInitiatingPayment}
                className="
                  w-full py-4 bg-green-500 text-white rounded-lg 
                  hover:bg-green-500/90 transition-colors 
                  flex items-center justify-center space-x-2
                  disabled:opacity-50 disabled:cursor-not-allowed
                "
              >
                {isInitiatingPayment ? (
                  <Spinner />
                ) : (
                  <>
                    <FaCheckCircle />
                    <span> {t("student.wallet.fund_wallet")}</span>
                  </>
                )}
              </button>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default FundWallet;
