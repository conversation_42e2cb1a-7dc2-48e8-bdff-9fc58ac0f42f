import { useChangePasswordMutation } from "@/app/services/auth";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@/components/atoms";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { FaLock } from "react-icons/fa6";
import { MdEdit } from "react-icons/md";
import { useNavigate } from "react-router-dom";
import { z } from "zod";

const strongPasswordSchema = z
  .string()
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    "Password must be at least 8 characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character.",
  );

const passwordSchema = z
  .object({
    old_password: z
      .string()
      .min(8, "Current password must be at least 8 characters long."),
    new_password: strongPasswordSchema,
    new_password_confirmation: strongPasswordSchema,
  })
  .refine((data) => data.new_password === data.new_password_confirmation, {
    message: "New passwords do not match",
    path: ["new_password_confirmation"],
  });

type PasswordForm = z.infer<typeof passwordSchema>;

const UserProfile = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const [notifications, setNotifications] = useState({
    lessons: true,
    payments: true,
    messages: true,
    updates: true,
  });

  const [changePassword, { isLoading: isChangingPassword }] =
    useChangePasswordMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<PasswordForm>({
    resolver: zodResolver(passwordSchema),
  });

  const handlePasswordChange = async (data: PasswordForm) => {
    try {
      await RequestInterceptor.handleRequest(
        () => changePassword(data).unwrap(),
        {},
        "Change password",
      );
    } catch (error) {
      console.error("Error changing password", error);
    }
  };
  const { t } = useTranslation();

  return (
    <div className="max-w-4xl mx-auto p-4 space-y-6">
      {/* Profile Header */}
      <div className="flex items-center gap-6">
        <div className="relative">
          <img
            src={`https://ui-avatars.com/api/?name=${user!.first_name}+${user!.last_name}&background=random`}
            alt="Profile"
            className="w-24 h-24 rounded-full object-cover"
          />
        </div>
        <div>
          <h1 className="text-2xl font-bold">{`${user!.first_name} ${user!.last_name}`}</h1>
          <p className="text-slate-600">{user!.email}</p>
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200">
        <div className="p-4 border-b border-slate-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold">
            {" "}
            {t("student.profile.personal_info")}
          </h2>
          <button
            onClick={() => navigate("/student/account-settings")}
            className="flex items-center gap-2 px-3 py-1.5 text-sm border border-slate-300 rounded-md hover:bg-slate-50"
          >
            <MdEdit className="w-4 h-4" />
            Edit Details
          </button>
        </div>
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.full_names")}
              </label>
              <input
                type="text"
                disabled
                value={`${user!.first_name} ${user!.last_name}`}
                className="w-full px-3 py-2 border border-slate-300 rounded-md disabled:bg-slate-50 disabled:text-slate-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.email")}
              </label>
              <input
                type="email"
                value={user!.email}
                disabled
                className="w-full px-3 py-2 border border-slate-300 rounded-md disabled:bg-slate-50 disabled:text-slate-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.phone")}
              </label>
              <input
                type="tel"
                disabled
                placeholder={t("student.profile.add_phone_number")}
                className="w-full px-3 py-2 border border-slate-300 rounded-md disabled:bg-slate-50 disabled:text-slate-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.country")}
              </label>
              <input
                type="text"
                value="Cameroon"
                disabled
                className="w-full px-3 py-2 border border-slate-300 rounded-md disabled:bg-slate-50 disabled:text-slate-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200">
        <div className="p-4 border-b border-slate-200">
          <h2 className="text-lg font-semibold">
            {" "}
            {t("student.profile.notifications")}
          </h2>
        </div>
        <div className="p-4 space-y-4">
          <div className="space-y-4">
            {Object.entries(notifications).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="block text-sm font-medium text-slate-700">
                    {key.charAt(0).toUpperCase() + key.slice(1)}
                  </label>
                  <p className="text-sm text-slate-500">
                    {t("student.profile.recieve_notifications")}
                    {key}
                  </p>
                </div>
                <button
                  role="switch"
                  aria-checked={value}
                  onClick={() =>
                    setNotifications((prev) => ({ ...prev, [key]: !value }))
                  }
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                    ${value ? "bg-blue-600" : "bg-slate-200"}`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                      ${value ? "translate-x-6" : "translate-x-1"}`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-slate-200">
        <div className="p-4 border-b border-slate-200">
          <h2 className="text-lg font-semibold">
            {" "}
            {t("student.profile.password_security")}
          </h2>
        </div>
        <div className="p-4">
          <form
            onSubmit={handleSubmit(handlePasswordChange)}
            className="space-y-4"
          >
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.current_password")}
              </label>
              <input
                type="password"
                placeholder={t("student.profile.enter_current_password")}
                {...register("old_password")}
                className="w-full px-3 py-2 border border-slate-300 rounded-md"
              />
              {errors.old_password && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.old_password.message}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.new_password")}
              </label>
              <input
                type="password"
                placeholder={t("student.profile.enter_new_password")}
                {...register("new_password")}
                className="w-full px-3 py-2 border border-slate-300 rounded-md"
              />
              {errors.new_password && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.new_password.message}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-1">
                {t("student.profile.repeat_new_password")}
              </label>
              <input
                type="password"
                placeholder={t("student.profile.re-type_new_password")}
                {...register("new_password_confirmation")}
                className="w-full px-3 py-2 border border-slate-300 rounded-md"
              />
              {errors.new_password_confirmation && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.new_password_confirmation.message}
                </p>
              )}
            </div>
            {isChangingPassword ? (
              <Spinner />
            ) : (
              <LearnosoButton
                type="submit"
                title={t("student.profile.change_password")}
                animated
                disabled={isChangingPassword}
                icon={<FaLock />}
                variant="primary"
              />
            )}
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
