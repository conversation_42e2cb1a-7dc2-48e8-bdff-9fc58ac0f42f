import { usePostReviewMutation } from "@/app/services/reviews/review.service";
import { useGetTutorByIdQuery } from "@/app/services/tutor/tutor.service";
import { Spinner } from "@/components/atoms";
import { LearnosoButton } from "@/components/atoms/Button";
import { ModalWrapper } from "@/components/molecules";
import Modal from "@/components/molecules/ModalWrapper";
import { resources } from "@/features/tutor/components/organ/coursePrerequisites/data/data";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { DEFAULT_USER_PROFILE } from "@/lib/util/constants";
import dayjs from "dayjs";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { FaGlobe, FaGraduationCap, FaStar, FaUsers } from "react-icons/fa";
import { FaDownload } from "react-icons/fa6";
import { MdLink } from "react-icons/md";
import { Link, useNavigate, useParams } from "react-router-dom";
import { ITutorUser } from "../@types";
import Day from "../components/atoms/Day";
import Time from "../components/atoms/Time";
import ReviewsList from "../components/organs/ReviewList";
import BookTrialLesson from "./BookTrialLesson";

interface TimeSlot {
  time: string;
  available: boolean;
}

const MORNING_SLOTS: TimeSlot[] = [
  { time: "7:00AM", available: true },
  { time: "8:30AM", available: true },
  { time: "10:00AM", available: true },
  { time: "11:00AM", available: true },
];

export const BookTrialLessonModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  const [selectedDate, setSelectedDate] = React.useState<dayjs.Dayjs>(dayjs());
  const [selectedTime, setSelectedTime] = React.useState<string>("");
  const navigate = useNavigate();

  // Calculate the start of the week for the current selected date
  const weekStart = React.useMemo(() => {
    return selectedDate.startOf("week").add(0, "day");
  }, [selectedDate]);

  // Generate array of dates for the week
  const weekDates = React.useMemo(() => {
    return Array.from({ length: 7 }).map((_, index) =>
      weekStart.add(index, "day"),
    );
  }, [weekStart]);

  // Navigate between weeks
  const navigateWeek = (direction: "prev" | "next") => {
    setSelectedDate((current) => {
      return direction === "prev"
        ? current.subtract(1, "week")
        : current.add(1, "week");
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} opacity="bg-opacity-20">
      <div className="bg-white rounded-md">
        <button className="w-fit ml-auto block mr-4 mt-4" onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
              stroke="#AB401D"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9.16992 14.8299L14.8299 9.16992"
              stroke="#AB401D"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M14.8299 14.8299L9.16992 9.16992"
              stroke="#AB401D"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
        <BookTrialLesson />
      </div>
    </Modal>
  );
};

interface Props {
  tutor?: ITutorUser;
}

export const AvailabilityComponent: React.FC<Props> = ({ tutor }) => {
  const availability = tutor?.tutor?.availability
    ? (JSON.parse(
        tutor.tutor.availability as unknown as string,
      ) as Availability[])
    : [];

  // Group availability into "Morning" and "Evening" based on time
  const groupByTimeOfDay = (time: string) => {
    const hours = parseInt(time.split(":")[0], 10);
    return hours < 12 ? "Morning" : "Evening";
  };

  const groupedAvailability = availability.reduce<
    Record<string, { time: string; day: string }[]>
  >((acc, av) => {
    const timeOfDayFrom = groupByTimeOfDay(av.availability.from);
    const timeOfDayTo = groupByTimeOfDay(av.availability.to);

    // Add "from" time
    acc[timeOfDayFrom] = acc[timeOfDayFrom] || [];
    acc[timeOfDayFrom].push({
      time: av.availability.from,
      day: av.day_of_the_week,
    });

    // Add "to" time if it's a different time period
    if (timeOfDayFrom !== timeOfDayTo) {
      acc[timeOfDayTo] = acc[timeOfDayTo] || [];
      acc[timeOfDayTo].push({
        time: av.availability.to,
        day: av.day_of_the_week,
      });
    }

    return acc;
  }, {});

  const days = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  const activeDays = availability.map((av) =>
    av.day_of_the_week.toLocaleLowerCase(),
  );
  const { t } = useTranslation();

  return (
    <div className="flex flex-col gap-6">
      <div>
        <h2 className="font-semibold text-lg">
          {" "}
          {t("student.tutordetails.availability")}
        </h2>
      </div>
      <div>
        <p className="text-sm">Day(s)</p>
        <div className="flex gap-2 flex-wrap">
          {days.map((day, index: number) => (
            <Day
              key={day}
              day={day}
              active={activeDays.includes(day.toLowerCase())}
            />
          ))}
        </div>
      </div>
      <div>
        <p className="text-sm">
          {t("student.tutordetails.time_zone")} ({tutor?.tutor?.timezone})
        </p>
        {Object.entries(groupedAvailability).map(([timeOfDay, slots]) => (
          <div key={timeOfDay} className="mb-4">
            <p className="font-semibold">{timeOfDay}</p>
            <div className="flex gap-4 flex-wrap mt-2">
              {slots.map((slot, index) => (
                <Time
                  active
                  time={slot.time}
                  key={`${slot.day}-${slot.time}-${index}`}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ReviewStars: React.FC<{
  rating: number;
  onRatingChange?: (rating: number) => void;
}> = ({ rating, onRatingChange }) => {
  return (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <FaStar
          key={star}
          className={`${star <= rating ? "text-yellow-400" : "text-gray-500-300"} ${onRatingChange ? "cursor-pointer" : ""}`}
          onClick={() => onRatingChange && onRatingChange(star)}
        />
      ))}
    </div>
  );
};

const ReviewForm: React.FC = () => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");
  const { user } = useAuth();
  const { tutorId } = useParams();
  const [postReview, { isLoading: isPostingReview }] = usePostReviewMutation();
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (rating === 0) return;
    try {
      const review: IReview = {
        tutor_id: +tutorId!,
        student_id: user?.student?.id!,
        rating,
        review: comment,
      };
      const { success } = await RequestInterceptor.handleRequest(
        () => postReview(review).unwrap(),
        {},
        "Review",
      );
      if (success) {
        setRating(0);
        setComment("");
      }
    } catch (error) {}
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="bg-white p-6 rounded-lg shadow mb-6"
    >
      <h3 className="text-lg font-semibold mb-4">
        {t("student.tutordetails.review")}
      </h3>
      <div className="mb-4">
        <label className="block mb-2">Rating</label>
        <ReviewStars rating={rating} onRatingChange={setRating} />
      </div>
      <div className="mb-4">
        <label className="block mb-2">
          {t("student.tutordetails.comment")}
        </label>
        <textarea
          className="w-full p-2 border rounded-md"
          rows={4}
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          required
        />
      </div>
      {isPostingReview ? (
        <Spinner />
      ) : (
        <LearnosoButton
          title={t("student.tutordetails.submit_review")}
          type="submit"
          variant="primary"
        />
      )}
    </form>
  );
};

const TutorDetails: React.FC = () => {
  const [openBookTrialSessionModal, setOpenBookTrialSessionModal] =
    useState<boolean>(false);
  const navigate = useNavigate();
  const { id } = useParams();
  const {
    data: { data = {} } = {},
    isLoading: isFetchingTutor,
    status: tutorQueryStatus,
  } = useGetTutorByIdQuery(+id!);
  const [tutor, setTutor] = useState<ITutorUser>();

  useEffect(() => {
    switch (tutorQueryStatus) {
      case "fulfilled":
        setTutor(data as ITutorUser);
    }
  }, [tutorQueryStatus]);

  return (
    <>
      {isFetchingTutor ? (
        <ModalWrapper
          isOpen={isFetchingTutor}
          onClose={() => {}}
          opacity="bg-opacity-70"
          shouldStayOpenOnOverlayClicked={false}
        >
          <div className="grid h-screen place-items-center">
            <Spinner variant="default" />
          </div>
        </ModalWrapper>
      ) : (
        <section className="text-dark space-y-8">
          <Link
            className="flex gap-2 items-center cursor-pointer"
            to={"/student"}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M9.56994 18.8201C9.37994 18.8201 9.18994 18.7501 9.03994 18.6001L2.96994 12.5301C2.67994 12.2401 2.67994 11.7601 2.96994 11.4701L9.03994 5.40012C9.32994 5.11012 9.80994 5.11012 10.0999 5.40012C10.3899 5.69012 10.3899 6.17012 10.0999 6.46012L4.55994 12.0001L10.0999 17.5401C10.3899 17.8301 10.3899 18.3101 10.0999 18.6001C9.95994 18.7501 9.75994 18.8201 9.56994 18.8201Z"
                fill="#292D32"
              />
              <path
                d="M20.4999 12.75H3.66992C3.25992 12.75 2.91992 12.41 2.91992 12C2.91992 11.59 3.25992 11.25 3.66992 11.25H20.4999C20.9099 11.25 21.2499 11.59 21.2499 12C21.2499 12.41 20.9099 12.75 20.4999 12.75Z"
                fill="#292D32"
              />
            </svg>
            <div className="text-xl font-semibold">{`${tutor?.first_name} ${tutor?.last_name}`}</div>
          </Link>
          <div className="w-full bg-white flex gap-4 px-16 py-8 rounded-lg">
            <div className="space-y-4">
              <div className="">
                <img
                  src={tutor?.tutor?.profile_picture ?? DEFAULT_USER_PROFILE}
                  alt=""
                  className="w-72 h-72 object-fill "
                />
              </div>
              <div className="text-sm mb-2">
                <div className="flex items-center mb-1">
                  <FaGraduationCap size={16} className="mr-2" />
                  {tutor?.courses.map((course) => <>{course.name},</>)} - {0}{" "}
                  lessons
                </div>
                <div className="flex items-center mb-1">
                  <FaUsers size={16} className="mr-2" />
                  {tutor?.tutor!.students.length}{" "}
                  {t("student.tutordetails.student")} - {0}{" "}
                  {t("student.tutordetails.active_student")}
                </div>
                <div className="flex items-center">
                  <FaGlobe size={16} className="mr-2" />
                  {tutor?.tutor?.native_language ?? "EN"},{" "}
                  {tutor?.languages.map((language) => <>{language}</>)}
                </div>
              </div>
              <div className="space-y-1 max-w-xl">
                <div className="text-lg font-semibold">
                  {t("student.tutordetails.about_me")}
                </div>
                <div>{tutor?.tutor?.short_description}</div>
                <div>{tutor?.tutor?.bio}</div>
              </div>
            </div>
            <div className="flex flex-col justify-between gap-4 flex-1">
              <AvailabilityComponent tutor={tutor} />
              {/* Buttons */}
              <div className="flex gap-3 w-fit ml-auto my-4">
                <LearnosoButton
                  title="Book trial lesson"
                  width="w-fit"
                  action={() => setOpenBookTrialSessionModal(true)}
                />
                <LearnosoButton
                  title="Send Message"
                  variant="orange"
                  width="w-fit"
                  disabled
                />
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <h2 className="text-xl font-bold">
              {t("student.tutordetails.curriculum")}
            </h2>
            <div className="grid grid-cols-3 gap-4">
              {["Beginner", "Intermediate", "Advanced"].map((level, index) => (
                <div className="bg-white py-6 px-6 space-y-2 rounded-lg">
                  <h3 className="font-bold">{level}</h3>
                  <ul className="space-y-2">
                    {level === "Beginner"
                      ? [
                          t("student.tutordetails.introduction_to_physics"),
                          t("student.tutordetails.mechanics"),
                          t("student.tutordetails.thermodynamics"),
                          t("student.tutordetails.wave_and_oscillation"),
                          t("student.tutordetails.electricity_and_magnetism"),
                        ].map((topic, i) => (
                          <li key={i} className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2 text-green-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {topic}
                          </li>
                        ))
                      : [
                          t("student.tutordetails.advanced_mechanics"),
                          t("student.tutordetails.electromagnetic_theory"),
                          t("student.tutordetails.optics_and_modern_physics"),
                          t(
                            "student.tutordetails.thermodynamics_and_statistical_mechanics",
                          ),
                          t(
                            "student.tutordetails.nuclear_and_particle_physics",
                          ),
                        ].map((topic, i) => (
                          <li key={i} className="flex items-center">
                            <svg
                              className="w-5 h-5 mr-2 text-green-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {topic}
                          </li>
                        ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Resources */}
          <div className="bg-white p-5 rounded-md">
            <div className="flex items-center justify-between *:w-fit gap-4 mb-6">
              <div className="flex gap-2 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M17 13.4V16.4C17 20.4 15.4 22 11.4 22H7.6C3.6 22 2 20.4 2 16.4V12.6C2 8.6 3.6 7 7.6 7H10.6"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M17.0001 13.4H13.8001C11.4001 13.4 10.6001 12.6 10.6001 10.2V7L17.0001 13.4Z"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M11.6001 2H15.6001"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M7 5C7 3.34 8.34 2 10 2H12.62"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M21.9999 8V14.19C21.9999 15.74 20.7399 17 19.1899 17"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M22 8H19C16.75 8 16 7.25 16 5V2L22 8Z"
                    stroke="#2D2F39"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <div>Resources</div>
              </div>
              <LearnosoButton
                title={t("student.tutordetails.get_resources")}
                variant="primary"
                disabled
                icon={<FaDownload />}
              />
            </div>
            <div
              className="blur-md select-none pointer-events-none !cursor-not-allowed"
              onContextMenu={(e) => e.preventDefault()}
            >
              {resources.map((resource) => (
                <div
                  key={resource.id}
                  className="border-t border-slate-400 py-4"
                >
                  <h3 className="font-semibold mb-2">
                    {resource.id}. {resource.title}
                  </h3>
                  <div className="flex items-center text-gray-500">
                    <span className="mr-4">{resource.type}</span>
                    {resource.type === "Document" && (
                      <span>{resource.filename}</span>
                    )}
                  </div>
                  <a
                    href={resource.link}
                    className="text-blue-600 flex items-center mt-2"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <MdLink className="mr-1" size={16} /> {/* Updated icon */}
                    {resource.link}
                  </a>
                </div>
              ))}
            </div>
          </div>
          {/* New Reviews Section */}
          <div className="space-y-6">
            <h2 className="text-xl font-bold">
              {t("student.tutordetails.reviews")}
            </h2>
            <ReviewForm />
            <ReviewsList />
          </div>
        </section>
      )}
      {openBookTrialSessionModal && (
        <BookTrialLessonModal
          isOpen={openBookTrialSessionModal}
          onClose={() => {
            setOpenBookTrialSessionModal(false);
          }}
        />
      )}
    </>
  );
};

export default TutorDetails;
