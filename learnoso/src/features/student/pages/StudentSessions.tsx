import { useGetStudentLessonsQuery } from "@/app/services/lesson/lesson.service";
import { Learno<PERSON>B<PERSON><PERSON>, Spinner } from "@/components/atoms";
import Modal from "@/components/molecules/ModalWrapper";
import { DashboardCard } from "@/features/tutor/components/molecules/DashboardCard";
import DashboardHeader from "@/features/tutor/components/molecules/DashboardHeader";
import { cardVariants } from "@/features/tutor/pages/Dashboard";
import { DashboardCardType } from "@/features/tutor/types";
import {
  DateCalendar,
  PickersDay,
  PickersDayProps,
} from "@mui/x-date-pickers-v6";
import dayjs, { Dayjs } from "dayjs";
import { motion } from "framer-motion";
import React, { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  FaBookReader,
  FaChalkboardTeacher,
  FaClock,
  FaSearch,
} from "react-icons/fa";
import { FaBookBookmark } from "react-icons/fa6";
import { Link, Outlet, useLocation } from "react-router-dom";

const HighlightDay = (
  props: PickersDayProps<Dayjs> & {
    highlightedDays?: number[];
  },
) => {
  const { highlightedDays = [], day, outsideCurrentMonth, ...other } = props;

  const isSelected =
    !props.outsideCurrentMonth &&
    highlightedDays.indexOf(props.day.date()) >= 0;
  const hasPassed =
    new Date(day.toISOString()).getTime() < new Date().getTime();
  const [color, setColor] = useState("");
  useEffect(() => {
    const color =
      new Date(day.toISOString()).getTime() < new Date().getTime()
        ? "red"
        : "#009B77";
    if (isSelected) {
      setColor(color);
    }
  });

  return (
    <div key={props.day.toString()}>
      <PickersDay
        {...other}
        style={{
          background: isSelected ? color : "",
          color: isSelected ? "white" : "",
        }}
        outsideCurrentMonth={outsideCurrentMonth}
        day={day}
        onClick={() => {
          console.log(hasPassed);
          // filter lessons for this day.
        }}
      />
    </div>
  );
};

export const StudentSessions = () => {
  const { t } = useTranslation();

  const location = useLocation();
  const { data: lessonsData, isLoading: isFetchingStudentLessons } =
    useGetStudentLessonsQuery("student-lessons");

  // Derive insights from lesson data
  const lessonInsights = useMemo(() => {
    if (!lessonsData?.data)
      return {
        totalSessions: "0",
        highlightedDays: [],
        upcomingSessions: [],
        pastSessions: [],
      };

    const now = dayjs();
    const upcomingSessions = lessonsData.data.filter((lesson: any) =>
      dayjs(lesson.starts_at).isAfter(now),
    );

    const pastSessions = lessonsData.data.filter((lesson: any) =>
      dayjs(lesson.ends_at).isBefore(now),
    );

    const highlightedDays = [
      ...new Set(
        lessonsData.data.map((lesson: any) => dayjs(lesson.starts_at).date()),
      ),
    ];

    const cardItems: DashboardCardType[] = [
      {
        icon: <FaChalkboardTeacher size={24} />,
        title: t("student.studentsessions.total_sessions"),
        value: lessonsData.data.length.toString().padStart(2, "0"),
        change: 30,
        changePeriod: "30 days",
        isCta: true,
      },
      {
        icon: <FaBookBookmark size={24} />,
        title: t("student.studentsessions.upcoming_sessions"),
        value: upcomingSessions.length.toString().padStart(2, "0"),
        change: 30,
        changePeriod: "30 days",
      },
      {
        icon: <FaBookReader size={24} />,
        title: t("student.studentsessions.past_sessions"),
        value: pastSessions.length.toString().padStart(2, "0"),
        change: 30,
        changePeriod: "30 days",
      },
      {
        icon: <FaClock size={24} />,
        title: t("student.studentsessions.total_hours"),
        value: (lessonsData.data.length * 0.33).toFixed(2), // Assuming 20min lessons
        change: 30,
        changePeriod: "30 days",
      },
    ];

    return {
      totalSessions: lessonsData.data.length.toString(),
      highlightedDays,
      upcomingSessions,
      pastSessions,
      cardItems,
    };
  }, [lessonsData]);

  return (
    <section>
      <DashboardHeader />
      <div className="flex flex-wrap gap-4 mb-8">
        {isFetchingStudentLessons ? (
          <Modal isOpen onClose={() => {}} shouldStayOpenOnOverlayClicked>
            <Spinner variant="default" />
          </Modal>
        ) : (
          lessonInsights.cardItems &&
          lessonInsights.cardItems.map((item, i) => (
            <motion.div
              key={i}
              custom={i}
              initial="hidden"
              animate="visible"
              variants={cardVariants}
            >
              <DashboardCard {...item} />
            </motion.div>
          ))
        )}
      </div>
      <div className="flex gap-14 items-center text-lg mb-5">
        <Link
          to={"upcoming"}
          className={`pb-3 px-1 flex gap-2 items-center ${location.pathname === "/student/sessions/upcoming" || location.pathname === "/student/sessions" ? "border-b-4 border-b-primary" : "text-dark"}`}
        >
          {t("student.studentsessions.upcoming_session")}

          {lessonInsights.upcomingSessions?.length < 1 && (
            <span className="flex w-2 h-2 bg-red-500 rounded-full"></span>
          )}
        </Link>
        <Link
          to={"/student/sessions/past"}
          className={`pb-3 px-1 ${location.pathname === "/student/sessions/past" ? "border-b-4 border-b-primary" : "text-dark"}`}
        >
          {t("student.studentsessions.past_session")}
        </Link>
      </div>
      <div className="grid grid-cols-8 gap-4">
        <div className="col-span-5">
          <Outlet
            context={{
              upcomingSessions: lessonInsights.upcomingSessions,
              pastSessions: lessonInsights.pastSessions,
            }}
          />
        </div>
        <div className="col-span-3 self-start">
          <div className="bg-white p-4 space-y-4 rounded-lg">
            <div className="font-semibold text-lg">
              {" "}
              {t("student.studentsessions.search")}{" "}
            </div>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <FaSearch className="text-sm absolute top-4 left-2" />
                <input
                  type="text"
                  placeholder={t("student.studentsessions.search_for_sessions")}
                  className="pl-6 rounded-md px-2 py-2 text-gray-500 outline-none border"
                />
              </div>
              <LearnosoButton
                icon={<FaSearch className="" />}
                title={t("student.studentsessions.search_button")}
              />
            </div>
            <div className="font-semibold text-lg">
              {" "}
              {t("student.studentsessions.calendar")}
            </div>
            <div>
              <DateCalendar
                readOnly
                showDaysOutsideCurrentMonth
                slots={{
                  day: HighlightDay,
                }}
                slotProps={{
                  day: {
                    highlightedDays: lessonInsights.highlightedDays,
                    isPassed: dayjs().isAfter(Date.now()),
                  } as any,
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
