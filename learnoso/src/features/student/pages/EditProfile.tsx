import {
  useUpdateProfileImageMutation,
  useUpdateProfileMutation,
} from "@/app/services/auth";
import { useFetchLanguagesQuery } from "@/app/services/util/util.service";
import { LearnosoButton, Spinner } from "@/components/atoms";
import { useFetchCountries } from "@/features/auth/hooks/useFetchCountries";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import React, { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { MdClose, MdEdit } from "react-icons/md";

const EditProfile = () => {
  const { user } = useAuth();
  const [formData, setFormData] = useState<{
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    country: string;
    bio: string;
    language: number;
  }>({
    first_name: user!.first_name,
    last_name: user!.last_name,
    email: user!.email,
    phone: "",
    country: "Cameroon",
    bio: "",
    language: 1,
  });
  const [countries, setCountries] =
    useState<{ name: string; code: string }[]>();
  useFetchCountries(setCountries);
  const { data: languages, isLoading: isFetchingLanguages } =
    useFetchLanguagesQuery("");
  const [
    updateProfile,
    { isLoading: isUpdatingProfile, isSuccess: isProfileUpdated },
  ] = useUpdateProfileMutation();
  const [
    updateProfileImage,
    { isLoading: isUpdatingProfileImage, isSuccess: isProfileImageUpdated },
  ] = useUpdateProfileImageMutation();

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [image, setImage] = useState<any>();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      setImage(file);
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log({ ...formData, profile_image: imagePreview });
    const imageForm = new FormData();
    imageForm.append("image", image);
    try {
      Promise.all([
        RequestInterceptor.handleRequest(
          () =>
            updateProfile({
              country: formData.country,
              phone: formData.phone,
              last_name: formData.last_name,
              first_name: formData.first_name,
            }).unwrap(),
          {},
          "User Profile",
        ),
        image &&
          RequestInterceptor.handleRequest(
            () => updateProfileImage(imageForm).unwrap(),
            {},
            "User Profile",
          ),
      ]);
    } catch (error) {}
  };

  const removeImage = () => {
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  useEffect(() => {
    // refresh the page to get new data
    if (isProfileUpdated && isProfileImageUpdated) {
      window.location.reload();
    }
  }, [isProfileImageUpdated, isProfileUpdated]);
  const { t } = useTranslation();

  return (
    <div className="max-w-4xl mx-auto p-4">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Image Section */}
        <div className="flex items-center gap-6">
          <div className="relative">
            <div className="w-24 h-24 rounded-full overflow-hidden bg-slate-100">
              {imagePreview ? (
                <div className="relative group">
                  <img
                    src={imagePreview}
                    alt="Profile Preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={removeImage}
                    className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <MdClose className="w-6 h-6" />
                  </button>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <img
                    src={`https://ui-avatars.com/api/?name=${formData.first_name}+${formData.last_name}&background=random`}
                    alt="Profile"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleImageChange}
              accept="image/*"
              className="hidden"
              id="profile-image"
            />
            <label
              htmlFor="profile-image"
              className="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-md hover:bg-slate-50 cursor-pointer"
            >
              <MdEdit className="w-4 h-4" />
            </label>
          </div>
          <div>
            <h1 className="text-2xl font-bold">
              {" "}
              {t("student.editprofile.edit_profile")}e
            </h1>
            <p className="text-slate-600">
              {" "}
              {t("student.editprofile.update_your_personal_info")}
            </p>
          </div>
        </div>

        {/* Form Fields. */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.first_name")}
            </label>
            <input
              type="text"
              value={formData.first_name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, first_name: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.last_name")}
            </label>
            <input
              type="text"
              value={formData.last_name}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, last_name: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.email")}
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, email: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.phone_number")}{" "}
            </label>
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, phone: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="+237 XXXXXXXXX"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.country")}
            </label>
            <select
              value={formData.country}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, country: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {countries?.map((country) => (
                <option key={country.code} value={country.name}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.language")}
            </label>
            <select
              value={formData.language}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, language: +e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {languages?.data?.map(
                (language: { name: string; id: number }) => (
                  <option key={language.id} value={language.id}>
                    {language.name}
                  </option>
                ),
              )}
            </select>
          </div>

          <div className="col-span-2">
            <label className="block text-sm font-medium text-slate-700 mb-1">
              {t("student.editprofile.bio")}
            </label>
            <textarea
              value={formData.bio}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, bio: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-32"
              placeholder={t("student.editprofile.tell_us_about_yourself")}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          {isUpdatingProfile || isUpdatingProfileImage ? (
            <Spinner />
          ) : (
            <LearnosoButton
              title={t("student.editprofile.save_changes")}
              type="submit"
              variant="primary"
              disabled={isUpdatingProfile || isUpdatingProfileImage}
              width="w-fit"
            />
          )}
          <button
            type="button"
            // onClick={onCancel}
            className="px-4 py-2 border border-slate-300 rounded-md hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2"
          >
            {t("student.editprofile.cancel")}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditProfile;
