import { motion } from "framer-motion";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { AvailabilityFilter } from "../components/molecules/Filters";
import Sessions from "../components/organs/Sessions";
import Tutors from "../components/organs/Tutors";

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

const StudentDashboard = () => {
  const [filters, setFilters] = useState({
    language: "",
    subject: "",
    cost: { min: 0, max: 0 },
    availability: {
      day_of_the_week: "",
      from: "",
      to: "",
    },
  });

  const handleFilterChange = (key: string, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleCostChange = (key: keyof typeof filters.cost, value: string) => {
    setFilters((prev) => ({
      ...prev,
      cost: { ...prev.cost, [key]: parseInt(value, 10) || 0 }, // Convert string to number
    }));
  };

  const handleAvailabilityChange = (key: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      availability: { ...prev.availability, [key]: value },
    }));
  };
  const { t } = useTranslation();
  return (
    <>
      <main className="container mx-auto px-4 py-8">
        <motion.div
          className="grid grid-cols-3 gap-8"
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
        >
          {/* Tutors Block */}
          <Tutors
            language={filters.language}
            subject={filters.subject}
            cost={filters.cost}
          />

          {/* Filter Block */}
          <div className="space-y-8">
            <div className="bg-white shadow rounded-lg p-6 space-y-6">
              <h2 className="text-lg font-semibold">
                {t("student.studentdashboard.filter_tutor")}
              </h2>

              {/* Language Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="language"
                  className="block text-sm font-medium text-gray-500-700"
                >
                  {t("student.studentdashboard.langauge")}
                </label>
                <select
                  id="language"
                  value={filters.language}
                  onChange={(e) =>
                    handleFilterChange("language", e.target.value)
                  }
                  className="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">{t("student.studentdashboard.any")}</option>
                  <option value="english">
                    {t("student.studentdashboard.english")}
                  </option>
                  <option value="french">
                    {t("student.studentdashboard.french")}
                  </option>
                  <option value="spanish">
                    {t("student.studentdashboard.spanish")}
                  </option>
                </select>
              </div>

              {/* Subject Filter. */}
              <div className="space-y-2">
                <label
                  htmlFor="subject"
                  className="block text-sm font-medium text-gray-500-700"
                >
                  {t("student.studentdashboard.subject")}
                </label>
                <input
                  type="text"
                  id="subject"
                  value={filters.subject}
                  onChange={(e) =>
                    handleFilterChange("subject", e.target.value)
                  }
                  placeholder={t("student.studentdashboard.maths")}
                  className="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Cost Filter */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-500-700">
                  {t("student.studentdashboard.cost")}
                </label>
                <div className="flex items-center gap-4">
                  <input
                    type="number"
                    placeholder={t("student.studentdashboard.min")}
                    value={filters.cost.min || ""}
                    onChange={(e) => handleCostChange("min", e.target.value)}
                    className="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="number"
                    placeholder="Max"
                    value={filters.cost.max || ""}
                    onChange={(e) => handleCostChange("max", e.target.value)}
                    className="block w-full border-gray-300 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              {/* Availability Filter */}
              <AvailabilityFilter
                filters={filters}
                handleAvailabilityChange={handleAvailabilityChange}
              />
            </div>

            {/* Sessions Block */}
            <Sessions />
          </div>
        </motion.div>
      </main>
    </>
  );
};

export default StudentDashboard;
