import { useInitiatePaymentMutation } from "@/app/services/payment/payment.service";
import { useGetTutorByIdQuery } from "@/app/services/tutor/tutor.service";
import { LearnosoButton } from "@/components/atoms/Button";
import Spinner from "@/components/atoms/Spinner";
import Modal from "@/components/molecules/ModalWrapper";
import useQuery from "@/hooks/useQuery";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { MAX_CARD_LENGTH } from "@/lib/util";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { FaChevronDown } from "react-icons/fa6";
import { Link, useNavigate, useParams } from "react-router-dom";
import { toast } from "react-toastify";
import { ITutorUser } from "../@types";
import {
  CardPaymentFormData,
  MobileMoneyFormData,
  PaymentMethodType,
  TrialLessonStatus,
} from "../student.types";

type PaymentInfo = {
  description: string;
  currency: string | "usd";
  payment_method: "stripe";
  total: number;
};

const CheckoutTrialLesson: React.FC = () => {
  const { id } = useParams();
  const {
    data: { data = {} } = {},
    isLoading: isFetchingTutor,
    status: tutorQueryStatus,
  } = useGetTutorByIdQuery(+id!);
  const [tutor, setTutor] = useState<ITutorUser>();

  useEffect(() => {
    switch (tutorQueryStatus) {
      case "fulfilled":
        setTutor(data as ITutorUser);
    }
  }, [tutorQueryStatus]);

  const [status, setStatus] = useState<TrialLessonStatus>("pending");
  const navigate = useNavigate();

  const [
    initiatePayment,
    { isLoading: isInitiatingPayment, isError, data: initiatePaymentResponse },
  ] = useInitiatePaymentMutation();

  const onSubmit = async (data: CardPaymentFormData | MobileMoneyFormData) => {
    setStatus("processing");
    switch (data["payment-method"]) {
      case "master-card":
        const paymentInfo: PaymentInfo = {
          currency: tutor?.tutor?.currency ?? "usd",
          description: `Book Trial lesson with tutor ${tutor?.first_name}`,
          payment_method: "stripe",
          total: tutor?.tutor?.price! + 100,
        };
        const { data } = await RequestInterceptor.handleRequest(
          () => initiatePayment(paymentInfo).unwrap(),
          {
            onSuccess: () => {
              setStatus("pending");
              console.log(initiatePaymentResponse);
            },
            onError: () => {
              setStatus("pending");
              console.log(initiatePaymentResponse);
            },
          },
          "Book trial Lesson.",
        );
        window.open(data.url, "_blank", "noopener,noreferrer");

        return;
      case "mobile-money":
        toast.info("Payment method not available at this time", {
          delay: 3000,
          position: "top-right",
        });
        return;
      default:
        return;
    }
  };

  const query = useQuery();
  const time = query.get("time");
  const date = query.get("date");
  const type = query.get("type");

  useEffect(() => {
    if (!time || !date || !type) {
      navigate("/student", { replace: true });
    }
  }, [query, navigate]);

  const [paymentMethod, setPaymentMethod] =
    useState<PaymentMethodType>("master-card");

  const {
    register: registerCard,
    handleSubmit: handleSubmitCard,
    formState: { errors: errorsCard, isValid: isValidCard },
    watch: watchCard,
    setValue: setValueCard,
  } = useForm<CardPaymentFormData>({
    defaultValues: {
      "save-card": false,
      "card-number": "4242 4242 4242 4242",
      "payment-method": "master-card",
      cvc: 123,
      mm: 12,
      yy: 30,
    },
    mode: "onChange",
  });

  const formatCreditCard = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(" ");
    } else {
      return value;
    }
  };

  const creditCardValue = watchCard("card-number");

  React.useEffect(() => {
    setValueCard("card-number", formatCreditCard(creditCardValue));
  }, [creditCardValue, setValueCard]);

  const {
    register: registerMomo,
    handleSubmit: handleSubmitMomo,
    formState: { errors: errorsMomo, isValid: isValidMomo },
  } = useForm<MobileMoneyFormData>({
    defaultValues: {
      "network-provider": "mtn",
      "payment-method": "mobile-money",
    },
    mode: "onChange",
  });
  const [selectedCourse, setSelectedCourse] = useState<number>(1);

  if (isFetchingTutor || isInitiatingPayment) {
    return (
      <Modal isOpen={true} onClose={() => {}} opacity="bg-opacity-80">
        <Spinner variant="default" size="extra-large" />
      </Modal>
    );
  }
  if (!tutor) {
    return <div> {t("student.checkouttriallesson.no_tutor")}</div>;
  }

  return (
    <>
      <section className="text-dark space-y-8">
        <Link
          to={`/student/tutors/${id}/info`}
          className="flex gap-2 items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M9.56994 18.8201C9.37994 18.8201 9.18994 18.7501 9.03994 18.6001L2.96994 12.5301C2.67994 12.2401 2.67994 11.7601 2.96994 11.4701L9.03994 5.40012C9.32994 5.11012 9.80994 5.11012 10.0999 5.40012C10.3899 5.69012 10.3899 6.17012 10.0999 6.46012L4.55994 12.0001L10.0999 17.5401C10.3899 17.8301 10.3899 18.3101 10.0999 18.6001C9.95994 18.7501 9.75994 18.8201 9.56994 18.8201Z"
              fill="#292D32"
            />
            <path
              d="M20.4999 12.75H3.66992C3.25992 12.75 2.91992 12.41 2.91992 12C2.91992 11.59 3.25992 11.25 3.66992 11.25H20.4999C20.9099 11.25 21.2499 11.59 21.2499 12C21.2499 12.41 20.9099 12.75 20.4999 12.75Z"
              fill="#292D32"
            />
          </svg>
          <div className="text-xl font-semibold">
            {" "}
            {t("student.checkouttriallesson.book_session")}
          </div>
        </Link>
        <div className="grid grid-cols-7 gap-8 *:border *:border-dark/20 *:shadow-md">
          <div className="bg-white col-span-3 p-6 px-8">
            <div className="flex gap-6 items-center">
              <div>
                <img
                  src="/assets/images/549f9e5fbf405f4170c1c9f143d0d468.jpeg"
                  alt=""
                  className="w-[76px] h-[76px]"
                />
              </div>
              <div className="">
                <div className="relative w-full max-w-sm">
                  <label
                    htmlFor="courses"
                    className="block mb-2 text-sm font-semibold text-gray-500-800 dark:text-gray-500-200"
                  >
                    {t("student.checkouttriallesson.select_a_course")}
                  </label>
                  <div className="relative">
                    <select
                      id="courses"
                      name="courses"
                      value={selectedCourse}
                      onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                        setSelectedCourse(+e.target.value)
                      }
                      className="appearance-none block w-full px-3 py-2 border border-gray  rounded-md 
            shadow-sm text-sm text-dark bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 
            transition-colors duration-200 ease-in-out"
                    >
                      {tutor.courses.map((course) => (
                        <option
                          key={course.id}
                          value={course.id}
                          className="bg-white dark:bg-gray-700"
                        >
                          {course.name}
                        </option>
                      ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-500">
                      <FaChevronDown className="h-5 w-5" />
                    </div>
                  </div>
                </div>

                <p className="text-sm my-2 font-semibold">{`${tutor.first_name} ${tutor.last_name}`}</p>
              </div>
              <div className="flex gap-2 items-center">
                <div className="flex gap-2 items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M13.73 3.50989L15.49 7.02989C15.73 7.51989 16.37 7.98989 16.91 8.07989L20.1 8.60989C22.14 8.94989 22.62 10.4299 21.15 11.8899L18.67 14.3699C18.25 14.7899 18.02 15.5999 18.15 16.1799L18.86 19.2499C19.42 21.6799 18.13 22.6199 15.98 21.3499L12.99 19.5799C12.45 19.2599 11.56 19.2599 11.01 19.5799L8.02003 21.3499C5.88003 22.6199 4.58003 21.6699 5.14003 19.2499L5.85003 16.1799C5.98003 15.5999 5.75003 14.7899 5.33003 14.3699L2.85003 11.8899C1.39003 10.4299 1.86003 8.94989 3.90003 8.60989L7.09003 8.07989C7.62003 7.98989 8.26003 7.51989 8.50003 7.02989L10.26 3.50989C11.22 1.59989 12.78 1.59989 13.73 3.50989Z"
                      stroke="#FF7F00"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <div>
                    0{" "}
                    <span className="font-semibold">
                      {" "}
                      {t("student.checkouttriallesson.review")}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="my-8 flex gap-2 items-center">
              <div className="py-2 px-12 rounded-md border border-dark/25 font-semibold">
                {t("student.checkouttriallesson.min")}
              </div>
              <div className="py-2 px-4 rounded-md font-semibold text-green-500">
                {type} {t("student.checkouttriallesson.session")}
              </div>
            </div>
            <hr className="-mx-4" />
            <div className="my-8">
              <p className="text-lg font-semibold">
                {new Date(date!).toLocaleDateString()} {time}{" "}
                {tutor.tutor?.timezone}
              </p>
            </div>
            <hr className="-mx-4" />
            <div className="my-8">
              <h3 className="text-xl mb-4">
                {" "}
                {t("student.checkouttriallesson.your_order")}
              </h3>
              <div className="flex gap-4 items-center justify-between my-2">
                <span className="text-sm">
                  {" "}
                  {t("student.checkouttriallesson.20mins_lesson")}
                </span>
                <span className="text-lg font-semibold">
                  {`${tutor.tutor?.price} ${tutor.tutor?.currency}`}{" "}
                </span>
              </div>
              <div className="flex gap-4 items-center justify-between my-2">
                <span className="text-sm flex gap-2 items-center">
                  {t("student.checkouttriallesson.processing_fee")}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                  >
                    <g clip-path="url(#clip0_545_5495)">
                      <path
                        d="M9.99996 18.3332C14.6023 18.3332 18.3333 14.6022 18.3333 9.99984C18.3333 5.39746 14.6023 1.6665 9.99996 1.6665C5.39759 1.6665 1.66663 5.39746 1.66663 9.99984C1.66663 14.6022 5.39759 18.3332 9.99996 18.3332Z"
                        stroke="#565459"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.57495 7.49999C7.77087 6.94304 8.15758 6.47341 8.66658 6.17426C9.17558 5.87512 9.77403 5.76577 10.3559 5.86558C10.9378 5.96539 11.4656 6.26792 11.8458 6.71959C12.2261 7.17126 12.4342 7.74292 12.4333 8.33332C12.4333 9.99999 9.93328 10.8333 9.93328 10.8333"
                        stroke="#565459"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M10 13.3335V13.7502"
                        stroke="#565459"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_545_5495">
                        <rect width="20" height="20" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>
                </span>
                <span className="text-lg font-semibold">0</span>
              </div>
              <div className="flex gap-4 items-center justify-between my-2">
                <span className="text-sm">Total</span>
                <span className="text-lg font-semibold">
                  {`${tutor.tutor?.price} ${tutor.tutor?.currency}`}
                </span>
              </div>
            </div>
          </div>
          <div className="col-span-4 bg-white p-6 px-8 h-fit space-y-6">
            <div className="text-xl font-semibold">
              {" "}
              {t("student.checkouttriallesson.choose_a_payment_method")}{" "}
            </div>
            <div className="flex gap-6">
              <label className="flex gap-2 items-center">
                <input
                  type="radio"
                  value="master-card"
                  className="!accent-primary"
                  name="payment-method"
                  defaultChecked
                  onChange={() => setPaymentMethod("master-card")}
                />
                <span>Master Card</span>
              </label>
              <label className="flex gap-2 items-center">
                <input
                  type="radio"
                  value="mobile-money"
                  className="!accent-primary"
                  name="payment-method"
                  onChange={() => setPaymentMethod("mobile-money")}
                />
                <span>Mobile Money</span>
              </label>
            </div>
            {paymentMethod == "master-card" ? (
              <form className="space-y-6" onSubmit={handleSubmitCard(onSubmit)}>
                <div className="space-y-1">
                  <label htmlFor="card-number">
                    {" "}
                    {t("student.checkouttriallesson.card_number")}{" "}
                  </label>
                  <input
                    type="text"
                    {...registerCard("card-number", {
                      required: "Credit card number is required",
                      validate: (value) =>
                        value.replace(/\s/g, "").length === MAX_CARD_LENGTH ||
                        "Credit card must be exactly 16 digits",
                    })}
                    id="card-number"
                    placeholder="1234 1234 1234 1234"
                    className="px-4 py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                  />
                  <p className="error">{errorsCard["card-number"]?.message}</p>
                </div>
                <div className="flex gap-4">
                  <div className="space-y-1">
                    <label htmlFor="mm">
                      {" "}
                      {t("student.checkouttriallesson.mm")}
                    </label>
                    <input
                      type="text"
                      {...registerCard("mm", {
                        required: {
                          value: true,
                          message: "Expiration month is required",
                        },
                        pattern: {
                          value: /^(0[1-9]|1[0-2])$/,
                          message: "Must be a valid month (01-12)",
                        },
                      })}
                      id="mm"
                      className="px-4 py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                    />
                    <p className="error">{errorsCard.mm?.message}</p>
                  </div>
                  <div className="space-y-1">
                    <label htmlFor="yy">
                      {" "}
                      {t("student.checkouttriallesson.yy")}
                    </label>
                    <input
                      type="text"
                      {...registerCard("yy", {
                        required: {
                          value: true,
                          message: t(
                            "student.checkouttriallesson.expiration_date_required",
                          ),
                        },
                        pattern: {
                          value: /^[0-9]{2}$/,
                          message: t(
                            "student.checkouttriallesson.must_be_a_valid_2digit_number",
                          ),
                        },
                      })}
                      id="yy"
                      className="px-4 py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                    />
                    <p className="error">{errorsCard.yy?.message}</p>
                  </div>
                  <div className="space-y-1">
                    <label htmlFor="cvc">CVC</label>
                    <input
                      type="text"
                      {...registerCard("cvc", {
                        required: {
                          value: true,
                          message: t(
                            "student.checkouttriallesson.cvc_is_required",
                          ),
                        },
                        pattern: {
                          value: /^[0-9]{3,4}$/,
                          message: t(
                            "student.checkouttriallesson.cvc_must_valid",
                          ),
                        },
                      })}
                      id="cvc"
                      className="px-4 py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                    />
                    <p className="error">{errorsCard.cvc?.message}</p>
                  </div>
                </div>

                <div className="flex gap-2 items-center">
                  <input
                    type="checkbox"
                    {...registerCard("save-card")}
                    id="save-card"
                    className="!accent-primary scale-125"
                  />
                  <label htmlFor="save-card">
                    {t(
                      "student.checkouttriallesson.save_this_card_for_future_payments",
                    )}
                  </label>
                </div>
                <LearnosoButton
                  title="Confirm Payment"
                  disabled={!isValidCard}
                  variant="primary"
                  type="submit"
                />
                <div>
                  {t(
                    "student.checkouttriallesson.by_confirming_this_payment_you_agree",
                  )}{" "}
                  <Link
                    to={"/refund-policy"}
                    className="text-primary font-medium"
                  >
                    {t("student.checkouttriallesson.refund")}
                  </Link>{" "}
                  {t("student.checkouttriallesson.and")}{" "}
                  <Link
                    to={"/payment-policy"}
                    className="text-primary font-medium"
                  >
                    {t("student.checkouttriallesson.payment_policy")}
                  </Link>
                </div>
              </form>
            ) : (
              <form className="space-y-6" onSubmit={handleSubmitMomo(onSubmit)}>
                <div className="space-y-1">
                  <label htmlFor="network-provider">
                    {" "}
                    {t("student.checkouttriallesson.network_provider")} Network
                    Provider:
                  </label>
                  <select
                    {...registerMomo("network-provider")}
                    id="network-provider"
                    className="px-4 capitalize py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                  >
                    <option value="mtn">
                      {" "}
                      {t("student.checkouttriallesson.mtn")}{" "}
                    </option>
                    <option value="orange">
                      {" "}
                      {t("student.checkouttriallesson.orange")}
                    </option>
                  </select>
                  <p className="error">
                    {errorsMomo["network-provider"]?.message}
                  </p>
                </div>
                <div className="space-y-1">
                  <label htmlFor="network-provider">
                    {" "}
                    {t("student.checkouttriallesson.account_number")}:
                  </label>
                  <input
                    {...registerMomo("account-number", {
                      required: t(
                        "student.checkouttriallesson.phone_number_is_required",
                      ),
                      pattern: {
                        value: /^(6[5-9][0-9]{7})$/,
                        message: t(
                          "student.checkouttriallesson.must_be_a_valid_orange_or_mtn_number",
                        ),
                      },
                    })}
                    maxLength={9}
                    type="text"
                    id="account-number"
                    placeholder="e.g. *********"
                    className="px-4 capitalize py-3 border border-dark/20 placeholder:text-dark/40 outline-none w-full"
                  />
                  <p className="error">
                    {errorsMomo["account-number"]?.message}
                  </p>
                </div>
                <LearnosoButton
                  disabled={!isValidMomo}
                  title={t("student.checkouttriallesson.confirm_payment")}
                  variant="primary"
                  type="submit"
                />
              </form>
            )}
          </div>
        </div>
      </section>
      {status === "processing" && (
        <Modal
          opacity="bg-opacity-30"
          isOpen={status === "processing"}
          onClose={() => {}}
        >
          <Spinner variant="default" size="extra-large" />
        </Modal>
      )}
      {status === "successfull" && (
        <Modal
          opacity="bg-opacity-30"
          isOpen={status === "successfull"}
          onClose={() => {}}
        >
          <div className="bg-white rounded-md py-10 px-8 w-full max-w-lg text-dark flex flex-col items-center gap-8">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="144"
              height="144"
              viewBox="0 0 144 144"
              fill="none"
            >
              <path
                d="M72 0C57.7598 0 43.8393 4.22273 31.999 12.1342C20.1586 20.0457 10.9302 31.2905 5.48071 44.4468C0.0311962 57.6031 -1.39464 72.0799 1.38349 86.0465C4.16163 100.013 11.019 112.842 21.0883 122.912C31.1577 132.981 43.9869 139.838 57.9535 142.617C71.9202 145.395 86.397 143.969 99.5533 138.519C112.71 133.07 123.954 123.841 131.866 112.001C139.777 100.161 144 86.2403 144 72C144 52.9044 136.414 34.5909 122.912 21.0883C109.409 7.58569 91.0956 0 72 0ZM119.025 47.835L59.895 106.92L24.975 72C23.7816 70.8065 23.1111 69.1878 23.1111 67.5C23.1111 65.8122 23.7816 64.1935 24.975 63C26.1685 61.8065 27.7872 61.136 29.475 61.136C31.1629 61.136 32.7816 61.8065 33.975 63L59.985 89.01L110.115 38.925C110.706 38.3341 111.408 37.8653 112.18 37.5455C112.952 37.2257 113.779 37.061 114.615 37.061C115.451 37.061 116.278 37.2257 117.05 37.5455C117.823 37.8653 118.524 38.3341 119.115 38.925C119.706 39.5159 120.175 40.2175 120.495 40.9896C120.814 41.7617 120.979 42.5893 120.979 43.425C120.979 44.2607 120.814 45.0883 120.495 45.8604C120.175 46.6325 119.706 47.3341 119.115 47.925L119.025 47.835Z"
                fill="#009B77"
              />
            </svg>
            <div className="text-center">
              <p className="text-3xl">
                {t("student.checkouttriallesson.payment")}{" "}
                <span className="text-green-500">
                  {" "}
                  {t("student.checkouttriallesson.sucessful")}
                </span>{" "}
                {t("student.checkouttriallesson.processed")}
              </p>
              <p>{t("student.checkouttriallesson.you_will_be_notified")}</p>
            </div>
            <div className="*:w-fit">
              <LearnosoButton
                title={t("student.checkouttriallesson.done")}
                action={() => alert("supposed to redirect to some page")}
              />
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default CheckoutTrialLesson;
