import { useScheduleLessonMutation } from "@/app/services/lesson/lesson.service";
import { useGetTutorByIdQuery } from "@/app/services/tutor/tutor.service";
import { Spinner } from "@/components/atoms";
import { LearnosoButton } from "@/components/atoms/Button";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import dayjs from "dayjs";
import { t } from "i18next";
import React, { useEffect, useMemo, useState } from "react";
import { FaChevronDown, FaQuestion } from "react-icons/fa";
import { useNavigate, useParams } from "react-router-dom";
import { ITutorUser } from "../@types";
import {ILesson} from '@/app/services/lesson/interface'

interface TimeSlot {
  time: string;
  available: boolean;
}

const MORNING_SLOTS: TimeSlot[] = [
  { time: "7:00AM", available: true },
  { time: "8:30AM", available: true },
  { time: "10:00AM", available: true },
  { time: "11:00AM", available: true },
];

const BookTrialLesson: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<dayjs.Dayjs>(dayjs());
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [selectedType, setSelectedType] = useState<"one-to-one" | "group">(
    "one-to-one",
  );
  const navigate = useNavigate();
  const { id } = useParams();

  // Calculate the start of the week for the current selected date
  const weekStart = useMemo(() => {
    return selectedDate.startOf("week").add(0, "day");
  }, [selectedDate]);

  // Generate array of dates for the week
  const weekDates = useMemo(() => {
    return Array.from({ length: 7 }).map((_, index) =>
      weekStart.add(index, "day"),
    );
  }, [weekStart]);

  // Navigate between weeks
  const navigateWeek = (direction: "prev" | "next") => {
    setSelectedDate((current) => {
      return direction == "prev"
        ? current.subtract(1, "week")
        : current.add(1, "week");
    });
  };

  const {
    data: { data = {} } = {},
    isLoading: isFetchingTutor,
    status: tutorQueryStatus,
  } = useGetTutorByIdQuery(+id!);
  const [tutor, setTutor] = useState<ITutorUser>();
  const [selectedCourse, setSelectedCourse] = useState<number>(1);
  useEffect(() => {
    switch (tutorQueryStatus) {
      case "fulfilled":
        setTutor(data as ITutorUser);
        return;
    }
    console.log(data);
  }, [tutorQueryStatus]);
  const { user } = useAuth();
  const [scheduleLesson, { isLoading: isSchedulingLesson }] =
    useScheduleLessonMutation();

  const finishLessonSetup = async () => {
    const combinedDateTime =
      selectedDate.format("YYYY-MM-DD") + " " + selectedTime;
    const startTime = dayjs(combinedDateTime, "YYYY-MM-DD h:mmA");
    const endsAt = startTime.add(20, "minute");
    const lesson: ILesson = {
      course_id: selectedCourse,
      timezone: tutor?.tutor?.timezone!,
      starts_at: dayjs(startTime).format("YYYY-MM-DD HH:mm:ss"),
      ends_at: dayjs(endsAt).format("YYYY-MM-DD HH:mm:ss"),
      student_id: user?.id!,
      title: `Lesson with ${tutor?.first_name} and ${user?.first_name}`,
      tutor_id: tutor?.id!,
      description: `Trial Lesson with ${tutor?.first_name} and ${user?.first_name}`,
    };
    console.log(lesson);
    await RequestInterceptor.handleRequest(
      () => scheduleLesson(lesson).unwrap(),
      {
        onSuccess: () => {
          // navigate to student sessions gracefully...
        },
      },
      "Schedule Lesson",
    );
  };

  return (
    <div className="flex justify-center">
      <div className="bg-white rounded-md p-6 px-8 w-full text-dark space-y-4">
        <div className="relative w-full">
          <label
            htmlFor="courses"
            className="block mb-2 text-sm font-semibold text-gray-500-800 dark:text-gray-500-200"
          >
            {t("student.booktraillesson.select_a_course")}
          </label>
          {isFetchingTutor ? (
            <Spinner />
          ) : (
            <div className="relative">
              <select
                id="courses"
                name="courses"
                value={selectedCourse}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  setSelectedCourse(+e.target.value)
                }
                className="appearance-none block w-full px-3 py-2 border border-gray  rounded-md 
            shadow-sm text-sm text-dark bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 
            transition-colors duration-200 ease-in-out"
              >
                {tutor?.courses?.map((course) => (
                  <option
                    key={course.id}
                    value={course.id}
                    className="bg-white dark:bg-gray-700"
                  >
                    {course.name}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500 dark:text-gray-500">
                <FaChevronDown className="h-5 w-5" />
              </div>
            </div>
          )}
        </div>
        <div className="flex gap-4 items-center">
          <img
            src={tutor?.tutor?.profile_picture!}
            alt=""
            className="w-10 h-10 rounded-full object-cover"
          />
          <div className="text-lg font-semibold">
            {t("student.booktraillesson.when_would_you_like_your_session")}{" "}
            {user?.last_name}?
          </div>
        </div>
        <div className="font-semibold">
          {t("student.booktraillesson.choose_date_and_time")} (
          {tutor?.tutor?.timezone})
        </div>

        <div className="mb-6">
          <div className="mb-4 flex items-center justify-between">
            <button onClick={() => navigateWeek("prev")}>
              <ChevronLeft className="h-4 w-4" />
            </button>
            <p className="text-sm font-medium">
              {weekStart.format("MMMM D")}-
              {weekStart.add(6, "day").format("D, YYYY")}
            </p>
            <button onClick={() => navigateWeek("next")}>
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
          <div className="mb-6 grid grid-cols-7 gap-2 text-center">
            {["Sun", "Mon", "Tues", "Wed", "Thurs", "Fri", "Sat"].map(
              (day, i) => (
                <div key={day} className="text-sm font-medium">
                  {day}
                </div>
              ),
            )}
            {weekDates.map((date, i) => (
              <button
                key={date.toString()}
                className={`
                        h-12 hover:bg-red-500/5
                        ${selectedDate.isSame(date, "day") && "bg-red-500/10 border border-red rounded-md font-bold"}`}
                onClick={() => setSelectedDate(date)}
              >
                {date.format("D")}
              </button>
            ))}
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="text-sm text-primary">
            {" "}
            {t("student.booktraillesson.morning")}{" "}
          </h3>
          <div className="grid grid-cols-2 gap-2 md:grid-cols-4">
            {MORNING_SLOTS.map((slot) => (
              <button
                key={slot.time}
                className={`
                        text-sm font-semibold border border-dark rounded-md p-2 px-2.5 w-28 text-center 
                        ${
                          selectedTime === slot.time &&
                          "border-red bg-red-500/5 font-bold"
                        }
                      `}
                onClick={() => setSelectedTime(slot.time)}
              >
                {slot.time}
              </button>
            ))}
          </div>
        </div>
        <div className="space-y-2">
          <h3 className="font-semibold my-2">
            {" "}
            {t("student.booktraillesson.session_details")}{" "}
          </h3>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm font-medium">
                {" "}
                {t("student.booktraillesson.session_type")}
              </p>
              <div className="flex gap-4">
                <div
                  className="flex items-center space-x-2"
                  onClick={() => setSelectedType("one-to-one")}
                >
                  <input
                    type="radio"
                    name="session-type"
                    value="one-on-one"
                    id="one-on-one"
                    className="cursor-pointer border border-primary"
                  />
                  <label
                    htmlFor="one-on-one"
                    className="font-medium cursor-pointer"
                  >
                    {t("student.booktraillesson.one_on_one")}
                  </label>
                </div>
                <div
                  className="flex items-center space-x-2 cursor-pointer"
                  onClick={() => setSelectedType("one-to-one")}
                >
                  <input
                    type="radio"
                    name="session-type"
                    value="group"
                    className="cursor-pointer"
                    id="group"
                  />
                  <label htmlFor="group" className="font-medium cursor-pointer">
                    {t("student.booktraillesson.group")}
                  </label>
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium">
                {" "}
                {t("student.booktraillesson.duration")}{" "}
              </p>
              <p className="cursor-not-allowed">
                {" "}
                {t("student.booktraillesson.min")}
              </p>
            </div>
          </div>
        </div>
        <div className="py-4">
          <p className="text-sm flex gap-1 items-center">
            <FaQuestion className="w-4 h-4" />
            <span className="font-semibold">Tip:</span>
            <span className="text-gray-500">
              {t("student.booktraillesson.use_trail_session")}
            </span>
          </p>
        </div>
        <div className="w-fit ml-auto flex gap-3">
          {isSchedulingLesson ? (
            <Spinner />
          ) : (
            <LearnosoButton
              title={t("student.booktraillesson.finish")}
              action={() => {
                console.log("Clicked me");
                finishLessonSetup();
              }}
              disabled={!selectedCourse || !selectedDate || !selectedTime}
              type="button"
            />
          )}
          {/* <LearnosoButton
            title="Continue"
            action={() =>
              navigate(
                `/student/tutors/${id}/checkout-trial-lesson/?type=${selectedType}&time=${encodeURI(selectedTime)}&date=${encodeURI(new Date(`${selectedDate}`).toDateString())}`
              )
            }
          /> */}
        </div>
      </div>
    </div>
  );
};

export default BookTrialLesson;
