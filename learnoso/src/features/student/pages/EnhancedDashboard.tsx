import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { useToast } from '@/components/ui/Toast';
import { enhancedLessonService, Lesson } from '@/app/services/lesson/enhanced-lesson.service';
import { enhancedPaymentService, WalletBalance } from '@/app/services/payment/enhanced-payment.service';
import { 
  Calendar, 
  Clock, 
  BookOpen, 
  TrendingUp, 
  Wallet, 
  MessageSquare,
  Video,
  Star,
  Plus
} from 'lucide-react';

interface DashboardStats {
  totalLessons: number;
  completedLessons: number;
  upcomingLessons: number;
  totalSpent: number;
  averageRating: number;
}

export const EnhancedStudentDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalLessons: 0,
    completedLessons: 0,
    upcomingLessons: 0,
    totalSpent: 0,
    averageRating: 0,
  });
  const [upcomingLessons, setUpcomingLessons] = useState<Lesson[]>([]);
  const [recentLessons, setRecentLessons] = useState<Lesson[]>([]);
  const [walletBalance, setWalletBalance] = useState<WalletBalance | null>(null);
  const [loading, setLoading] = useState(true);
  const { error } = useToast();

  // Mock user data - in real app, this would come from auth context
  const user = {
    id: 1,
    first_name: 'John',
    last_name: 'Doe',
    email: '<EMAIL>',
    profile_image: undefined,
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load lessons
      const lessonsResponse = await enhancedLessonService.getStudentLessons(1, 'scheduled');
      if (lessonsResponse.success && lessonsResponse.data) {
        const lessons = lessonsResponse.data.data;
        setUpcomingLessons(lessons.slice(0, 3));
        
        // Calculate stats
        setStats(prev => ({
          ...prev,
          upcomingLessons: lessons.length,
          totalLessons: lessonsResponse.data.total,
        }));
      }

      // Load recent completed lessons
      const recentResponse = await enhancedLessonService.getStudentLessons(1, 'completed');
      if (recentResponse.success && recentResponse.data) {
        setRecentLessons(recentResponse.data.data.slice(0, 3));
        setStats(prev => ({
          ...prev,
          completedLessons: recentResponse.data.total,
        }));
      }

      // Load wallet balance
      const walletResponse = await enhancedPaymentService.getWalletBalance();
      if (walletResponse.success && walletResponse.data) {
        setWalletBalance(walletResponse.data);
      }

    } catch (err) {
      error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const completionRate = stats.totalLessons > 0 ? (stats.completedLessons / stats.totalLessons) * 100 : 0;

  return (
    <DashboardLayout userRole="student" user={user}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Welcome back, {user.first_name}!
            </h1>
            <p className="text-gray-600 mt-1">
              Ready to continue your learning journey?
            </p>
          </div>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Book a Lesson
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Lessons</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.totalLessons}</p>
                </div>
                <div className="p-3 bg-blue-100 rounded-full">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.completedLessons}</p>
                </div>
                <div className="p-3 bg-green-100 rounded-full">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Upcoming</p>
                  <p className="text-3xl font-bold text-gray-900">{stats.upcomingLessons}</p>
                </div>
                <div className="p-3 bg-yellow-100 rounded-full">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Wallet Balance</p>
                  <p className="text-3xl font-bold text-gray-900">
                    ${walletBalance?.balance || 0}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-full">
                  <Wallet className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progress Section */}
        <Card>
          <CardHeader>
            <CardTitle>Learning Progress</CardTitle>
            <CardDescription>
              Your overall learning completion rate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Completion Rate</span>
                <span className="text-sm text-gray-600">{completionRate.toFixed(1)}%</span>
              </div>
              <Progress value={completionRate} className="h-2" />
              <p className="text-sm text-gray-600">
                You've completed {stats.completedLessons} out of {stats.totalLessons} lessons
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upcoming Lessons */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Upcoming Lessons
              </CardTitle>
              <CardDescription>
                Your scheduled lessons for this week
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingLessons.length > 0 ? (
                  upcomingLessons.map((lesson) => (
                    <div key={lesson.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={lesson.tutor?.profile_image} />
                          <AvatarFallback>
                            {lesson.tutor?.first_name?.[0]}{lesson.tutor?.last_name?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{lesson.title}</p>
                          <p className="text-sm text-gray-600">
                            with {lesson.tutor?.first_name} {lesson.tutor?.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatDate(lesson.scheduled_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(lesson.status)}>
                          {lesson.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Video className="h-4 w-4 mr-1" />
                          Join
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    No upcoming lessons scheduled
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Recent Activity
              </CardTitle>
              <CardDescription>
                Your recently completed lessons
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentLessons.length > 0 ? (
                  recentLessons.map((lesson) => (
                    <div key={lesson.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage src={lesson.tutor?.profile_image} />
                          <AvatarFallback>
                            {lesson.tutor?.first_name?.[0]}{lesson.tutor?.last_name?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{lesson.title}</p>
                          <p className="text-sm text-gray-600">
                            with {lesson.tutor?.first_name} {lesson.tutor?.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {formatDate(lesson.scheduled_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-sm">4.8</span>
                        </div>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    No recent lessons found
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};
