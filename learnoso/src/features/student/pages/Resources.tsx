import { t } from "i18next";
import React from "react";
import {
  FaBook,
  FaDownload,
  FaFileAlt,
  FaFlask,
  FaLink,
  FaVideo,
} from "react-icons/fa";
import { toast } from "react-toastify";

type ResourceType = {
  title: string;
  description: string;
  icon: any;
  link: string;
  linkText: string;
};

const ResourceCard = ({
  title,
  description,
  icon: Icon,
  link,
  linkText,
}: ResourceType) => {
  const comingSoon = () => toast.warn("Resource not published..");
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 flex flex-col h-full transition-transform duration-300 hover:scale-105">
      <div className="flex items-center mb-4">
        <Icon className="w-8 h-8 text-primary mr-4" />
        <h3 className="text-xl font-semibold text-black">{title}</h3>
      </div>
      <p className="text-dark mb-4 flex-grow text-sm">{description}</p>
      <a
        href={link}
        onClick={comingSoon}
        className="inline-flex items-center text-primary hover:text-orange-500 transition-colors duration-200"
      >
        {linkText}
        <FaLink className="w-4 h-4 ml-2" />
      </a>
    </div>
  );
};

export default function ResourcesPage() {
  const resources = [
    {
      title: t("student.resources.study_guides"),
      description: t("student.resources.comprehensive_materials"),
      icon: FaFileAlt,
      link: "#",
      linkText: t("student.resources.browse_study_guides"),
    },
    {
      title: t("student.resources.video_tutorials"),
      description: t("student.resources.in_depth_video"),
      icon: FaVideo,
      link: "#",
      linkText: t("student.resources.watch_tutorials"),
    },
    {
      title: t("student.resources.practice_test"),
      description: t("student.resources.prepare_for_your_exams"),
      icon: FaFlask,
      link: "#",
      linkText: "Take Practice Tests",
    },
    {
      title: t("student.resources.reccomended_books"),
      description: t("student.resources.curated_list_of_textbooks"),
      icon: FaBook,
      link: "#",
      linkText: t("student.resources.view_book_list"),
    },
    {
      title: t("student.resources.downloadable_worksheets"),
      description: t("student.resources.printable_worksheets"),
      icon: FaDownload,
      link: "#",
      linkText: t("student.resources.download_worksheet"),
    },
    {
      title: t("student.resources.online_forum"),
      description: t("student.resources.connect_with_other_students"),
      icon: FaLink,
      link: "#",
      linkText: t("student.resources.join_discussions"),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-primary py-8">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl font-bold text-white">
            {" "}
            {t("student.resources.student_resources")}{" "}
          </h1>
          <p className="text-lg text-white mt-4">
            {t("student.resources.explore_a_variety_of_materials")}
          </p>
        </div>
      </header>
      <main className="container mx-auto px-4 py-12">
        <p className="text-dark mb-8 max-w-3xl mx-auto text-center">
          {t("student.resources.welcome_to_our_comprehensive_resource_center")}
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {resources.map((resource, index) => (
            <ResourceCard key={index} {...resource} />
          ))}
        </div>
      </main>
      <footer className="bg-primary py-6 mt-12">
        <div className="container mx-auto px-4 text-center text-white">
          <p>
            &copy; {new Date().getFullYear()} {t("student.resources.learnoso")}
          </p>
        </div>
      </footer>
    </div>
  );
}
