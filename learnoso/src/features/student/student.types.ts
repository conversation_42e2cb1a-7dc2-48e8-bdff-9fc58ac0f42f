export interface CardPaymentFormData {
  "payment-method": PaymentMethodType;
  "card-number": string;
  mm: number;
  yy: number;
  cvc: number;
  "save-card": boolean;
}

export interface MobileMoneyFormData {
  "payment-method": PaymentMethodType;
  "network-provider": "mtn" | "orange";
  "account-number": string;
}

export type TrialLessonStatus =
  | "pending"
  | "processing"
  | "successfull"
  | "failed";

export type PaymentMethodType = "master-card" | "mobile-money";
