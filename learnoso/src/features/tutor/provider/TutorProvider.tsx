import React from "react";
export interface ITutorContext {
  isEditingComponent: boolean;
  setIsEditingComponent: React.Dispatch<React.SetStateAction<boolean>>;
}

export const TutorContext = React.createContext<ITutorContext>({
  isEditingComponent: false,
  setIsEditingComponent: () => {},
});

function TutorProvider({ children }: { children: React.ReactNode }) {
  const [isEditingComponent, setIsEditingComponent] =
    React.useState<boolean>(false);
  return (
    <TutorContext.Provider
      value={{ isEditingComponent, setIsEditingComponent }}
    >
      {children}
    </TutorContext.Provider>
  );
}

export default TutorProvider;
