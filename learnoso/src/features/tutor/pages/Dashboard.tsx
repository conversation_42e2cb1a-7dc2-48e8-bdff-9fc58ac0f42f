import { useGetTutorStatsQuery } from "@/app/services/tutor/tutor.service";
import { useAuth } from "@/hooks";
import { motion } from "framer-motion"; // Import framer motion
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON>lock,
  FaDollarSign,
  FaUsers,
} from "react-icons/fa";
import { DashboardCard } from "../components/molecules/DashboardCard";
import DashboardHeader from "../components/molecules/DashboardHeader";
import LessonTable from "../components/molecules/LessonTable";
import Payments from "../components/molecules/Payments";
import Upcoming from "../components/molecules/Upcoming";
import { DashboardCardType } from "../types";
import { useTranslation } from "react-i18next";
// import {t} from "i18next";

export const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1, // Delay each card slightly for a stagger effect
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

const tableVariants = {
  hidden: { opacity: 0, x: -50 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const sidebarVariants = {
  hidden: { opacity: 0, x: 50 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const TutorDashboard = () => {
  const [cardItems, setCardItems] = useState<DashboardCardType[]>([]);
  const { user } = useAuth();

  const {
    data,
    status: tutorQueryStatus,
    isLoading: isLoadingDashboardStats,
  } = useGetTutorStatsQuery(user?.tutor?.id);

  useEffect(() => {
    if (tutorQueryStatus === "fulfilled") {
      const stats = data?.data;
      setCardItems([
        {
          icon: <FaDollarSign size={24} />,
          title: t("tutors.dashboard.total_income"),
          value: new Number(stats?.totalIncome).toLocaleString(),
          change: 30,
          changePeriod: "30 Days",
          isCta: true,
        },
        {
          icon: <FaUsers size={24} />,
          title: t("tutors.dashboard.active_student"),
          value: stats?.activeStudents,
          change: 30,
          changePeriod: "30 Days",
        },
        {
          icon: <FaBookOpen size={24} />,
          title: t("tutors.dashboard.total_lesson"),
          value: stats?.totalLessons,
          change: 30,
          changePeriod: "30 Days",
        },
        {
          icon: <FaClock size={24} />,
          title: t("tutors.dashboard.total_hour"),
          value: stats?.totalHours,
          change: -30,
          changePeriod: "30 Days",
        },
        {
          icon: <FaBookReader size={24} />,
          title: t("tutors.dashboard.total_session"),
          value: stats.totalSessions,
          change: -30,
          changePeriod: "30 Days",
        },
      ]);

      console.log(data);
    }
  }, [tutorQueryStatus]);

  const { t } = useTranslation();
  return (
    <main className="">
      <DashboardHeader />

      {/* Cards Section */}
      <div className="flex flex-wrap gap-4 mb-8">
        {cardItems.map((item, i) => (
          <motion.div
            key={item.title}
            custom={i}
            initial="hidden"
            animate="visible"
            variants={cardVariants}
          >
            <DashboardCard {...item} />
          </motion.div>
        ))}
      </div>

      <div className="flex gap-8">
        {/* Lesson Table with Slide-In */}
        <motion.div
          className="flex-grow"
          initial="hidden"
          animate="visible"
          variants={tableVariants}
        >
          <LessonTable />
        </motion.div>

        {/* Sidebar (Payments & Upcoming) with Slide-In */}
        <motion.div
          className="w-80"
          initial="hidden"
          animate="visible"
          variants={sidebarVariants}
        >
          <Payments />
          <Upcoming />
        </motion.div>
      </div>
    </main>
  );
};

export default TutorDashboard;
