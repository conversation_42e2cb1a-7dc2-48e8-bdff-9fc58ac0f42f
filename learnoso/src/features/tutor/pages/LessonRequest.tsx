import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import {
  FaArrowLeft,
  FaBook,
  FaCalendar,
  FaClock,
  FaGlobe,
  FaStopwatch,
  FaVideo,
} from "react-icons/fa";

const LessonRequestDetails = () => {
  return (
    <div className="bg-slate-100 p-6 min-h-screen">
      <motion.div
        className="max-w-4xl mx-auto"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <header className="flex items-center justify-between mb-6">
          <motion.div
            className="flex items-center"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <FaArrowLeft className="mr-2 cursor-pointer" />
            <h1 className="text-xl font-semibold">
              {" "}
              {t("tutors.lessonrequest.lesson_request")}
            </h1>
          </motion.div>
          <motion.button
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            + {t("tutors.lessonrequest.message_student")}
          </motion.button>
        </header>

        <motion.div
          className="bg-white rounded-lg shadow-md p-6 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="flex items-start">
            <img
              src="https://i.pravatar.cc/100?img=38"
              alt="Staphanie Jones"
              className="w-24 h-24 rounded-lg mr-6"
            />
            <div className="flex-grow">
              <h2 className="text-xl font-semibold mb-2">
                {" "}
                Staphanie Jones {/* {t("tutors.lessonrequest.staphanie")} */}
              </h2>
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center">
                  <FaBook className="mr-2 text-gray-500" />
                  <span>{t("tutors.lessonrequest.course")}</span>
                </div>
                <div className="flex items-center">
                  <FaClock className="mr-2 text-gray-500" />
                  <span>
                    {t("tutors.lessonrequest.hours", { value: `02` })}
                  </span>
                </div>
                <div className="flex items-center">
                  <FaCalendar className="mr-2 text-gray-500" />
                  <span>Date: 09/09/2024</span>
                </div>
                <div className="flex items-center">
                  <FaStopwatch className="mr-2 text-gray-500" />
                  <span>
                    {t("tutors.lessonrequest.time", { value: `7:30` })}
                  </span>
                </div>
                <div className="flex items-center">
                  <FaGlobe className="mr-2 text-gray-500" />
                  <span>{t("tutors.lessonrequest.language")}</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold mb-1">$24</div>
              <div className="text-sm text-gray-500">
                {t("tutors.lessonrequest.paid")}
              </div>
            </div>
          </div>
          <div className="mt-6 flex justify-end space-x-4">
            <motion.button
              className="flex items-center justify-between gap-2 bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t("tutors.lessonrequest.start_lesson")}
              <FaVideo className="relative -bottom-[2px]" />
            </motion.button>
            <motion.button
              className="border border-slate-300 text-dark px-6 py-2 rounded-md hover:bg-slate-100"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {t("tutors.lessonrequest.decline")}
            </motion.button>
          </div>
        </motion.div>

        <motion.div
          className="mb-6 flex items-center justify-between"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="text-xl font-semibold">
            {t("tutors.lessonrequest.curriculum")}
          </h2>
          <button className="text-blue-600 hover:underline border border-blue-600 rounded-md p-2">
            {t("tutors.lessonrequest.customize_curriculum")}
          </button>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          {[
            t("tutors.lessonrequest.beginner"),
            t("tutors.lessonrequest.intermediate"),
            t("tutors.lessonrequest.advance"),
          ].map((level, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-lg shadow-md p-6"
              whileHover={{ scale: 1.02 }}
            >
              <h3 className="text-lg font-semibold mb-4">{level}</h3>
              <ul className="space-y-2">
                {level === t("tutors.lessonrequest.beginner")
                  ? [
                      t("tutors.lessonrequest.introduction_to_physics"),
                      t("tutors.lessonrequest.mechanics"),
                      t("tutors.lessonrequest.thermodynamics"),
                      t("tutors.lessonrequest.wave"),
                      t("tutors.lessonrequest.electricity_and_megnetism"),
                    ].map((topic, i) => (
                      <li key={i} className="flex items-center">
                        <svg
                          className="w-5 h-5 mr-2 text-green-500"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        {topic}
                      </li>
                    ))
                  : [
                      t("tutors.lessonrequest.advance_mechanics"),
                      t("tutors.lessonrequest.electromagnetic"),
                      t("tutors.lessonrequest.optics_physics"),
                      t("tutors.lessonrequest.thermodynamics_statistical"),
                      t("tutors.lessonrequest.nuclear_particles"),
                    ].map((topic, i) => (
                      <li key={i} className="flex items-center">
                        {i + 1}. {topic}
                      </li>
                    ))}
              </ul>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default LessonRequestDetails;
