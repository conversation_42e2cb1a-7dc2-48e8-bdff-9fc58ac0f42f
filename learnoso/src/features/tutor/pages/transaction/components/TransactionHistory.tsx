import { useGetTransactionsQuery } from "@/app/services/payment/payment.service";
import { Spinner } from "@/components/atoms";
import { DateWizard } from "@/lib/util";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { FaArrowDown, FaArrowUp, FaSearch } from "react-icons/fa";
import { FaCircleChevronLeft, FaCircleChevronRight } from "react-icons/fa6";

interface Transaction {
  id: string;
  type: "payment" | "withdraw" | "deposit";
  amount: number;
  currency: string;
  date: string;
  status: "confirmed" | "pending" | "failed";
  description: string;
}

const TransactionHistory: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState<
    "all" | "confirmed" | "withdraw" | "deposits"
  >("all");
  const [currentPage, setCurrentPage] = useState(1);
  const transactionsPerPage = 10;

  const {
    data: transactionHistory,
    status: transactionQueryStatus,
    isLoading: isTransactionsLoading,
  } = useGetTransactionsQuery("transactions");

  const [transactions, setTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    switch (transactionQueryStatus) {
      case "fulfilled":
        const _ = transactionHistory?.data?.map((transaction: any) => ({
          id: transaction.id,
          type: transaction.type,
          amount: +transaction.amount,
          currency: "USD",
          date: DateWizard.toLocaleDateTime(transaction.created_at),
          status: transaction.status,
          description: transaction.description || "N/A",
        }));
        setTransactions(_ as Transaction[]);
        break;
      case "rejected":
        console.error("Failed to fetch transactions");
        break;
      default:
        break;
    }
  }, [transactionQueryStatus]);

  const filteredTransactions = transactions.filter((transaction) => {
    const matchesSearch = transaction.description
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    const matchesFilter =
      filter === "all" || transaction.type === filter.slice(0, -1); // Remove 's' from filter
    return matchesSearch && matchesFilter;
  });

  const indexOfLastTransaction = currentPage * transactionsPerPage;
  const indexOfFirstTransaction = indexOfLastTransaction - transactionsPerPage;
  const currentTransactions = filteredTransactions.slice(
    indexOfFirstTransaction,
    indexOfLastTransaction,
  );

  const totalPages = Math.ceil(
    filteredTransactions.length / transactionsPerPage,
  );

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4">
        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Search transactions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
        </div>
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value as any)}
          className="w-full md:w-auto px-4 py-2 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
        >
          <option value="all">
            {t("tutors.transaction.transactionhistory.all_transaction")}
          </option>
          <option value="withdraws">
            {t("tutors.transaction.transactionhistory.withdrawals")}
          </option>
          <option value="deposits">
            {t("tutors.transaction.transactionhistory.deposit")}
          </option>
        </select>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="bg-light-gray">
              <th className="px-4 py-2 text-left">
                {t("tutors.transaction.transactionhistory.date")}
              </th>
              <th className="px-4 py-2 text-left">
                {t("tutors.transaction.transactionhistory.type")}
              </th>
              <th className="px-4 py-2 text-left">
                {t("tutors.transaction.transactionhistory.amount")}
              </th>
              <th className="px-4 py-2 text-left">
                {t("tutors.transaction.transactionhistory.status")}
              </th>
              <th className="px-4 py-2 text-left">
                {t("tutors.transaction.transactionhistory.description")}
              </th>
            </tr>
          </thead>
          <tbody>
            {isTransactionsLoading ? (
              <Spinner />
            ) : (
              currentTransactions.map((transaction) => (
                <tr key={transaction.id} className="border-b border-gray/30">
                  <td className="px-4 py-2">{transaction.date}</td>
                  <td className="px-4 py-2">
                    <span
                      className={`flex items-center ${
                        transaction.type === "deposit"
                          ? "text-green-500"
                          : transaction.type === "withdraw"
                            ? "text-red-500"
                            : "text-blue"
                      }`}
                    >
                      {transaction.type === "payment" && (
                        <FaArrowDown className="mr-2" />
                      )}
                      {transaction.type === "withdraw" && (
                        <FaArrowUp className="mr-2" />
                      )}
                      {transaction.type === "deposit" && (
                        <FaArrowDown className="mr-2" />
                      )}
                      {transaction.type.charAt(0).toUpperCase() +
                        transaction.type.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-2">
                    {transaction.currency}{" "}
                    {transaction.amount.toFixed(2).toLocaleString()}
                  </td>
                  <td className="px-4 py-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        transaction.status === "confirmed"
                          ? "bg-green-500/20 text-green-500"
                          : transaction.status === "pending"
                            ? "bg-yellow-500/20 text-yellow-500"
                            : "bg-red-500/20 text-red-500"
                      }`}
                    >
                      {transaction.status.charAt(0).toUpperCase() +
                        transaction.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-4 py-2">{transaction.description}</td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end items-center mt-4 space-x-4">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FaCircleChevronLeft size={24} />
        </button>
        <span>
          {t("tutors.transaction.transactionhistory.page")} {currentPage} of{" "}
          {totalPages}
        </span>
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <FaCircleChevronRight size={24} />
        </button>
      </div>
    </div>
  );
};

export default TransactionHistory;
