import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  FaCheckCircle,
  FaMobileAlt,
  FaPaypal,
  FaUniversity,
} from "react-icons/fa";

type WithdrawalMethod = "bank" | "paypal" | "mobile-money";

const WithdrawFunds: React.FC = () => {
  const [withdrawalMethod, setWithdrawalMethod] =
    useState<WithdrawalMethod>("bank");
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isValid },
  } = useForm({
    mode: "onChange",
  });

  const onSubmit = (data: any) => {
    console.log("Withdrawal request:", { method: withdrawalMethod, ...data });
    // Here you would typically send this data to your API
  };
  const { t } = useTranslation();
  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <label className="block text-dark font-semibold mb-3">
          {t("tutors.transaction.withdrawfunds.select_withdrawal")}
        </label>
        <div className="flex gap-4">
          {["bank", "paypal", "mobile-money"].map((method) => (
            <button
              key={method}
              type="button"
              onClick={() => setWithdrawalMethod(method as WithdrawalMethod)}
              className={`
                flex-1 flex items-center justify-center p-4 rounded-lg border-2
                ${withdrawalMethod === method ? "border-primary bg-primary/5" : "border-gray/30"}
                transition-all duration-300 ease-in-out
              `}
            >
              {method === "bank" && (
                <FaUniversity
                  className={`text-2xl ${withdrawalMethod === method ? "text-primary" : "text-gray-500"}`}
                />
              )}
              {method === "paypal" && (
                <FaPaypal
                  className={`text-2xl ${withdrawalMethod === method ? "text-primary" : "text-gray-500"}`}
                />
              )}
              {method === "mobile-money" && (
                <FaMobileAlt
                  className={`text-2xl ${withdrawalMethod === method ? "text-primary" : "text-gray-500"}`}
                />
              )}
              <span
                className={`ml-2 font-medium capitalize ${withdrawalMethod === method ? "text-primary" : "text-gray-500"}`}
              >
                {method.replace("-", " ")}
              </span>
            </button>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        {withdrawalMethod === "bank" && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-dark font-semibold">
                  {t("tutors.transaction.withdrawfunds.bank_name")}
                </label>
                <input
                  {...register("bankName", {
                    required: "Bank name is required",
                  })}
                  className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                  placeholder="Enter bank name"
                />
                {errors.bankName && (
                  // @ts-ignore
                  <p className="text-red-500 text-sm">
                    {/*   @ts-ignore */}
                    {errors.bankName.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <label className="block text-dark font-semibold">
                  {t("tutors.transaction.withdrawfunds.account_number")}
                </label>
                <input
                  {...register("accountNumber", {
                    required: "Account number is required",
                  })}
                  className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                  placeholder="Enter account number"
                />
                {errors.accountNumber && (
                  <p className="text-red-500 text-sm">
                    {/*   @ts-ignore */}
                    {errors.accountNumber.message}
                  </p>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <label className="block text-dark font-semibold">
                {t("tutors.transaction.withdrawfunds.account_holder_name")}
              </label>
              <input
                {...register("accountName", {
                  required: "Account holder name is required",
                })}
                className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                placeholder="Enter account holder name"
              />
              {errors.accountName && (
                // @ts-ignore
                <p className="text-red-500 text-sm">
                  {/* @ts-ignore */}
                  {errors.accountName.message}
                </p>
              )}
            </div>
          </>
        )}

        {withdrawalMethod === "paypal" && (
          <div className="space-y-2">
            <label className="block text-dark font-semibold">
              {t("tutors.transaction.withdrawfunds.paypal_email")}
            </label>
            <input
              {...register("paypalEmail", {
                required: "PayPal email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
              className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
              placeholder="Enter PayPal email"
            />
            {errors.paypalEmail && (
              // @ts-ignore
              <p className="text-red-500 text-sm">
                {/* @ts-ignore */}
                {errors.paypalEmail.message}
              </p>
            )}
          </div>
        )}

        {withdrawalMethod === "mobile-money" && (
          <>
            <div className="space-y-2">
              <label className="block text-dark font-semibold">
                {t("tutors.transaction.withdrawfunds.mobile_number")}
              </label>
              <input
                {...register("mobileNumber", {
                  required: "Mobile number is required",
                  pattern: {
                    value: /^[0-9]{10}$/,
                    message: "Invalid mobile number",
                  },
                })}
                className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
                placeholder="Enter mobile number"
              />
              {errors.mobileNumber && (
                <p className="text-red-500 text-sm">
                  {/* @ts-ignore */}
                  {errors.mobileNumber.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <label className="block text-dark font-semibold">Provider</label>
              <select
                {...register("mobileProvider", {
                  required: "Provider is required",
                })}
                className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
              >
                <option value="">
                  {t("tutors.transaction.withdrawfunds.select_provider")}{" "}
                </option>
                <option value="mtn">
                  {t("tutors.transaction.withdrawfunds.mtn")}{" "}
                </option>
                <option value="orange">
                  {t("tutors.transaction.withdrawfunds.orange")}
                </option>
                {/* Add more providers as needed */}
              </select>
              {errors.mobileProvider && (
                <p className="text-red-500 text-sm">
                  {/* @ts-ignore */}
                  {errors.mobileProvider.message}
                </p>
              )}
            </div>
          </>
        )}

        <div className="space-y-2">
          <label className="block text-dark font-semibold">
            {t("tutors.transaction.withdrawfunds.amount_to_withdraw")}
          </label>
          <input
            {...register("amount", {
              required: "Amount is required",
              min: {
                value: 10,
                message: "Minimum withdrawal amount is 10",
              },
            })}
            type="number"
            className="w-full px-4 py-3 border border-gray/30 rounded-lg focus:ring-2 focus:ring-primary/50 outline-none"
            placeholder="Enter amount"
          />
          {errors.amount && (
            // @ts-ignore
            <p className="text-red-500 text-sm">{errors.amount.message}</p>
          )}
        </div>
      </div>

      <button
        type="submit"
        disabled={!isValid}
        className="
          w-full py-4 bg-green text-white rounded-lg 
          hover:bg-green-500/90 transition-colors 
          flex items-center justify-center space-x-2
          disabled:opacity-50 disabled:cursor-not-allowed
        "
      >
        <FaCheckCircle />
        <span> {t("tutors.transaction.withdrawfunds.request_withdrawal")}</span>
      </button>
    </form>
  );
};

export default WithdrawFunds;
