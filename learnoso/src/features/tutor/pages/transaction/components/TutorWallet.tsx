import { useGetWalletBalanceQuery } from "@/app/services/payment/payment.service";
import { Spinner } from "@/components/atoms";
import React, { useEffect, useState } from "react";
import { FaFileInvoiceDollar, FaWallet } from "react-icons/fa";
import { FaHandHoldingDollar } from "react-icons/fa6";
import TransactionHistory from "./TransactionHistory";
import WithdrawFunds from "./WithdrawFunds";
import { useTranslation } from "react-i18next";

const TutorWallet: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<"withdraw" | "history">("history");
  const {
    data: balance,
    isLoading: isWalletBalanceLoading,
    status: tutorWalletBalanceQueryStatus,
  } = useGetWalletBalanceQuery("wallet balance");
  // Mock data for wallet balance
  const [walletBalance, setWalletBalance] = useState({
    currency: "",
    balance: 0,
  });

  useEffect(() => {
    switch (tutorWalletBalanceQueryStatus) {
      case "fulfilled":
        setWalletBalance({
          currency: balance?.data?.currency || "USD",
          balance: balance?.data?.balance || 0,
        });
        break;
      default:
        setWalletBalance({ currency: "USD", balance: 0 });
        break;
    }
  }, [tutorWalletBalanceQueryStatus]);

  return (
    <div className="min-h-screen bg-light-gray p-8">
      <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden">
        {/* Header Section */}
        <div className="bg-primary text-white p-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">
              {t("tutors.transaction.tutorwallet.your_wallet")}
            </h1>
            <p className="text-sm opacity-80">
              {t("tutors.transaction.tutorwallet.manage_earning")}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <FaWallet className="text-3xl" />
            {isWalletBalanceLoading ? (
              <Spinner />
            ) : (
              <div>
                <p className="font-semibold text-lg">
                  {walletBalance.currency?.toLocaleUpperCase()}{" "}
                  {new Number(walletBalance.balance).toLocaleString()}
                </p>
                <p className="text-xs opacity-70">
                  {t("tutors.transaction.tutorwallet.current_balance")}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Tab Selector */}
        <div className="flex border-b p-4 bg-light-gray">
          <button
            onClick={() => setActiveTab("withdraw")}
            className={`
              flex-1 flex justify-center items-center gap-2 py-3 px-4 text-center font-medium
              ${activeTab === "withdraw" ? "bg-primary text-white" : "bg-white text-dark hover:bg-light-gray"}
              transition-all duration-300 ease-in-out
            `}
          >
            <FaHandHoldingDollar size={24} />
            {t("tutors.transaction.tutorwallet.withdraw_fund")}
          </button>
          <button
            onClick={() => setActiveTab("history")}
            className={`
              flex-1 flex justify-center items-center gap-2 py-3 px-4 text-center font-medium
              ${activeTab === "history" ? "bg-primary text-white" : "bg-white text-dark hover:bg-light-gray"}
              transition-all duration-300 ease-in-out
            `}
          >
            <FaFileInvoiceDollar size={24} />
            {t("tutors.transaction.tutorwallet.transaction_history")}
          </button>
        </div>

        {/* Content Section */}
        <div className="p-8">
          {activeTab === "withdraw" ? (
            <WithdrawFunds />
          ) : (
            <TransactionHistory />
          )}
        </div>
      </div>
    </div>
  );
};

export default TutorWallet;
