import dayjs from "dayjs";
import { t } from "i18next";
import React from "react";

type LessonType = {
  date: string;
  agenda: "Lesson" | "Session";
  course: string;
  hours: number;
  status: "Complete" | "Pending" | "Missed";
};

const lessons: LessonType[] = [
  {
    date: "2023-11-17",
    agenda: "Lesson",
    course: "Web Development",
    hours: 3,
    status: "Complete",
  },
  {
    date: "2023-12-05",
    agenda: "Session",
    course: "Data Science",
    hours: 2,
    status: "Pending",
  },
  {
    date: "2024-01-12",
    agenda: "Lesson",
    course: "Machine Learning",
    hours: 4,
    status: "Missed",
  },
  {
    date: "2024-02-19",
    agenda: "Session",
    course: "Artificial Intelligence",
    hours: 3,
    status: "Complete",
  },
  {
    date: "2024-03-26",
    agenda: "Lesson",
    course: "Software Engineering",
    hours: 2,
    status: "Pending",
  },
  {
    date: "2024-04-09",
    agenda: "Session",
    course: "Cybersecurity",
    hours: 4,
    status: "Complete",
  },
  {
    date: "2024-05-16",
    agenda: "Lesson",
    course: "Cloud Computing",
    hours: 3,
    status: "Missed",
  },
  {
    date: "2024-06-23",
    agenda: "Session",
    course: "Database Management",
    hours: 2,
    status: "Pending",
  },
];

const ListView: React.FC = () => {
  return (
    <div className="pl-4 py-4">
      <table className="w-full">
        <thead className="bg-[#F1F1F4]">
          <tr>
            <th className="p-4 text-center w-20">
              {t("tutors.calender.listview.date")}{" "}
            </th>
            <th className="p-4 text-left w-28">
              {t("tutors.calender.listview.agenda")}{" "}
            </th>
            <th className="p-4 px-5 text-left">
              {t("tutors.calender.listview.course")}{" "}
            </th>
            <th className="p-4 text-center w-32">
              {t("tutors.calender.listview.hours")}{" "}
            </th>
            <th className="p-4 text-center w-36">
              {t("tutors.calender.listview.status")}{" "}
            </th>
          </tr>
        </thead>
        <tbody className="text-sm">
          {lessons.map((lesson, index) => (
            <Lesson key={index} {...lesson} />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ListView;

const Lesson: React.FC<LessonType> = ({
  date,
  agenda,
  course,
  hours,
  status,
}) => {
  return (
    <tr>
      <td className="p-4 py-2 border-[#F2F1F2] border-b flex flex-col items-center">
        <span>{dayjs(date).format("ddd")}</span>
        <span>{dayjs(date).format("D")}</span>
      </td>
      <td className="p-4 py-2 border-[#F2F1F2] border-b">{agenda}</td>
      <td className="p-4 py-2 border-[#F2F1F2] border-b">{course}</td>
      <td className="p-4 py-2 border-[#F2F1F2] border-b text-center">
        {hours}
      </td>
      <td className="p-4 py-2 border-[#F2F1F2] border-b text-center">
        {status}
      </td>
    </tr>
  );
};
