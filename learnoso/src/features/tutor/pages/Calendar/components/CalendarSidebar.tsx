import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import { t } from "i18next";

const tabs: string[] = ["All", "Sessions", "Lessons"];

const activities: {
  date: string;
  student: string;
  course: string;
  startTime: string;
  endTime: string;
  completed: boolean;
}[] = [
  {
    date: "2023-11-17",
    student: "<PERSON>",
    course: t("tutors.calender.calendersidebar.web_development"),
    startTime: "9:00 AM",
    endTime: "11:00 AM",
    completed: true,
  },
  {
    date: "2023-12-05",
    student: "<PERSON>",
    course: t("tutors.calender.calendersidebar.data_science"),
    startTime: "10:00 AM",
    endTime: "12:00 PM",
    completed: false,
  },
  {
    date: "2024-01-12",
    student: "<PERSON>",
    course: t("tutors.calender.calendersidebar.machine_learning"),
    startTime: "11:00 AM",
    endTime: "1:00 PM",
    completed: true,
  },
  {
    date: "2024-02-19",
    student: "<PERSON>",
    course: t("tutors.calender.calendersidebar.artificial_intelligence"),
    startTime: "12:00 PM",
    endTime: "2:00 PM",
    completed: false,
  },
  {
    date: "2024-03-26",
    student: "Eve",
    course: t("tutors.calender.calendersidebar.software_engineering"),
    startTime: "1:00 PM",
    endTime: "3:00 PM",
    completed: true,
  },
  {
    date: "2024-03-26",
    student: "Eve",
    course: t("tutors.calender.calendersidebar.software_engineering"),
    startTime: "1:00 PM",
    endTime: "3:00 PM",
    completed: true,
  },
  {
    date: "2024-03-26",
    student: "Eve",
    course: t("tutors.calender.calendersidebar.software_engineering"),
    startTime: "1:00 PM",
    endTime: "3:00 PM",
    completed: true,
  },
  {
    date: "2024-03-26",
    student: "Eve",
    course: t("tutors.calender.calendersidebar.software_engineering"),
    startTime: "1:00 PM",
    endTime: "3:00 PM",
    completed: true,
  },
  {
    date: "2024-03-26",
    student: "Eve",
    course: t("tutors.calender.calendersidebar.software_engineering"),
    startTime: "1:00 PM",
    endTime: "3:00 PM",
    completed: true,
  },
];

const CalendarSidebar: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const { t } = useTranslation();
  return (
    <div className="w-[328px] min-w-[328px] max-h-screen flex flex-col border-l border-gray/20">
      <div className="bg-red-500 flex pb-[0.5px] min-h-12">
        {tabs.map((tab, index) => (
          <div
            key={index}
            className={`flex-1 flex items-center justify-center text-white cursor-pointer ${
              selectedTab === index ? "border-b-2 border-white" : ""
            }`}
            onClick={() => {
              setSelectedTab(index);
            }}
          >
            {tab}
          </div>
        ))}
      </div>
      <div className="flex gap-4 px-4 py-3 items-center justify-between border-b border-[#F2F1F2]">
        <h2 className="font-bold">
          {t("tutors.calender.calendersidebar.recent_activities")}{" "}
        </h2>
        {/* <button className="bg-white hover:bg-red-5005 py-2.5 px-6 rounded-md text-lg font-semibold transition duration-300 ease-in-out hover:scale-105 border border-red text-red-500">
          Download
        </button> */}
      </div>
      <div className="flex-1 overflow-y-scroll">
        {/* {activities.map((activity, index) => (
          <Activity key={index} {...activity} />
        ))} */}
        <p className="px-2 text-grey">
          {t("tutors.calender.calendersidebar.no_activity")}
        </p>
      </div>
    </div>
  );
};

export default CalendarSidebar;
