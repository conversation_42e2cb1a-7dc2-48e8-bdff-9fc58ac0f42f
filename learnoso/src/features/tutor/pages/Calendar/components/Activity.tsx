import React from "react";

const Activity: React.FC<{
  date: string;
  student: string;
  course: string;
  startTime: string;
  endTime: string;
  completed: boolean;
}> = ({ date, student, course, startTime, endTime, completed }) => {
  return (
    <div className="p-5 flex items-center gap-4 border-b border-[#F2F1F2] cursor-pointer">
      <div
        className={`w-6 h-6 border border-red rounded-sm ${
          completed ? "bg-red-500" : "bg-white"
        }`}
      ></div>
      <div>
        <h3 className="text-sm font-bold">{new Date(date).toDateString()}</h3>
        <div className="bg-[#F2F1F2] flex p-2 items-center gap-2 justify-between">
          <div>
            <h4 className="text-xs">{student}</h4>
            <p className="text-2xs">Course: {course}</p>
          </div>
          <div className="text-2xs">
            {startTime} - {endTime}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Activity;
