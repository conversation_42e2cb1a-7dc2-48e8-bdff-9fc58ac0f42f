import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import dayjs from "dayjs";
import React, { useState } from "react";
import { FaRegCircleCheck } from "react-icons/fa6";
import { IoCalendarClearOutline, IoSearchSharp } from "react-icons/io5";
import { useCalendarContext } from "../hooks/useCalendarContext";
import { Views } from "../providers/CalendarContext";
import CustomDatePicker from "./CustomDatePicker";
import { t } from "i18next";

const views = ["Day", "Week", "Month"];

const CustomToolbar: React.FC = () => {
  const {
    currentDate,
    setCurrentDate,
    currentViewName,
    setCurrentViewName,
    currentTab,
    setCurrentTab,
  } = useCalendarContext();

  const [showDatepicker, setShowDatepicker] = useState(false);

  const handleNext = () => {
    if (currentViewName === "Week") {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + 7);
      setCurrentDate(newDate);
    } else if (currentViewName === "Day") {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + 1);
      setCurrentDate(newDate);
    } else {
      const newDate = new Date(currentDate);
      newDate.setMonth(newDate.getMonth() + 1);
      setCurrentDate(newDate);
    }
  };

  const handlePrev = () => {
    if (currentViewName === "Week") {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - 7);
      setCurrentDate(newDate);
    } else if (currentViewName === "Day") {
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - 1);
      setCurrentDate(newDate);
    } else {
      const newDate = new Date(currentDate);
      newDate.setMonth(newDate.getMonth() - 1);
      setCurrentDate(newDate);
    }
  };

  const handleHideDatepicker = () => {
    setShowDatepicker(false);
  };
  const handleShowDatepicker = () => {
    setShowDatepicker(true);
  };

  const startOfWeek = new Date(currentDate);
  startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1);
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(endOfWeek.getDate() + 6);

  return (
    <div className="w-full flex gap-4 items-center justify-between px-4 border-b border-gray/20">
      <div className="flex">
        <IconButton size="small" onClick={handlePrev}>
          <ChevronLeftIcon />
        </IconButton>
        <Button
          variant="text"
          color="inherit"
          sx={{
            whiteSpace: "nowrap",
            fontSize: "12px",
            position: "relative",
          }}
          onClick={handleShowDatepicker}
        >
          {currentViewName === "Day"
            ? dayjs(currentDate).format("MMM D, YYYY")
            : currentViewName === "Week"
              ? `${dayjs(startOfWeek).format("MMM D,")} - ${dayjs(
                  endOfWeek,
                ).format("MMM D, YYYY")}`
              : dayjs(currentDate).format("MMMM YYYY")}
          {showDatepicker && (
            <CustomDatePicker
              currentDate={dayjs(new Date(currentDate).toLocaleDateString())}
              setCurrentDate={setCurrentDate}
              handleHideDatepicker={handleHideDatepicker}
            />
          )}
        </Button>
        <IconButton size="small" onClick={handleNext}>
          <ChevronRightIcon />
        </IconButton>
      </div>
      <div className="flex items-center text-xs">
        <Select
          value={currentViewName}
          onChange={(e) => setCurrentViewName(e.target.value as Views)}
          size="small"
          sx={{ fontSize: "12px" }}
        >
          {views.map((view, index) => (
            <MenuItem value={view} key={index}>
              {view}
            </MenuItem>
          ))}
        </Select>
        <div
          className={`flex gap-1.5 items-center cursor-pointer py-4 px-5 font-bold ml-4 ${
            currentTab === "Calendar" && "border-b-4 border-primary"
          }`}
          onClick={() => {
            setCurrentTab("Calendar");
          }}
        >
          <IoCalendarClearOutline className="text-sm" />
          <span>{t("tutors.calender.customtoolbar.calender")}</span>
        </div>
        <div
          className={`flex gap-1.5 items-center cursor-pointer py-4 px-5 font-bold ${
            currentTab === "List" && "border-b-4 border-primary"
          }`}
          onClick={() => {
            setCurrentTab("List");
          }}
        >
          <FaRegCircleCheck className="text-sm" />
          <span>{t("tutors.calender.customtoolbar.list")}</span>
        </div>
        <div className="relative ml-4 flex-1 max-w-28">
          <IoSearchSharp className="text-base absolute top-4 left-0" />
          <input
            type="text"
            placeholder={t("tutors.calender.customtoolbar.search")}
            className="h-full w-full py-4 outline-none pl-5"
          />
        </div>
      </div>
    </div>
  );
};

export default CustomToolbar;
