import { useScheduleLessonMutation } from "@/app/services/lesson/lesson.service";
import { Spinner } from "@/components/atoms";
import Modal from "@/components/molecules/ModalWrapper";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import {ILesson} from '@/app/services/lesson/interface'
import {
  ChangeSet,
  EditingState,
  IntegratedEditing,
  ViewState,
} from "@devexpress/dx-react-scheduler";
import {
  AppointmentForm,
  Appointments,
  AppointmentTooltip,
  ConfirmationDialog,
  CurrentTimeIndicator,
  DateNavigator,
  DayView,
  DragDropProvider,
  EditRecurrenceMenu,
  MonthView,
  Resources,
  Scheduler,
  Toolbar,
  ViewSwitcher,
  WeekView,
} from "@devexpress/dx-react-scheduler-material-ui";
import { styled } from "@mui/material/styles";
import React, { ComponentType } from "react";
import { useCalendarContext } from "../hooks/useCalendarContext";
import { Views } from "../providers/CalendarContext";

const StyledWeekViewDayScaleCell = styled(WeekView.DayScaleCell)(() => ({
  [`&.${"today"}`]: {
    borderBottom: "4px solid black",
  },
}));

const DayScaleCell = (props: any) => {
  const { today } = props;

  if (today) {
    return <StyledWeekViewDayScaleCell {...props} className={"today"} />;
  }
  return <StyledWeekViewDayScaleCell {...props} />;
};

const StyledToolbarRoot = styled(Toolbar.Root)(() => ({
  display: "none",
}));

const ToolbarRoot = (props: any) => {
  return <StyledToolbarRoot {...props} />;
};

const AppointmentContent: React.FC<{
  children: React.ReactNode;
  [key: string]: any;
}> = ({ children, key, ...restProps }) => {
  return (
    // @ts-ignore
    <Appointments.AppointmentContent {...restProps} className="!text-dark">
      {children}
    </Appointments.AppointmentContent>
  );
};

const SchedulerComponent: React.FC = () => {
  const {
    appointments,
    setAppointments,
    currentViewName,
    currentDate,
    setCurrentDate,
    setCurrentViewName,
    resources,
  } = useCalendarContext();

  const [scheduleLesson, { isLoading: isSchedulingLesson }] =
    useScheduleLessonMutation();
  const { user } = useAuth();

  type LessonType = {
    title: string;
    startDate: string;
    endDate: string;
    allDay: boolean;
    Student: number;
    Status: "completed" | "pending" | "canceled";
    Timezone: string;
    notes: string;
    Course: number;
  };

  const commitChanges = async ({ added, deleted, changed }: ChangeSet) => {
    const lessonData = added as LessonType;

    const lesson: ILesson = {
      title: lessonData.title,
      starts_at: lessonData.startDate,
      ends_at: lessonData.endDate,
      student_id: lessonData.Student,
      description: lessonData.notes,
      course_id: lessonData.Course,
      timezone: lessonData.Timezone,
      tutor_id: user!.id,
    };
    console.log(lesson);
    await RequestInterceptor.handleRequest(
      () => scheduleLesson(lesson).unwrap(),
      {},
      "Schedule Lesson",
    );

    setAppointments((data) => {
      if (added) {
        const startingAddedId = data.length + 1;
        data = [
          ...data,
          {
            id: startingAddedId,
            title: "",
            startDate: "",
            endDate: "",
            status: "Completed",
            student_id: 1,
            course_id: 1,
            timezone: "",
            tutor_id: 1,
          },
        ];
      }
      if (changed) {
        data = data.map((appointment) =>
          changed[appointment.id!]
            ? { ...appointment, ...changed[appointment.id!] }
            : appointment,
        );
      }
      if (deleted !== undefined) {
        data = data.filter((appointment) => appointment.id !== deleted);
      }
      return data;
    });
  };

  return (
    <div className="overflow-x-auto max-w-full">
      <Scheduler height={589} data={appointments} firstDayOfWeek={1}>
        <ViewState
          currentViewName={currentViewName}
          currentDate={new Date(currentDate)}
          onCurrentDateChange={setCurrentDate}
          onCurrentViewNameChange={(viewName: string) =>
            setCurrentViewName(viewName as Views)
          }
        />
        <EditingState onCommitChanges={commitChanges} />
        <IntegratedEditing />
        <EditRecurrenceMenu />
        <DayView cellDuration={60} />
        <WeekView dayScaleCellComponent={DayScaleCell} cellDuration={60} />
        <MonthView />
        <Toolbar rootComponent={ToolbarRoot} />
        <DateNavigator />
        <ViewSwitcher />
        <ConfirmationDialog />
        <Appointments
          appointmentContentComponent={AppointmentContent as ComponentType<any>}
        />
        <Resources data={resources} mainResourceName="status" />
        <DragDropProvider />
        <AppointmentTooltip showCloseButton showDeleteButton showOpenButton />
        <AppointmentForm />
        <CurrentTimeIndicator />
      </Scheduler>
      <Modal
        isOpen={isSchedulingLesson}
        onClose={() => {}}
        shouldStayOpenOnOverlayClicked
      >
        <Spinner variant="default" size="extra-large" />
      </Modal>
    </div>
  );
};

export default SchedulerComponent;
