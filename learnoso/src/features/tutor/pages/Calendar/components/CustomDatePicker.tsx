import { DateCalendar } from "@mui/x-date-pickers-v6";
import { Dayjs } from "dayjs";
import React, { useEffect, useRef } from "react";

const CustomDatePicker: React.FC<{
  currentDate: Dayjs;
  setCurrentDate: React.Dispatch<React.SetStateAction<Date>>;
  handleHideDatepicker: () => void;
}> = ({ currentDate, setCurrentDate, handleHideDatepicker }) => {
  const calendarRef = useRef<HTMLDivElement>(null!);

  const handleClickAway = (e: MouseEvent) => {
    if (!calendarRef.current.contains(e.target as HTMLElement)) {
      handleHideDatepicker();
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickAway);
    return () => document.removeEventListener("mousedown", handleClickAway);
  }, []);

  return (
    <DateCalendar
      value={currentDate}
      onChange={(newValue) => setCurrentDate(newValue)}
      showDaysOutsideCurrentMonth
      ref={calendarRef}
      className="border rounded-md border-gray/50 shadow-md absolute bg-white z-20 -top-8 -left-8"
    />
  );
};

export default CustomDatePicker;
