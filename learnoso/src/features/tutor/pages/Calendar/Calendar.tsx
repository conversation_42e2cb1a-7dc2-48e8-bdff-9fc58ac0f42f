import React from "react";
import { toast } from "react-toastify";
import CalendarSidebar from "./components/CalendarSidebar";
import CustomToolbar from "./components/CustomToolbar";
import ListView from "./components/ListView";
import SchedulerComponent from "./components/SchedulerComponent";
import { useCalendarContext } from "./hooks/useCalendarContext";

const Calendar: React.FC = () => {
  const { currentTab } = useCalendarContext();
  const comingSoon = () => toast.info("Coming soon..");
  return (
    <section className="text-dark space-y-8">
      <div className="flex items-center justify-between gap-4">
        <h1 className="font-bold text-2xl tracking-wider">Calendar</h1>
        <button
          onClick={comingSoon}
          className="flex gap-2 items-center bg-blue-700 hover:bg-primary text-white py-3 px-8 rounded-md text-lg font-semibold transition duration-300 ease-in-out hover:scale-105"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
          >
            <path
              d="M12 6.75H6.75V12H5.25V6.75H0V5.25H5.25V0H6.75V5.25H12V6.75Z"
              fill="white"
            />
          </svg>
          <span>Add an Activity</span>
        </button>
      </div>
      <div className="bg-white flex items-start">
        <div className="flex-1">
          <CustomToolbar />
          {currentTab === "Calendar" ? <SchedulerComponent /> : <ListView />}
        </div>
        <CalendarSidebar />
      </div>
    </section>
  );
};

export default Calendar;
