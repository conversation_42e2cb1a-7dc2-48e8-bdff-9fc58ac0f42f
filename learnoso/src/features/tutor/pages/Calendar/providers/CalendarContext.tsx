import { useGetTutorLessonsQuery } from "@/app/services/lesson/lesson.service";
import { useGetStudentsQuery } from "@/app/services/student/student.service";
import { useGetTutorCoursesQuery } from "@/app/services/tutor/tutor.service";
import { useFetchTimezonesQuery } from "@/app/services/util/util.service";
import { Course } from "@/features/tutor/types";
import { useAuth } from "@/hooks";
import { Resource, ResourceInstance } from "@devexpress/dx-react-scheduler";
import React, { useEffect, useState } from "react";

type ContextType = {
  currentViewName: Views;
  setCurrentViewName: React.Dispatch<React.SetStateAction<Views>>;
  currentDate: Date;
  setCurrentDate: React.Dispatch<React.SetStateAction<Date>>;
  appointments: AppointmentType[];
  setAppointments: React.Dispatch<React.SetStateAction<AppointmentType[]>>;
  currentTab: Tabs;
  setCurrentTab: React.Dispatch<React.SetStateAction<Tabs>>;
  resources: Resource[];
};

export type Views = "Week" | "Day" | "Month";

type Tabs = "Calendar" | "List";

export const CalendarContext = React.createContext<ContextType>(
  {} as ContextType,
);

type AppointmentStatus = "InProgress" | "Completed" | "Pending" | "Missed";

type AppointmentType = {
  title: string;
  startDate: string;
  endDate: string;
  status: AppointmentStatus;
  student_id: number;
  tutor_id: number;
  course_id: number;
  timezone: string;
  id?: number;
};

const sampleAppointments: AppointmentType[] = [
  {
    title: "Website Re-Design Plan",
    startDate: "2024-11-14T08:35:00.000Z",
    endDate: "2024-11-12T10:30:00.000Z",
    status: "Completed",
    student_id: 1,
    course_id: 1,
    timezone: "GMT",
    id: 1,
    tutor_id: 1,
  },
  {
    title: "Website Re-Design Plan 2",
    startDate: "2024-11-12T08:35:00.000Z",
    endDate: "2024-11-14T10:30:00.000Z",
    status: "Completed",
    student_id: 1,
    course_id: 1,
    timezone: "GMT",
    tutor_id: 1,
    id: 2,
  },
];

const initialResources: Resource[] = [
  {
    fieldName: "Status",
    instances: [
      { id: "completed", text: "Completed" },
      { id: "missed", text: "Missed" },
      { id: "reserved", text: "Reserved" },
    ],
  },
];

const CalendarContextProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [currentViewName, setCurrentViewName] = useState<Views>("Week");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [appointments, setAppointments] = useState(sampleAppointments);
  const [currentTab, setCurrentTab] = useState<Tabs>("Calendar");
  const [resources, setResources] = useState<Resource[]>(initialResources);
  const { user } = useAuth();
  const {
    data: students,
    isLoading: isStudentsLoading,
    status: studentQueryStatus,
  } = useGetStudentsQuery("");
  const {
    data: courses,
    isLoading: isCoursesLoading,
    status: tutorCourseQueryStatus,
  } = useGetTutorCoursesQuery(user!.id);

  const {
    data: timezones,
    isLoading: isTimeZonesLoading,
    status: timeZoneQueryStatus,
  } = useFetchTimezonesQuery("");
  const {
    data: lessons,
    status: tutorLessonsQueryStatus,
    isLoading: isTutorLessonsLoading,
  } = useGetTutorLessonsQuery("tutor lessons");

  useEffect(() => {
    switch (studentQueryStatus) {
      case "fulfilled": {
        const studentInstances: ResourceInstance[] = students!.data.map(
          ({ user }: { user: IUser }) => ({
            id: user.id,
            text: `${user.first_name} ${user.last_name}`,
          }),
        );
        setResources([
          ...resources,
          { fieldName: "Student", instances: studentInstances },
        ]);
      }
    }
  }, [studentQueryStatus]);

  useEffect(() => {
    switch (tutorCourseQueryStatus) {
      case "fulfilled": {
        const courseInstances: ResourceInstance[] = courses!.data.map(
          (course: Course) => ({
            id: course.id,
            text: course.name,
          }),
        );
        setResources([
          ...resources,
          { fieldName: "Course", instances: courseInstances },
        ]);
      }
    }
  }, [tutorCourseQueryStatus]);

  useEffect(() => {
    switch (tutorLessonsQueryStatus) {
      case "fulfilled": {
        const lessonInstances: AppointmentType[] = lessons!.data.map(
          (lesson: any) => ({
            id: lesson.id,
            title: lesson.course.name,
            startDate: lesson.starts_at,
            endDate: lesson.ends_at,
            status: lesson.status,
            student_id: lesson.student_id,
            tutor_id: lesson.tutor_id,
            course_id: lesson.course_id,
            timezone: lesson.timezone,
          }),
        );
        setAppointments(lessonInstances);
      }
    }
  }, [tutorLessonsQueryStatus]);

  useEffect(() => {
    switch (timeZoneQueryStatus) {
      case "fulfilled": {
        const timeZoneInstances: ResourceInstance[] = timezones!.data.map(
          (timezone: string) => ({
            id: timezone,
            text: timezone,
          }),
        );
        setResources([
          ...resources,
          { fieldName: "Timezone", instances: timeZoneInstances },
        ]);
      }
    }
  }, [timeZoneQueryStatus]);

  return (
    <CalendarContext.Provider
      value={{
        currentViewName,
        setCurrentViewName,
        currentDate,
        setCurrentDate,
        appointments,
        setAppointments,
        currentTab,
        setCurrentTab,
        resources,
      }}
    >
      {children}
    </CalendarContext.Provider>
  );
};

export default CalendarContextProvider;
