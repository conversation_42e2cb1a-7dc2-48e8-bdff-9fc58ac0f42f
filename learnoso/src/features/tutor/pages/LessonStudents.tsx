import { motion } from "framer-motion";
import React from "react";
import { AiOutlineDown, AiOutlineSearch } from "react-icons/ai";
import { useNavigate } from "react-router-dom";

import { t } from "i18next";

interface Student {
  id: string;
  name: string;
  course: string;
  enrollmentDate: string;
  noOfLessons: number;
}

const students: Student[] = [
  {
    id: "01",
    name: "<PERSON>",
    course: "UI/UX Design",
    enrollmentDate: "09/09/2002 12:00 pm",
    noOfLessons: 5,
  },
  {
    id: "02",
    name: "<PERSON>",
    course: "UI/UX Design",
    enrollmentDate: "09/09/2002 12:00 pm",
    noOfLessons: 5,
  },
  {
    id: "03",
    name: "<PERSON>",
    course: "UI/UX Design",
    enrollmentDate: "09/09/2002 12:00 pm",
    noOfLessons: 5,
  },
  {
    id: "04",
    name: "<PERSON>",
    course: "UI/UX Design",
    enrollmentDate: "09/09/2002 12:00 pm",
    noOfLessons: 5,
  },
];

const AvailableStudents: React.FC = () => {
  const navigate = useNavigate();

  // Mock filter options
  const courseOptions = ["UI/UX Design", "Web Development", "Data Science"];
  const dateOptions = ["Last 7 days", "Last 30 days", "Last year"];
  const lessonsOptions = [
    "1-5 lessons",
    "6-10 lessons",
    "More than 10 lessons",
  ];

  return (
    <>
      <motion.h1
        className="text-2xl font-bold mb-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {t("tutors.lessonstudent.student")}
      </motion.h1>
      <motion.div
        className="bg-white rounded-lg shadow-md p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <h2 className="text-xl font-semibold mb-4">
          {" "}
          {t("tutors.lessonstudent.available_student")}
        </h2>

        <div className="flex justify-between items-center mb-4">
          <div className="flex gap-2 relative">
            <span className="text-gray-500">
              {" "}
              {t("tutors.lessonstudent.filter_by")}{" "}
            </span>

            {/* Course Filter */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <select className="px-3 py-1 border rounded-md flex items-center text-sm">
                <option value="">
                  {t("tutors.lessonstudent.course")}{" "}
                  <AiOutlineDown className="ml-1 w-4 h-4" />
                </option>
                {courseOptions.map((option) => (
                  <option
                    value={option}
                    key={option}
                    className="block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left"
                  >
                    {option}
                  </option>
                ))}
              </select>
            </motion.div>
            {/* Date Filter */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <select className="px-3 py-1 border rounded-md flex items-center text-sm">
                <option value="">
                  {t("tutors.lessonstudent.date")}
                  <AiOutlineDown className="ml-1 w-4 h-4" />
                </option>
                {dateOptions.map((option) => (
                  <option
                    value={option}
                    key={option}
                    className="block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left"
                  >
                    {option}
                  </option>
                ))}
              </select>
            </motion.div>

            {/* No of Lessons Filter */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <select className="px-3 py-1 border rounded-md flex items-center text-sm">
                <option value="">
                  {t("tutors.lessonstudent.lesson")}{" "}
                  <AiOutlineDown className="ml-1 w-4 h-4" />
                </option>
                {lessonsOptions.map((option) => (
                  <option
                    value={option}
                    key={option}
                    className="block px-4 py-2 text-sm hover:bg-gray-100 w-full text-left"
                  >
                    {option}
                  </option>
                ))}
              </select>
            </motion.div>
          </div>

          <motion.div
            className="relative"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <input
              type="text"
              placeholder="Search here"
              className="pl-8 pr-4 py-2 border rounded-md"
            />
            <AiOutlineSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
          </motion.div>
        </div>

        <motion.table
          className="w-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <thead>
            <tr className="bg-slate-200">
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.sn")}
              </th>
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.students")}
              </th>
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.courses")}
              </th>
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.enrollment_date")}
              </th>
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.no_lesson")}
              </th>
              <th className="text-left py-2 px-4">
                {t("tutors.lessonstudent.actions")}
              </th>
            </tr>
          </thead>
          <tbody>
            <p className="text-gray-500 p-2">
              {t("tutors.lessonstudent.no_data")}
            </p>
            {/* {students.map((student, index) => (
              <motion.tr
                key={student.id}
                className={`${
                  index % 2 === 0 ? "bg-white" : "bg-slate-100"
                } cursor-pointer hover:bg-slate-50`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.98 }}
              >
                <td className="py-3 px-4">{student.id}</td>
                <td className="py-3 px-4 flex items-center">
                  <img
                    src="https://i.pravatar.cc/100?img=32"
                    alt={student.name}
                    className="w-10 h-10 rounded-full mr-2"
                  />
                  {student.name}
                </td>
                <td className="py-3 px-4">{student.course}</td>
                <td className="py-3 px-4">{student.enrollmentDate}</td>
                <td className="py-3 px-4">{student.noOfLessons}</td>
                <td className="py-3 px-4">
                  <div className="flex gap-2">
                    <motion.button
                      className="p-1 bg-slate-200 rounded-md"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <AiOutlineCalendar className="w-4 h-4" />
                    </motion.button>
                    <motion.button
                      className="p-1 bg-slate-200 rounded-md"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <AiOutlineMessage className="w-4 h-4" />
                    </motion.button>
                    <Link
                      to={`/tutor/students/${student.id}`}
                      className="p-1 bg-blue-100 rounded-md"
                    >
                      <FaEye className="w-4 h-4 text-primary" />
                    </Link>
                  </div>
                </td>
              </motion.tr>
            ))} */}
          </tbody>
        </motion.table>
      </motion.div>
    </>
  );
};

export default AvailableStudents;
