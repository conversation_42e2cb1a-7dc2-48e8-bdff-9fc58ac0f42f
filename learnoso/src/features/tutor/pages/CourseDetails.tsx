import { t } from "i18next";
import React, { useEffect } from "react";
import { FaArrowLeft, FaTrash } from "react-icons/fa";
import { Link, Outlet, useLocation, useParams } from "react-router-dom";
import useTutorContext from "../hooks/useTutorContext";
import TutorProvider from "../provider/TutorProvider";

interface CourseDetail {
  title: string;
  description: string;
  shortDescription: string;
  language: string;
  skillLevel: string;
  lastUpdated: string;
  currency: string;
  pricePerHour: number;
  rating: number;
  students: number;
  totalHours: number;
}

const courseDetail: CourseDetail = {
  title: "UI/UX Design",
  description:
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam a neque faucibus, mollis tortor sed, tristique ex. Praesent nibh justo, semper vel elementum eget, luctus in elit. Integer laoreet augue tortor, ac ultricies velit vulputate eget. Phasellus tempus dapibus sem, a consequat nunc consequat nec. Ut pulvinar egestas nisl eu sollicitudin. Integer ornare lectus sed augue fringilla tristique. Cras condimentum tristique est eget tempor. Vivamus fermentum, purus eu fermentum ultricies, turpis neque tempor justo, et maximus velit dui non magna.",
  shortDescription:
    "Design thinking, design principles, empathy, responsive design. Description continues ...",
  language: "English",
  skillLevel: "Beginner",
  lastUpdated: "November 2024",
  currency: "FCFA",
  pricePerHour: 2000,
  rating: 4.7,
  students: 52,
  totalHours: 11.5,
};

const CourseDetailComponent: React.FC = () => {
  const { pathname } = useLocation();
  const { setIsEditingComponent, isEditingComponent } = useTutorContext();

  useEffect(() => {
    /**
     * When the child route changes, reset the editing state.
     */
    setIsEditingComponent(false);
  }, [pathname]);

  const { id } = useParams();
  const navItems = [
    { label: "Overview", path: `overview` },
    { label: "Curriculum", path: `curriculum` },
    { label: "Prerequisites", path: `prerequisites` },
    { label: "Evaluation", path: `evaluation` },
    { label: "Reviews", path: `reviews` },
  ];

  return (
    <div className="">
      <header className="my-4 flex justify-between items-center">
        <div className="flex items-center">
          <FaArrowLeft className="mr-2" />
          <h1 className="text-xl font-semibold">{courseDetail.title}</h1>
        </div>
        <button className="text-red-500 font-medium border border-red flex rounded-md items-center p-2 gap-1">
          <FaTrash />
          {t("tutors.coursedetails.delete_course")}
        </button>
      </header>

      <div className="bg-blue-600 text-white p-8 rounded-t-md">
        <h2 className="text-3xl font-bold mb-4">{courseDetail.title}</h2>
        <p className="mb-4">{courseDetail.shortDescription}</p>
        {pathname.split("/").at(4) === "reviews" ? (
          <></>
        ) : (
          <button
            className="bg-white text-blue-600 px-4 py-2 rounded"
            onClick={() => setIsEditingComponent(true)}
          >
            {/* Change this magic value (4) later on. @spykelionel */}
            {isEditingComponent ? "Editing" : "Edit"}{" "}
            {pathname.split("/").at(4)}
          </button>
        )}
      </div>

      <nav className="bg-white border-b border-slate-400">
        <ul className="flex p-4">
          {navItems.map((item) => (
            <li
              key={item.label}
              className={`mr-6 ${
                pathname.includes(item.path) ? "border-b-2 border-blue-600" : ""
              }`}
            >
              <Link
                to={item.path}
                className={`${
                  pathname.includes(item.path)
                    ? "text-blue-600"
                    : "text-gray-500"
                }`}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

      <main className="my-2">
        <Outlet />
      </main>
    </div>
  );
};

const CourseDetailView = () => {
  return (
    <TutorProvider>
      <CourseDetailComponent />
    </TutorProvider>
  );
};

export default CourseDetailView;
