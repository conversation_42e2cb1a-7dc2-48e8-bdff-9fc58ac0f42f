import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { toast } from "react-toastify";
import { Course } from "../types";

const courses: Course[] = [
  {
    id: 1,
    name: t("tutors.courses.name"),
    description: t("tutors.courses.description"),
    rating: 4.7,
    reviews: 28,
    resources: 2,
    color: "bg-blue-600",
  },
  {
    id: 2,
    name: t("tutors.courses.php"),
    description: t("tutors.courses.description"),
    rating: 4.7,
    reviews: 28,
    resources: 2,
    color: "bg-green-500",
  },
  {
    id: 3,
    name: t("tutors.courses.name"),
    description: t("tutors.courses.description"),
    rating: 4.7,
    reviews: 28,
    resources: 2,
    color: "bg-pink-500",
  },
  {
    id: 4,
    name: t("tutors.courses.design_systems"),
    description: t("tutors.courses.system_design"),
    rating: 4.9,
    reviews: 38,
    resources: 5,
    color: "bg-red-500",
  },
  {
    id: 5,
    name: t("tutors.courses.design_systems"),
    description: t("tutors.courses.system_design"),
    rating: 4.9,
    reviews: 38,
    resources: 5,
    color: "bg-orange-500",
  },
];

// const courses:Course[]= t("course.items", { returnObjects: true }) as Array<{
//   question: string;
//   answer: string;
// }>;

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const CoursesDashboard: React.FC = () => {
  return (
    <motion.div
      className=""
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="max-w-6xl container">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-black">
            {" "}
            {t("tutors.courses.courses")}
          </h1>
          <button
            className="bg-blue-500 text-white px-4 py-2 rounded-md text-sm"
            onClick={() => toast.info("Feature not available yet")}
          >
            + {t("tutors.courses.add_course")}
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <p className="text-gray-500">{t("tutors.courses.no_data")}</p>
          {/* {courses.map((course) => (
            <CourseCard key={course.id} course={course} />
          ))} */}
        </div>
      </div>
    </motion.div>
  );
};

export default CoursesDashboard;
