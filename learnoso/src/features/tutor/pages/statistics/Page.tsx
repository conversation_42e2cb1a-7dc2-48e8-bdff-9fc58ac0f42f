import dayjs from "dayjs";
import {
  <PERSON><PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  Calendar,
  Clock,
  DollarSign,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>dingU<PERSON>,
  <PERSON>,
} from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

// Sample data - replace with your actual data
const monthlySessionsData = [
  { month: "Jan", sessions: 24 },
  { month: "Feb", sessions: 28 },
  { month: "Mar", sessions: 32 },
  { month: "Apr", sessions: 36 },
  { month: "May", sessions: 30 },
  { month: "Jun", sessions: 34 },
];

const weeklyHoursData = [
  { day: "Mon", hours: 3 },
  { day: "Tue", hours: 4 },
  { day: "Wed", hours: 2 },
  { day: "Thu", hours: 5 },
  { day: "Fri", hours: 3 },
  { day: "Sat", hours: 6 },
  { day: "Sun", hours: 1 },
];

const subjectDistributionData = [
  { name: "Mathematics", value: 40 },
  { name: "English", value: 25 },
  { name: "Science", value: 20 },
  { name: "History", value: 15 },
];

const studentProgressData = [
  { name: "Alex", initial: 65, current: 85 },
  { name: "Jamie", initial: 45, current: 72 },
  { name: "Casey", initial: 55, current: 80 },
  { name: "<PERSON>", initial: 70, current: 90 },
  { name: "Jordan", initial: 50, current: 78 },
];

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#A4DE6C"];

const TutorStatisticsDashboard = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [dateRange, setDateRange] = useState("monthly");

  const totalSessions = monthlySessionsData.reduce(
    (sum, item) => sum + item.sessions,
    0,
  );
  const totalHours = weeklyHoursData.reduce((sum, item) => sum + item.hours, 0);
  const totalStudents = studentProgressData.length;
  const averageRating = 4.8;
  const completionRate = 94;
  const totalEarnings = 2845;

  const getTimeData = () => {
    if (dateRange === "weekly") return weeklyHoursData;
    return monthlySessionsData;
  };

  const { t } = useTranslation();

  return (
    <div className="bg-slate-100 min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <span className="flex items-center gap-2">
            <Loader2 className="text-red-500 animate-spin" />
            <p className="text-rose-500">
              {t("tutors.statistics.page_under_development")}
            </p>
          </span>
        </div>
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black">
            {" "}
            {t("tutors.statistics.tutor_dashboard")}
          </h1>
          <p className="text-slate-600">
            {t("tutors.statistics.analytics_statistics")}{" "}
            {dayjs().format("MMMM YYYY")}
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <StatsCard
            icon={<Calendar className="text-blue-500" />}
            title={t("tutors.statistics.total_session")}
            value={totalSessions}
            trend={t("tutors.statistics.12%", { value: `12%` })}
            trendUp={true}
          />
          <StatsCard
            icon={<Clock className="text-green-500" />}
            title={t("tutors.statistics.total_hours")}
            value={`${totalHours}h`}
            trend={t("tutors.statistics.8%", { value: `8%` })}
            trendUp={true}
          />
          <StatsCard
            icon={<Users className="text-purple-500" />}
            title={t("tutors.statistics.active_students")}
            value={totalStudents}
            trend={t("tutors.statistics.month", { value: `1` })}
            trendUp={true}
          />
          <StatsCard
            icon={<Star className="text-yellow-500" />}
            title={t("tutors.statistics.average_rating")}
            value={averageRating}
            trend={t("tutors.statistics.0.2_increase", { value: `0.2` })}
            trendUp={true}
          />
          <StatsCard
            icon={<BookOpen className="text-indigo-500" />}
            title={t("tutors.statistics.completion_rate")}
            value={`${completionRate}%`}
            trend={t("tutors.statistics.2%", { value: `2%` })}
            trendUp={true}
          />
          <StatsCard
            icon={<DollarSign className="text-emerald-500" />}
            title={t("tutors.statistics.total_earning")}
            value={`$${totalEarnings}`}
            trend={t("tutors.statistics.15%", { value: `15%` })}
            trendUp={true}
          />
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="border-b px-4">
            <nav className="flex -mb-px">
              <button
                className={`py-4 px-6 font-medium ${
                  activeTab === "overview"
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-500 hover:text-gray-500-700"
                }`}
                onClick={() => setActiveTab("overview")}
              >
                {t("tutors.statistics.overview")}
              </button>
              <button
                className={`py-4 px-6 font-medium ${
                  activeTab === "students"
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-500 hover:text-gray-500-700"
                }`}
                onClick={() => setActiveTab("students")}
              >
                {t("tutors.statistics.student_progress")}
              </button>
              <button
                className={`py-4 px-6 font-medium ${
                  activeTab === "subjects"
                    ? "text-blue-600 border-b-2 border-blue-600"
                    : "text-gray-500 hover:text-gray-500-700"
                }`}
                onClick={() => setActiveTab("subjects")}
              >
                {t("tutors.statistics.students")}
              </button>
            </nav>
          </div>

          <div className="p-6">
            {/* Date Range Selector */}
            <div className="flex justify-end mb-6">
              <div className="inline-flex rounded-md shadow-sm">
                <button
                  className={`px-4 py-2 text-sm font-medium rounded-l-md ${
                    dateRange === "weekly"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-white text-gray-500-700 hover:bg-gray-50"
                  } border border-gray-300`}
                  onClick={() => setDateRange("weekly")}
                >
                  {t("tutors.statistics.weekly")}
                </button>
                <button
                  className={`px-4 py-2 text-sm font-medium rounded-r-md ${
                    dateRange === "monthly"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-white text-gray-500-700 hover:bg-gray-50"
                  } border border-t border-r border-b border-gray-300`}
                  onClick={() => setDateRange("monthly")}
                >
                  {t("tutors.statistics.monthly")}
                </button>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === "overview" && (
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  {dateRange === "weekly" ? "Weekly Hours" : "Monthly Sessions"}
                </h2>
                <div className="bg-white p-4 rounded-lg mb-8 h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getTimeData()}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey={dateRange === "weekly" ? "day" : "month"}
                      />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar
                        dataKey={dateRange === "weekly" ? "hours" : "sessions"}
                        fill="#4f46e5"
                        name={
                          dateRange === "weekly"
                            ? "Hours Taught"
                            : "Sessions Conducted"
                        }
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="bg-white p-4 rounded-lg shadow h-64">
                    <h3 className="text-lg font-medium mb-4 flex items-center">
                      <BarChart2 className="mr-2 h-5 w-5 text-gray-500" />
                      {t("tutors.statistics.upcoming_session")}
                    </h3>
                    <div className="space-y-4">
                      <SessionItem
                        student="Alex Miller"
                        subject={t("tutors.statistics.mathematic")}
                        time={t("tutors.statistics.tomorrow", {
                          value: `4:00 PM`,
                        })}
                        duration={t("tutors.statistics.min", { value: `60` })}
                      />
                      <SessionItem
                        student="Jamie Smith"
                        subject={t("tutors.statistics.english")}
                        time={t("tutors.statistics.wednesday", {
                          value: `5:30 PM`,
                        })}
                        duration={t("tutors.statistics.min_45", {
                          value: `45`,
                        })}
                      />
                      <SessionItem
                        student="Casey Jones"
                        subject={t("tutors.statistics.science")}
                        time={t("tutors.statistics.friday", {
                          value: `3:15PM`,
                        })}
                        duration={t("tutors.statistics.min_90", {
                          value: `90`,
                        })}
                      />
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow h-64">
                    <h3 className="text-lg font-medium mb-4 flex items-center">
                      <PieChartIcon className="mr-2 h-5 w-5 text-gray-500" />
                      {t("tutors.statistics.session_distribution")}
                    </h3>
                    <ResponsiveContainer width="100%" height="80%">
                      <PieChart>
                        <Pie
                          data={subjectDistributionData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) =>
                            `${name} ${(percent * 100).toFixed(0)}%`
                          }
                        >
                          {subjectDistributionData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "students" && (
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  {t("tutors.statistics.student_progress")}
                </h2>
                <div className="bg-white rounded-lg mb-8 h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={studentProgressData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar
                        dataKey="initial"
                        fill="#94a3b8"
                        name="Initial Score"
                      />
                      <Bar
                        dataKey="current"
                        fill="#4f46e5"
                        name="Current Score"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>

                <div className="bg-white rounded-lg shadow">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg font-medium">
                      {t("tutor.statistics.student_details")}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {t("tutors.statistics.active_student")}
                    </p>
                  </div>
                  <div className="border-t border-slate-300">
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-slate-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.active_student")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.active_subject")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.active_progress")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.sessions")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.last_session")}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-slate-300">
                          <StudentRow
                            name="Alex Miller"
                            subject={t("tutors.statistics.mathematic")}
                            progress={85}
                            sessions={12}
                            lastSession={t("tutors.statistics.mar_28", {
                              value: `28, 2025`,
                            })}
                          />
                          <StudentRow
                            name="Jamie Smith"
                            subject={t("tutors.statistics.english")}
                            progress={72}
                            sessions={8}
                            lastSession={t("tutors.statistics.mar_25", {
                              value: `25,2025`,
                            })}
                          />
                          <StudentRow
                            name="Casey Jones"
                            subject={t("tutors.statistics.science")}
                            progress={80}
                            sessions={10}
                            lastSession={t("tutors.statistics.mar_27", {
                              value: `27, 2025`,
                            })}
                          />
                          <StudentRow
                            name="Taylor Wilson"
                            subject={t("tutors.statistics.mathematics")}
                            progress={90}
                            sessions={14}
                            lastSession={t("tutors.statistics.mar_29", {
                              value: `29, 2025`,
                            })}
                          />
                          <StudentRow
                            name="Jordan Lee"
                            subject={t("tutors.statistics.history")}
                            progress={78}
                            sessions={9}
                            lastSession={t("tutors.statistics.mar_26", {
                              value: `26, 2025`,
                            })}
                          />
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "subjects" && (
              <div>
                <h2 className="text-xl font-semibold mb-4">
                  {t("tutors.statistics.subject_analysis")}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="bg-white p-4 rounded-lg shadow h-64">
                    <h3 className="text-lg font-medium mb-4">
                      {t("tutors.statistics.session_by")}
                    </h3>
                    <ResponsiveContainer width="100%" height="80%">
                      <PieChart>
                        <Pie
                          data={subjectDistributionData}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label
                        >
                          {subjectDistributionData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={COLORS[index % COLORS.length]}
                            />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  <div className="bg-white p-4 rounded-lg shadow h-64">
                    <h3 className="text-lg font-medium mb-4">
                      {t("tutors.statistics.earning_by")}
                    </h3>
                    <ResponsiveContainer width="100%" height="80%">
                      <BarChart
                        data={[
                          { name: "Mathematics", value: 1200 },
                          { name: "English", value: 800 },
                          { name: "Science", value: 600 },
                          { name: "History", value: 245 },
                        ]}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip
                          formatter={(value) => [`$${value}`, "Earnings"]}
                        />
                        <Bar
                          dataKey="value"
                          fill="#4f46e5"
                          name="Earnings ($)"
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                <div className="mt-8 bg-white rounded-lg shadow overflow-hidden">
                  <div className="px-4 py-5 sm:px-6">
                    <h3 className="text-lg font-medium">
                      {" "}
                      {t("tutors.statistics.subject_performance")}{" "}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {t("tutors.statistics.details")}
                    </p>
                  </div>
                  <div className="border-t border-slate-300">
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-slate-300">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.subject")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.student")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.avg")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.avg_rating")}
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              {t("tutors.statistics.earnings")}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-slate-300">
                          <SubjectRow
                            name="Mathematics"
                            students={12}
                            avgLength={60}
                            avgRating={4.9}
                            earnings={1200}
                          />
                          <SubjectRow
                            name="English"
                            students={8}
                            avgLength={45}
                            avgRating={4.7}
                            earnings={800}
                          />
                          <SubjectRow
                            name="Science"
                            students={6}
                            avgLength={75}
                            avgRating={4.8}
                            earnings={600}
                          />
                          <SubjectRow
                            name="History"
                            students={3}
                            avgLength={60}
                            avgRating={4.6}
                            earnings={245}
                          />
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

interface StatsCardProps {}

// Component for stats cards
const StatsCard: React.FC<any> = ({ icon, title, value, trend, trendUp }) => {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className="p-3 rounded-full bg-gray-100 mr-4">{icon}</div>
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-semibold text-gray-500-900">{value}</p>
        </div>
      </div>
      <div className="mt-2 flex items-center">
        <TrendingUp
          className={`h-4 w-4 ${trendUp ? "text-green-500" : "text-red-500"} mr-1`}
        />
        <p
          className={`text-sm ${trendUp ? "text-green-500" : "text-red-500/75"}`}
        >
          {trend}
        </p>
      </div>
    </div>
  );
};

// Component for upcoming session items
interface SessionItemProps {
  student: string;
  subject: string;
  time: string;
  duration: string;
}

const SessionItem: React.FC<SessionItemProps> = ({
  student,
  subject,
  time,
  duration,
}) => {
  return (
    <div className="flex items-center py-2 border-b border-gray-100 last:border-0">
      <div className="mr-4">
        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
          <span className="text-blue-600 font-medium">{student.charAt(0)}</span>
        </div>
      </div>
      <div className="flex-1">
        <p className="font-medium text-gray-500-900">{student}</p>
        <p className="text-sm text-gray-500">
          {subject} • {duration}
        </p>
      </div>
      <div className="text-sm text-gray-500">{time}</div>
    </div>
  );
};

interface StudentRowProps {
  name: string;
  subject: string;
  progress: number;
  sessions: number;
  lastSession: string;
}

const StudentRow: React.FC<StudentRowProps> = ({
  name,
  subject,
  progress,
  sessions,
  lastSession,
}) => {
  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-blue-600 font-medium">{name.charAt(0)}</span>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-500-900">{name}</div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm text-gray-500-900">{subject}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="w-full bg-slate-300 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <span className="text-xs text-gray-500 mt-1">{progress}%</span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {sessions}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {lastSession}
      </td>
    </tr>
  );
};

interface SubjectRowProps {
  name: string;
  students: number;
  avgLength: number;
  avgRating: number;
  earnings: number;
}

const SubjectRow: React.FC<SubjectRowProps> = ({
  name,
  students,
  avgLength,
  avgRating,
  earnings,
}) => {
  return (
    <tr>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="text-sm font-medium text-gray-500-900">{name}</div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {students}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {avgLength} min
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <div className="flex items-center">
          <Star className="h-4 w-4 text-yellow-400 mr-1" />
          <span className="text-sm text-gray-500-900">{avgRating}</span>
        </div>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500-900">
        ${earnings}
      </td>
    </tr>
  );
};

export default TutorStatisticsDashboard;
