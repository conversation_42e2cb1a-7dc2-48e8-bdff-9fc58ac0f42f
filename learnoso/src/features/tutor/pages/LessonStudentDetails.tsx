import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import {
  AiOutlineArrowLeft,
  AiOutlineBook,
  AiOutlineClockCircle,
  AiOutlineGlobal,
  AiOutlineMessage,
} from "react-icons/ai";
import LessonCard from "../components/molecules/StudentLessonCard";

const StudentDetails: React.FC = () => {
  const student = {
    name: "<PERSON>",
    completedLessons: 2,
    courses: ["Design", "PHP"],
    languages: ["Native English", "French", "German"],
    bio: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna.",
  };

  const lessons = [
    {
      title: "UI/UX Design",
      description:
        "Design thinking, design principles, empathy, responsive design. Description continues ....",
      price: 20,
      duration: 45,
      lessonCount: 2,
      status: "Completed",
      date: "Tuesday 17 September 2024 - 7:30am",
      resources: 2,
      curriculumCategory: "Advanced",
      assignment: null,
      backgroundColor: "bg-blue-600", // Add the background color for this lesson
    },
    {
      title: "PHP",
      description:
        "Design thinking, design principles, empathy, responsive design. Description continues ....",
      price: 20,
      duration: 45,
      lessonCount: 2,
      status: "Completed",
      date: "Wednesday 18 September 2024 - 8:00am",
      resources: 2,
      curriculumCategory: "Advanced",
      assignment: 1,
      backgroundColor: "bg-green-500",
    },
  ];

  return (
    <>
      <motion.div
        className="flex justify-between items-center mb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center">
          <AiOutlineArrowLeft className="w-6 h-6 mr-2" />
          <h1 className="text-2xl font-bold">
            {t("tutors.lessonstudentdetails.stephanie")}
          </h1>
        </div>
        <motion.button
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <AiOutlineMessage className="w-6 h-6 mr-2" />
          {t("tutors.lessonstudentdetails.message")}
        </motion.button>
      </motion.div>

      <motion.div
        className="bg-white rounded-lg shadow-md p-6 mb-6"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-start">
          <img
            src="https://i.pravatar.cc/100?img=43"
            alt="John Mart"
            className="w-24 h-24 rounded-lg mr-6"
          />
          <div>
            <h2 className="text-xl font-bold mb-2">{student.name}</h2>
            <p className="flex items-center mb-1">
              <AiOutlineClockCircle className="w-6 h-6 mr-2" />
              {t("tutors.lessonstudentdetails.number_complete")}
              {student.completedLessons}
            </p>
            <p className="flex items-center mb-1">
              <AiOutlineBook className="w-6 h-6 mr-2" />
              {t("tutors.lessonstudentdetails.course")}{" "}
              {student.courses.join(", ")}
            </p>
            <p className="flex items-center mb-2">
              <AiOutlineGlobal className="w-6 h-6 mr-2" />
              {student.languages.join(", ")}
            </p>
            <p>{student.bio}</p>
          </div>
        </div>
      </motion.div>

      {/* Lessons Section */}
      <motion.h2
        className="text-xl font-bold mb-4"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {t("tutors.lessonstudentdetails.lesson")}
      </motion.h2>
      <motion.div
        className="grid grid-cols-2 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.3 }}
      >
        {lessons.map((lesson, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <LessonCard
              title={lesson.title}
              description={lesson.description}
              price={lesson.price}
              duration={lesson.duration}
              lessonCount={lesson.lessonCount}
              status={lesson.status}
              date={lesson.date}
              resources={lesson.resources}
              curriculumCategory={lesson.curriculumCategory}
              assignment={lesson.assignment}
              backgroundColor={lesson.backgroundColor}
            />
          </motion.div>
        ))}
      </motion.div>
    </>
  );
};

export default StudentDetails;
