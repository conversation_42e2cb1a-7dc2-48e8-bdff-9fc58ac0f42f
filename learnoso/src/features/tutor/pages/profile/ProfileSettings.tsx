import {
  useGetTutorProfileMutation,
  useOnboardTutorPriceAvailabilityMutation,
} from "@/app/services/tutor/tutor.service";
import { useFetchCurrenciesQuery } from "@/app/services/util/util.service";
import { Spinner } from "@/components/atoms";
import { TimeSlot } from "@/features/studentOnboarding/components";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import React, { memo, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { MdDeleteOutline, MdKeyboardArrowDown } from "react-icons/md";

type TimeSlot = {
  time: string;
  availability: {
    from: string;
    to: string;
  };
  day_of_the_week: string;
};

type AvailabilityState = {
  enabled: boolean;
  timezone: string;
  slots: TimeSlot[];
};
type Tab = "about" | "pricing" | "language";

const ProfileSettings = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<Tab>("about");
  const [profile, setProfile] = useState({
    fullName: `${user?.first_name} ${user?.last_name}`,
    email: user!.email,
    phone: "N/A",
    country: "N/A",
    bio: "",
    languages: ["English", "French"],
  });
  const [language, setLanguage] = useState("English");

  const TabButton = ({ tab, label }: { tab: Tab; label: string }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`px-4 py-2 text-left ${
        activeTab === tab ? "bg-slate-300" : "hover:bg-gray/50"
      } w-full`}
    >
      {label}
    </button>
  );

  const AboutContent = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-medium">
        {t("tutors.profilesetting.about")}
      </h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("tutors.profilesetting.full_name")}
          </label>
          <input
            type="text"
            value={profile.fullName}
            onChange={(e) =>
              setProfile((prev) => ({ ...prev, fullName: e.target.value }))
            }
            className="w-full px-3 py-2 border border-slate-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("common.email")}
          </label>
          <input
            type="email"
            value={profile.email}
            onChange={(e) =>
              setProfile((prev) => ({ ...prev, email: e.target.value }))
            }
            className="w-full px-3 py-2 border border-slate-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("tutors.profilesetting.phone_number")}
          </label>
          <input
            type="tel"
            value={profile.phone}
            onChange={(e) =>
              setProfile((prev) => ({ ...prev, phone: e.target.value }))
            }
            className="w-full px-3 py-2 border border-slate-300 rounded-md"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("tutors.profilesetting.country")}
          </label>
          <div className="relative">
            <select
              value={profile.country}
              onChange={(e) =>
                setProfile((prev) => ({ ...prev, country: e.target.value }))
              }
              className="w-full px-3 py-2 border border-slate-300 rounded-md appearance-none pr-10"
            >
              <option>{t("tutors.profilesetting.cameroon")}</option>
              <option>{t("tutors.profilesetting.nigeria")}</option>
              <option>{t("tutors.profilesetting.ghana")}</option>
            </select>
            <MdKeyboardArrowDown className="absolute right-3 top-3 text-slate-400" />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("tutors.profilesetting.bio")}
          </label>
          <textarea
            value={profile.bio}
            onChange={(e) =>
              setProfile((prev) => ({ ...prev, bio: e.target.value }))
            }
            className="w-full px-3 py-2 border border-slate-300 rounded-md h-32"
            placeholder={t("tutors.profilesetting.type_placeholder")}
          />
        </div>
      </div>

      <button className="px-4 py-2 bg-slate-200 text-slate-700 rounded-md hover:bg-slate-300">
        {t("tutors.profilesetting.save")}
      </button>
    </div>
  );

  const PricingContent = memo(() => {
    const [price, setPrice] = useState<number>(6000);
    const [currency, setCurrency] = useState<string>("xaf");
    const [availability, setAvailability] = useState<AvailabilityState>({
      enabled: true,
      timezone: "West African",
      slots: [
        {
          time: "morning",
          availability: { from: "08:00", to: "12:00" },
          day_of_the_week: "monday",
        },
      ],
    });

    const [
      getTutorProfile,
      { data: profileData, isLoading: isLoadingTutorProfile },
    ] = useGetTutorProfileMutation();

    const { user } = useAuth();

    const getProfileData = async () => {
      const { data } = await RequestInterceptor.handleRequest(() =>
        getTutorProfile({ user_id: user!.id }).unwrap(),
      );
      console.log(data);
    };

    useEffect(() => {
      getProfileData();
    }, []);

    useEffect(() => {
      if (profileData?.data?.tutor) {
        const {
          price,
          currency,
          availability: availabilityString,
        } = profileData.data.tutor;

        // Set price and currency
        setPrice(price || 0);
        setCurrency(currency || "xaf");

        // Parse and set availability
        try {
          if (availabilityString) {
            const parsedAvailability = JSON.parse(availabilityString);
            setAvailability({
              enabled: true,
              timezone: parsedAvailability[0]?.time || "GMT+1",
              slots: parsedAvailability.map((slot: any) => ({
                time: slot.time === "GMT+12" ? "morning" : slot.time, // Convert timezone to time period if needed
                availability: {
                  from: slot.availability.from,
                  to: slot.availability.to,
                },
                day_of_the_week: slot.day_of_the_week.toLowerCase(),
              })),
            });
          }
        } catch (error) {
          console.error("Error parsing availability:", error);
        }
      }
    }, [profileData]);

    const { data: remoteCurrencies, status: currencyQueryStatus } =
      useFetchCurrenciesQuery("currencies");
    const [currencies, setCurrencies] = useState([""]);
    const [
      postTutorAvailability,
      { isLoading: isUpdatingAvailabiltyAndPrice },
    ] = useOnboardTutorPriceAvailabilityMutation();
    const daysOfWeek = [
      t("tutors.profilesetting.monday"),
      t("tutors.profilesetting.tuesday"),
      t("tutors.profilesetting.wednesday"),
      t("tutors.profilesetting.thursday"),
      t("tutors.profilesetting.friday"),
      t("tutors.profilesetting.saturday"),
      t("tutors.profilesetting.sunday"),
    ];

    const timeOptions = [
      { value: "morning", label: "Morning" },
      { value: "afternoon", label: "Afternoon" },
      { value: "evening", label: "Evening" },
    ];
    useEffect(() => {
      if (remoteCurrencies?.data) {
        const keys = remoteCurrencies.data.map(
          (currency: Record<string, string>) => Object.keys(currency)[0],
        );
        setCurrencies(keys);
      }
    }, [currencyQueryStatus]);

    const addTimeSlot = () => {
      setAvailability((prev) => ({
        ...prev,
        slots: [
          ...prev.slots,
          {
            time: "morning",
            availability: { from: "08:00", to: "12:00" },
            day_of_the_week: "monday",
          },
        ],
      }));
    };

    const removeTimeSlot = (index: number) => {
      setAvailability((prev) => ({
        ...prev,
        slots: prev.slots.filter((_, i) => i !== index),
      }));
    };

    const updateSlot = (index: number, field: keyof TimeSlot, value: any) => {
      setAvailability((prev) => ({
        ...prev,
        slots: prev.slots.map((slot, i) => {
          if (i === index) {
            if (field === "availability") {
              return {
                ...slot,
                availability: { ...slot.availability, ...value },
              };
            }
            return { ...slot, [field]: value };
          }
          return slot;
        }),
      }));
    };

    const handleSubmit = async () => {
      // Only include active slots when availability is enabled
      const formData = {
        user_id: user?.id,
        currency,
        price,
        availability: availability.enabled ? availability.slots : [],
      };
      console.log("Submitting data:", formData);
      await RequestInterceptor.handleRequest(
        () =>
          postTutorAvailability(formData as ITutorPriceAvailability).unwrap(),
        {},
        "Update Tutor Availability and Price",
      );
    };
    if (isLoadingTutorProfile) {
      return <Spinner />;
    }

    return (
      <div className="space-y-6">
        <h2 className="text-xl font-medium">
          {t("tutors.profilesetting.pricing_availability")}
        </h2>

        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">
              {t("tutors.profilesetting.pricing")}{" "}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  {t("tutors.profilesetting.amount")}
                </label>
                <input
                  type="number"
                  value={price}
                  onChange={(e) => setPrice(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  {t("tutors.profilesetting.currency")}
                </label>
                <div className="relative">
                  <select
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md appearance-none"
                  >
                    {currencies.map((curr) => (
                      <option key={curr} value={curr}>
                        {curr.toLocaleUpperCase()}
                      </option>
                    ))}
                  </select>
                  <MdKeyboardArrowDown className="absolute right-3 top-3 text-slate-400" />
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">
                  {t("tutors.profilesetting.availability")}{" "}
                </h3>
                <p className="text-sm text-slate-500">
                  {t("tutors.profilesetting.teaching_schedule")}
                </p>
              </div>
              <button
                role="switch"
                aria-checked={availability.enabled}
                onClick={() =>
                  setAvailability((prev) => ({
                    ...prev,
                    enabled: !prev.enabled,
                  }))
                }
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                  ${availability.enabled ? "bg-blue-600" : "bg-slate-200"}`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                    ${availability.enabled ? "translate-x-6" : "translate-x-1"}`}
                />
              </button>
            </div>

            {availability.enabled && (
              <div className="space-y-4">
                {availability.slots.map((slot, index) => (
                  <div
                    key={index}
                    className="p-4 border border-slate-200 rounded-lg space-y-4"
                  >
                    <div className="flex justify-between items-center">
                      <h4 className="font-medium">
                        {t("tutors.profilesetting.slot")} {index + 1}
                      </h4>
                      <button
                        onClick={() => removeTimeSlot(index)}
                        className="text-red-500 hover:text-red-500/90"
                      >
                        <MdDeleteOutline size={20} />
                      </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-1">
                          {t("tutors.profilesetting.day")}
                        </label>
                        <div className="relative">
                          <select
                            value={slot.day_of_the_week}
                            onChange={(e) =>
                              updateSlot(
                                index,
                                "day_of_the_week",
                                e.target.value,
                              )
                            }
                            className="w-full px-3 py-2 border border-slate-300 rounded-md appearance-none"
                          >
                            {daysOfWeek.map((day) => (
                              <option key={day} value={day}>
                                {day.charAt(0).toUpperCase() + day.slice(1)}
                              </option>
                            ))}
                          </select>
                          <MdKeyboardArrowDown className="absolute right-3 top-3 text-slate-400" />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-1">
                          {t("tutors.profilesetting.time_of_day")}
                        </label>
                        <div className="relative">
                          <select
                            value={slot.time}
                            onChange={(e) =>
                              updateSlot(index, "time", e.target.value)
                            }
                            className="w-full px-3 py-2 border border-slate-300 rounded-md appearance-none"
                          >
                            {timeOptions.map((option) => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </select>
                          <MdKeyboardArrowDown className="absolute right-3 top-3 text-slate-400" />
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-1">
                          {t("tutors.profilesetting.from")}
                        </label>
                        <input
                          type="time"
                          value={slot.availability.from}
                          onChange={(e) =>
                            updateSlot(index, "availability", {
                              from: e.target.value,
                            })
                          }
                          className="w-full px-3 py-2 border border-slate-300 rounded-md"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-1">
                          {t("tutors.profilesetting.to")}
                        </label>
                        <input
                          type="time"
                          value={slot.availability.to}
                          onChange={(e) =>
                            updateSlot(index, "availability", {
                              to: e.target.value,
                            })
                          }
                          className="w-full px-3 py-2 border border-slate-300 rounded-md"
                        />
                      </div>
                    </div>
                  </div>
                ))}

                <button
                  onClick={addTimeSlot}
                  className="text-blue-600 hover:text-blue-700 flex items-center gap-2"
                >
                  {t("tutors.profilesetting.time_slot")}
                </button>
              </div>
            )}
          </div>
        </div>

        {isUpdatingAvailabiltyAndPrice ? (
          <Spinner />
        ) : (
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 w-full"
          >
            {t("tutors.profilesetting.save_changes")}
          </button>
        )}
      </div>
    );
  });

  const LanguageContent = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-medium">
        {" "}
        {t("tutors.profilesetting.switch_language")}{" "}
      </h2>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">
            {t("tutors.profilesetting.language")}
          </label>
          <div className="relative">
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className="w-full px-3 py-2 border border-slate-300 rounded-md appearance-none pr-10"
            >
              <option>{t("tutors.profilesetting.english")}</option>
              <option>{t("tutors.profilesetting.french")}</option>
            </select>
            <MdKeyboardArrowDown className="absolute right-3 top-3 text-slate-400" />
          </div>
        </div>

        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          {t("tutors.profilesetting.update")}
        </button>
      </div>
    </div>
  );

  return (
    <div className="max-w-5xl mx-auto p-4 flex gap-6">
      <div className="w-64 flex-shrink-0">
        <TabButton tab="about" label="About You" />
        <TabButton tab="pricing" label="Pricing and Availability" />
        <TabButton tab="language" label="Language" />
      </div>
      <div className="flex-1 bg-white p-6 rounded-lg border border-slate-200">
        {activeTab === "about" && <AboutContent />}
        {activeTab === "pricing" && <PricingContent />}
        {activeTab === "language" && <LanguageContent />}
      </div>
    </div>
  );
};

export default ProfileSettings;
