import NotFound from "@/components/partials/NotFound";
import UserProfile from "@/features/student/pages/Profile";
import { CustomRouteObject } from "@/types";
import React from "react";
import { CurriculumOverview } from "../components/organ/courseCurriculum";
import { CourseDescriptionOverview } from "../components/organ/courseDescription";
import { CourseModuleOverView } from "../components/organ/courseModuleQuestions";
import { CoursePrerequisitesOverview } from "../components/organ/coursePrerequisites";
import CourseReviewsSection from "../components/organ/CourseReviews";
import TutorLayout from "../components/partials/TutorLayout";
import Calendar from "../pages/Calendar/Calendar";
import CalendarContextProvider from "../pages/Calendar/providers/CalendarContext";
import CourseDetailView from "../pages/CourseDetails";
import CoursesDashboard from "../pages/Courses";
import TutorDashboard from "../pages/Dashboard";
import LessonRequestDetails from "../pages/LessonRequest";
import Lessons from "../pages/Lessons";
import StudentDetails from "../pages/LessonStudentDetails";
import AvailableStudents from "../pages/LessonStudents";
import ProfileSettings from "../pages/profile/ProfileSettings";
import TutorStatisticsDashboard from "../pages/statistics/Page";
import TutorWalletPage from "../pages/transaction/Transaction";

export const tutorRoutes: CustomRouteObject[] = [
  {
    element: <TutorLayout />,
    path: "/tutor",
    children: [
      {
        index: true,
        element: <TutorDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "dashboard",
        element: <TutorDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "statistics",
        element: <TutorStatisticsDashboard />,
        errorElement: <NotFound />,
      },
      {
        path: "lessons",
        element: <Lessons />,
        errorElement: <NotFound />,
      },
      {
        path: "profile",
        element: <UserProfile />,
        errorElement: <NotFound />,
      },
      {
        path: "account-settings",
        element: <ProfileSettings />,
        errorElement: <NotFound />,
      },
      {
        path: "lessons/:id",
        element: <LessonRequestDetails />,
        errorElement: <NotFound />,
      },
      {
        path: "courses",
        element: <CoursesDashboard />,
        errorElement: <NotFound />,
      },

      {
        path: "wallet",
        element: <TutorWalletPage />,
        errorElement: <NotFound />,
      },
      {
        path: "students",
        element: <AvailableStudents />,
        errorElement: <NotFound />,
      },
      {
        path: "students/:id",
        element: <StudentDetails />,
        errorElement: <NotFound />,
      },
      {
        path: "calendar",
        element: (
          <CalendarContextProvider>
            <Calendar />
          </CalendarContextProvider>
        ),
        errorElement: <NotFound />,
      },
      {
        path: "courses/:id",
        element: <CourseDetailView />,
        errorElement: <NotFound />,
        children: [
          {
            path: "",
            index: true,
            element: <CourseDescriptionOverview />,
            errorElement: <NotFound />,
          },
          {
            path: "overview",
            element: <CourseDescriptionOverview />,
            errorElement: <NotFound />,
          },
          {
            path: "curriculum",
            element: <CurriculumOverview />,
            errorElement: <NotFound />,
          },
          {
            path: "prerequisites",
            element: <CoursePrerequisitesOverview />,
            errorElement: <NotFound />,
          },
          {
            path: "evaluation",
            element: <CourseModuleOverView />,
            errorElement: <NotFound />,
          },
          {
            path: "reviews",
            element: <CourseReviewsSection />,
            errorElement: <NotFound />,
          },
        ],
      },
    ],
    errorElement: <NotFound />,
  },
];
