import { useAuth } from "@/hooks";
import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";

function DashboardHeader() {
  const { user } = useAuth();
  return (
    <motion.div
      className="flex justify-between items-center mb-8"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className="flex flex-col">
        <h1 className="text-2xl font-semibold">
          {t("tutors.dashboardheader.welcome")}{" "}
          <span className="text-primary">{user?.first_name}!</span>
        </h1>
        <p className="text-dark">{t("tutors.dashboardheader.description")}</p>
      </div>
      <select className="border rounded px-2 py-1 text-gray-500">
        <option> {t("tutors.dashboardheader.monthly")}</option>
        <option> {t("tutors.dashboardheader.weekly")}</option>
        <option> {t("tutors.dashboardheader.daily")}</option>
      </select>
    </motion.div>
  );
}

export default DashboardHeader;
