import { useGetTutorLessonsQuery } from "@/app/services/lesson/lesson.service";
import { Spinner } from "@/components/atoms";
import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import LessonItem from "./LessonItem";
import { t } from "i18next";
const listItemVariants = {
  hidden: { opacity: 0, x: 20 },
  visible: { opacity: 1, x: 0 },
};

function Upcoming() {
  const {
    data: lessons,
    status: lessonsQueryStatus,
    isLoading: isLessonsLoading,
  } = useGetTutorLessonsQuery("session");
  const [lessonsData, setLessonsData] = useState([]);
  useEffect(() => {
    if (lessonsQueryStatus === "fulfilled") {
      setLessonsData(lessons?.data?.slice(0, 5));
    }
  }, [lessonsQueryStatus]);

  return (
    <motion.div
      className="bg-white rounded-lg shadow p-4 "
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {t("tutors.upcoming.upcoming")}
        </h2>
        <Link to="/tutor/lessons" className="text-primary text-sm">
          {t("tutors.upcoming.view_all")}
        </Link>
      </div>
      <ul className="space-y-2">
        {isLessonsLoading ? (
          <Spinner />
        ) : (
          lessonsData.map((lesson: any) => (
            <LessonItem
              lesson={lesson}
              listItemVariants={listItemVariants}
              key={lesson.id}
            />
          ))
        )}
      </ul>
    </motion.div>
  );
}

export default Upcoming;
