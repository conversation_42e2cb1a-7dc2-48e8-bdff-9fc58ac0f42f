import { t } from "i18next";
import React, { useState } from "react";
import { FaChevronDown } from "react-icons/fa";

interface GradeAssignmentCardProps {
  onSave: (grade: string, motivation: string) => void;
  onCancel: () => void;
}

const GradeAssignmentCard: React.FC<GradeAssignmentCardProps> = ({
  onSave,
  onCancel,
}) => {
  const [grade, setGrade] = useState<string>("");
  const [motivation, setMotivation] = useState<string>("");

  const handleSave = () => {
    onSave(grade, motivation);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 w-2/5">
      <h2 className="text-2xl font-bold text-blue-700 mb-6">
        {t("tutors.gradecard.grade_assignment")}
      </h2>

      <div className="mb-6">
        <label
          htmlFor="grade"
          className="block text-sm font-medium text-gray-500-700 mb-2"
        >
          {t("tutors.gradecard.select_grade")}
        </label>
        <div className="relative">
          <select
            id="grade"
            value={grade}
            onChange={(e) => setGrade(e.target.value)}
            className="block w-full px-3 py-2 text-gray-500 bg-white border border-slate-300 rounded-md shadow-sm appearance-none focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">{t("tutor.gradecard.select_grade")}</option>
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
            <option value="D">D</option>
            <option value="F">F</option>
          </select>
          <FaChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
        </div>
      </div>

      <div className="mb-6">
        <label
          htmlFor="motivation"
          className="block text-sm font-medium text-dark mb-2"
        >
          {t("tutors.gradecard.motivation")}
        </label>
        <textarea
          id="motivation"
          rows={4}
          value={motivation}
          onChange={(e) => setMotivation(e.target.value)}
          className="block w-full px-3 py-2 text-dark border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
        ></textarea>
      </div>

      <div className="flex justify-end space-x-4">
        <button
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-blue-700 border border-blue-700 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {t("tutors.gradecard.cancel")}
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-700 rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {t("tutors.gradecard.save")}
        </button>
      </div>
    </div>
  );
};

export default GradeAssignmentCard;
