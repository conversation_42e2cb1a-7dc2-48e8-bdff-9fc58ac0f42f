import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useEffect, useRef, useState } from "react";
import {
  FaArchive,
  FaBookOpen,
  FaCopy,
  FaEdit,
  FaEllipsisV,
  FaEye,
  FaShareAlt,
  FaStar,
  FaTrash,
} from "react-icons/fa";
import { Link } from "react-router-dom";
import { Course } from "../../types";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2, // Delay between card animations
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const CourseCard: React.FC<{ course: Course }> = ({ course }) => {
  const [showActions, setShowActions] = useState(false);
  const actionsRef = useRef<HTMLDivElement>(null);

  // Close actions menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        actionsRef.current &&
        !actionsRef.current.contains(event.target as Node)
      ) {
        setShowActions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <motion.div
      className="bg-white rounded-lg shadow-md relative overflow-hidden"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.3 }}
      variants={itemVariants}
    >
      <>
        <div className={`${course.color} p-4 text-white`}>
          <div className="flex justify-between items-start">
            <h3 className="text-xl font-semibold">{course.name}</h3>
            <button
              className="text-white"
              onClick={() => setShowActions(!showActions)}
            >
              <FaEllipsisV size={20} />
            </button>
          </div>
          <p className="mt-2 text-sm">{course.description}</p>
        </div>
        <div className="p-4">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>
              {t("tutors.coursecard.course")}
              {course.name}
            </span>
            <div className="flex items-center">
              <span className="font-semibold mr-1">{course.rating}</span>
              <FaStar size={16} color="gold" />
            </div>
          </div>
          <div className="flex items-center justify-between mt-2 text-sm text-gray-500">
            <span>
              {t("tutors.coursecard.resources")}
              {course?.resources?.toString().padStart(2, "0")}
            </span>
            <span>
              {course.reviews} {t("tutors.coursecard.reviews")}
            </span>
          </div>
          <Link
            to={"/tutor/courses/" + course.id}
            className="block text-center mt-4 w-full bg-slate-200 text-dark hover:bg-slate-300 py-2 rounded-md text-sm font-medium"
          >
            {t("tutors.coursecard.curriculum")}
          </Link>
        </div>

        {showActions && (
          <div
            ref={actionsRef}
            className="absolute top-0 right-10 bg-white shadow-lg rounded-lg z-20"
          >
            <ul className="text-sm text-dark">
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform  transition-transform duration-200">
                <FaEye className="mr-2" />
                {t("tutors.coursecard.view")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform transition-transform duration-200">
                <FaEdit className="mr-2" />
                {t("tutors.coursecard.edit")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform transition-transform duration-200">
                <FaTrash className="mr-2" />
                {t("tutors.coursecard.delete")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform  transition-transform duration-200">
                <FaBookOpen className="mr-2" />
                {t("tutors.coursecard.course_curriculum")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform  transition-transform duration-200">
                <FaShareAlt className="mr-2" />
                {t("tutors.coursecard.share")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform  transition-transform duration-200">
                <FaCopy className="mr-2" />
                {t("tutors.coursecard.duplicate_course")}
              </li>
              <li className="flex items-center p-2 hover:bg-slate-100 cursor-pointer transform  transition-transform duration-200">
                <FaArchive className="mr-2" />
                {t("tutors.coursecard.archive")}
              </li>
            </ul>
          </div>
        )}
      </>
    </motion.div>
  );
};

export default CourseCard;
