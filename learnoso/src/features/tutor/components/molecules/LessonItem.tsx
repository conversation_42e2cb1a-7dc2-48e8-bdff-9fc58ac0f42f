import { compareDates } from "@/lib/util";
import { motion } from "framer-motion";
import React from "react";
const LessonItem = ({
  lesson,
  listItemVariants,
}: {
  lesson: any;
  listItemVariants: any;
}) => {
  return (
    <motion.li
      key={lesson.id}
      className="flex justify-between items-center p-4 bg-white shadow-md rounded-lg hover:shadow-md transition"
      variants={listItemVariants}
      initial="hidden"
      animate="visible"
      transition={{ duration: 0.5, delay: 0.5 * 0.1 }}
    >
      {/* Left Section: Student Name and Course */}
      <div>
        <div className="font-medium">
          {lesson.student.first_name} {lesson.student.last_name}
        </div>
        <div className="text-sm text-gray-500">{lesson.course.name}</div>
      </div>

      {/* Right Section: Time Difference */}
      <div className="text-right">
        <div className="font-medium text-black">
          {compareDates({
            date1: lesson.starts_at,
            date2: lesson.ends_at,
          })}
        </div>
      </div>
    </motion.li>
  );
};

export default LessonItem;
