import { useGetTransactionsQuery } from "@/app/services/payment/payment.service";
import { Spinner } from "@/components/atoms";
import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import TransactionItem from "./TransactionItem";
import { t } from "i18next";

const listItemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

interface TransactionType {
  id: number;
  status: string;
  amount: number;
  description: string | null;
  created_at: string;
  type: string;
}

function Payments() {
  const {
    data: transactions,
    status: transactionsQueryStatus,
    isLoading: isLoadingPayments,
  } = useGetTransactionsQuery("transactions");
  const [transactionsData, setTransactionsData] = useState<TransactionType[]>(
    [],
  );

  useEffect(() => {
    switch (transactionsQueryStatus) {
      case "fulfilled":
        setTransactionsData(
          transactions?.data
            ?.filter((transaction: any) => transaction.status === "confirmed")
            ?.slice(0, 6),
        );
        break;
      default:
        setTransactionsData([]);
        break;
    }
  }, [transactionsQueryStatus]);

  return (
    <motion.div
      className="bg-white rounded-lg shadow p-4 mb-4 text-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {t("tutors.payment.recent_transaction")}
        </h2>
        <Link
          to="/tutor/wallet"
          className="text-primary text-sm hover:underline"
        >
          {t("tutors.payment.view_all")}
        </Link>
      </div>
      <ul className="space-y-2">
        {isLoadingPayments ? (
          <Spinner />
        ) : (
          transactionsData.map((data: TransactionType) => (
            <TransactionItem
              data={data}
              listItemVariants={listItemVariants}
              key={data.id}
            />
          ))
        )}
      </ul>
    </motion.div>
  );
}

export default Payments;
