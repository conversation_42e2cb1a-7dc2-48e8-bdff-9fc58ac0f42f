import { motion } from "framer-motion";
import moment from "moment";
import React from "react";
import { FaArrowDown, FaArrowUp } from "react-icons/fa"; // Import React Icons

// Define the type for the transaction data
interface TransactionData {
  id: number;
  type: string;
  description: string | null;
  amount: number;
  created_at: string;
}

// Define the props for the component
interface TransactionItemProps {
  data: TransactionData;
  listItemVariants: any;
}

const TransactionItem: React.FC<TransactionItemProps> = ({
  data,
  listItemVariants,
}) => {
  return (
    <motion.li
      key={data.id}
      className="flex justify-between items-center p-4 bg-white shadow-sm rounded-lg border border-slate-200 hover:shadow-md transition"
      variants={listItemVariants}
      initial="hidden"
      animate="visible"
      transition={{ duration: 0.5 }}
    >
      {/* Left Section: Type and Description */}
      <div className="flex items-center space-x-4">
        {/* Icon based on transaction type */}
        <div className="flex-shrink-0">
          {data.type === "deposit" ? (
            <FaArrowDown className="w-6 h-6 text-green-500" />
          ) : (
            <FaArrowUp className="w-6 h-6 text-red-500" />
          )}
        </div>
        <div>
          <div className="font-medium capitalize text-gray-500">
            {data.type}
          </div>
          <div className="text-sm text-gray-500">{data.description}</div>
        </div>
      </div>

      {/* Right Section: Amount and Time */}
      <div className="text-right">
        <div
          className={`font-medium ${
            data.type === "deposit" ? "text-green-500" : "text-red-500"
          }`}
        >
          {new Number(data.amount).toLocaleString()} USD
        </div>
        <div className="text-sm text-gray-500">
          {moment(data.created_at).fromNow()}
        </div>
      </div>
    </motion.li>
  );
};

export default TransactionItem;
