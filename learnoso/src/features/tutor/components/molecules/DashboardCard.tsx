import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { DashboardCardType } from "../../types";

// Number counting animation hook
const useCountUp = (targetValue: number) => {
  const [currentValue, setCurrentValue] = useState(0);

  useEffect(() => {
    const increment = targetValue / 60; // Adjust this to change the speed of counting
    const timer = setInterval(() => {
      setCurrentValue((prev) => {
        const nextValue = prev + increment;
        if (nextValue >= targetValue) {
          clearInterval(timer);
          return targetValue;
        }
        return nextValue;
      });
    }, 16); // Run every 16ms (approx 60 frames per second)

    return () => clearInterval(timer);
  }, [targetValue]);

  return Math.round(currentValue).toLocaleString(); // Format number with commas if needed
};

export const DashboardCard = ({
  icon,
  title,
  value,
  change,
  changePeriod,
  isCta = false,
}: DashboardCardType) => {
  const animatedValue = useCountUp(Number(value));

  return (
    <motion.div
      className={`${
        isCta ? "bg-primary" : "bg-white"
      } p-4 rounded-lg shadow flex flex-col w-[15rem] h-[8rem]`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      <div className={`${isCta ? "text-white" : "text-dark"} mb-2`}>{icon}</div>
      <div className={`${isCta ? "text-white" : "text-gray-500"} text-sm mb-1`}>
        {title}
      </div>
      <div className="flex justify-between items-center text-xs">
        <div
          className={`${
            isCta ? "text-white" : "text-black"
          } text-2xl font-semibold mb-1`}
        >
          {isCta ? "" + animatedValue : animatedValue}
        </div>
        {/* <div className="flex flex-col items-end">
          <span
            className={`px-2 py-1 rounded shadow-sm ${
              isCta
                ? "bg-white text-black"
                : change >= 0
                  ? "bg-[#E6F5F1] text-green-500"
                  : "bg-[#FEEFEA] text-red-500"
            } `}
          >
            {change >= 0 ? "↑" : "↓"} {Math.abs(change)}%
          </span>
          <span className={`${isCta ? "text-white" : "text-gray-500"}`}>
            Since last {changePeriod}
          </span>
        </div> */}
      </div>
    </motion.div>
  );
};
