import { useGetTutorLessonsQuery } from "@/app/services/lesson/lesson.service";
import { Spinner } from "@/components/atoms";
import Modal from "@/components/molecules/ModalWrapper";
import { UpcomingSessionCardProps } from "@/features/student/@types";
import { useAuth } from "@/hooks";
import { constructQuery, DateWizard, LocalStorage } from "@/lib/util";
import { QueryStatus } from "@reduxjs/toolkit/query";
import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { LessonStatus } from "../../types";
import { LessonRow } from "./LessonRow";

const computeLessonDuration = (starts_at: string, ends_at: string): number => {
  const start = new Date(starts_at);
  const end = new Date(ends_at);
  const duration = (end.getTime() - start.getTime()) / (1000 * 60); // duration in minutes
  return duration;
};

function LessonTable({ hasVideo }: { hasVideo?: boolean }) {
  const { user } = useAuth();
  const navigate = useNavigate();

  const {
    data,
    status: tutorLessonsQueryStatus,
    isLoading: isTutorLessonsLoading,
  } = useGetTutorLessonsQuery("tutor lessons");

  const [lessons, setLessons] = useState<UpcomingSessionCardProps[]>([]);
  const [filteredLessons, setFilteredLessons] = useState<
    UpcomingSessionCardProps[]
  >([]);

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [courseFilter, setCourseFilter] = useState("");
  const [dateFilter, setDateFilter] = useState("");

  useEffect(() => {
    switch (tutorLessonsQueryStatus as QueryStatus) {
      case "fulfilled":
        const processedLessons =
          data?.data?.map((lesson: any, index: number) => {
            LocalStorage.save("learnoso-agora-key", {
              channel_name: lesson.channel_name,
              agora_token: lesson.agora_token,
            });
            window.localStorage.setItem(
              "learnoso-agora-token",
              lesson.agora_token,
            );
            window.localStorage.setItem(
              "learnoso-agora-channel",
              lesson.channel_name,
            );
            return {
              id: lesson.id,
              title: lesson.title,
              price: 0,
              date: DateWizard.toLocaleDateTime(lesson.starts_at),
              student: lesson.student,
              duration: computeLessonDuration(lesson.starts_at, lesson.ends_at),
              videoLink: constructQuery(
                lesson.title,
                user!,
                lesson.id,
                lesson.tutor,
                lesson.student,
                lesson.starts_at,
                lesson.ends_at,
                lesson.status as LessonStatus,
                lesson.agora_token,
                lesson.channel_name,
              ),
              status: lesson.status,
              course: lesson.course,
              index,
            };
          }) || [];

        setLessons(processedLessons);
        break;
    }
  }, [tutorLessonsQueryStatus]);

  // Filter and search effect
  useEffect(() => {
    let result = lessons;

    // Search filter (across multiple fields)
    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      result = result.filter(
        (lesson) =>
          lesson.student?.user?.first_name
            ?.toLowerCase()
            .includes(searchTermLower) ||
          lesson.course?.name?.toLowerCase().includes(searchTermLower) ||
          lesson.status?.toLowerCase().includes(searchTermLower),
      );
    }

    // Status filter
    if (statusFilter) {
      result = result.filter((lesson) => lesson.status === statusFilter);
    }

    // Course filter
    if (courseFilter) {
      result = result.filter((lesson) => lesson.course?.name === courseFilter);
    }

    // Date filter
    if (dateFilter) {
      result = result.filter((lesson) => {
        const lessonDate = new Date(lesson.date);
        const filterDate = new Date(dateFilter);
        return lessonDate.toDateString() === filterDate.toDateString();
      });
    }

    setFilteredLessons(result);
  }, [lessons, searchTerm, statusFilter, courseFilter, dateFilter]);

  // Get unique courses and statuses for filter dropdowns
  const uniqueCourses = [
    ...new Set(lessons.map((lesson) => lesson.course?.name).filter(Boolean)),
  ];
  const uniqueStatuses = [
    ...new Set(lessons.map((lesson) => lesson.status).filter(Boolean)),
  ];

  if (isTutorLessonsLoading)
    return (
      <Modal isOpen onClose={() => {}}>
        <Spinner variant="default" size="large" />{" "}
      </Modal>
    );

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {t("tutors.lessontable.title")}
        </h2>
        <Link to="/tutor/lessons" className="text-blue-600 text-sm">
          {t("tutors.lessontable.view_all")}
        </Link>
      </div>

      {/* Search and Filter Section */}
      <div className="mb-4 flex flex-wrap gap-2">
        <input
          type="text"
          placeholder={t("tutors.lessontable.search_lesson")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded-md px-3 py-2 flex-grow md:w-64 text-sm placeholder-gray focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="border rounded-md px-3 py-2 text-sm md:w-40 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">{t("tutors.lessontable.all_statuses")}</option>
          {uniqueStatuses.map((status) => (
            <option key={status} value={status}>
              {status}
            </option>
          ))}
        </select>
        <select
          value={courseFilter}
          onChange={(e) => setCourseFilter(e.target.value)}
          className="border rounded-md px-3 py-2 text-sm md:w-40 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">{t("tutors.lessontable.all_courses")}</option>
          {uniqueCourses.map((course) => (
            <option key={course} value={course}>
              {course}
            </option>
          ))}
        </select>
        <input
          type="date"
          value={dateFilter}
          onChange={(e) => setDateFilter(e.target.value)}
          className="border rounded-md px-3 py-2 text-sm md:w-40 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <motion.table
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <thead className="border-b ">
          <tr className="text-left text-gray-500">
            <th className="py-2">{t("tutors.lessontable.sn")}</th>
            <th className="py-2">{t("tutors.lessontable.student")}</th>
            <th className="py-2">{t("tutors.lessontable.course")}</th>
            <th className="py-2">{t("tutors.lessontable.hour")}</th>
            <th className="py-2">{t("tutors.lessontable.date")}</th>
            <th className="py-2">{t("tutors.lessontable.amount")}</th>
            <th className="py-2">{t("tutors.lessontable.status")}</th>
          </tr>
        </thead>
        <tbody>
          {filteredLessons.length === 0 ? (
            <tr>
              <td colSpan={7} className="text-center py-4">
                {t("tutors.lessontable.no_lesson")}
              </td>
            </tr>
          ) : (
            filteredLessons.map((lesson, index) => (
              <LessonRow
                sn={`${index + 1}`}
                /*@ts-ignore */
                student={lesson.student!}
                hasVideo
                course={lesson.course?.name}
                date={lesson.date}
                hours={lesson.duration}
                key={lesson.id}
                amount={0}
                videoLink={lesson.videoLink!}
                status={lesson.status}
              />
            ))
          )}
        </tbody>
      </motion.table>
    </div>
  );
}

export default LessonTable;
