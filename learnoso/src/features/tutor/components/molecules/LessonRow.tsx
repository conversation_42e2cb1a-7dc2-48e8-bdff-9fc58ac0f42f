import IconWithTip from "@/components/atoms/IconWithTip";
import { motion } from "framer-motion";
import React from "react";
import { FaVideo } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

export const LessonRow = ({
  sn,
  student,
  course,
  hours,
  date,
  amount,
  status,
  hasVideo,
  videoLink,
}: {
  sn: string;
  student: { first_name: string; last_name: string; email: string; id: number };
  course: any;
  hours: any;
  date: any;
  amount: any;
  status: string;
  hasVideo?: boolean;
  videoLink: string;
}) => {
  const navigate = useNavigate();
  const handleVideoLink = () => {
    Date.now() > new Date(date).getTime()
      ? toast.error("Session is Due")
      : navigate(videoLink);
  };
  return (
    <motion.tr
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      <td className="py-2">{sn}</td>
      <td className="py-2">
        <div className="flex items-center">
          <img
            src="https://aui.atlassian.com/aui/9.3/docs/images/avatar-person.svg"
            alt="user"
            className="w-7 cursor-pointer mr-2"
          />
          {student.first_name} {student.last_name}
        </div>
      </td>
      <td className="py-2">{course}</td>
      <td className="py-2">{hours}</td>
      <td className="py-2">{date}</td>
      <td className="py-2">${amount}</td>
      <td className="py-2">
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            status === "Completed"
              ? "bg-[#E6F5F1] text-green-500"
              : status === "Pending"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-[#FEEFEA] text-red-500"
          }`}
        >
          {status}
        </span>
      </td>
      {hasVideo && (
        <td className="py-2" onClick={handleVideoLink}>
          <IconWithTip icon={<FaVideo />} content="Start Video lesson" />
        </td>
      )}
    </motion.tr>
  );
};
