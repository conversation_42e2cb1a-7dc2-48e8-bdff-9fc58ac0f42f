import Modal from "@/components/molecules/ModalWrapper";
import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useState } from "react";
import {
  AiOutlineBook,
  AiOutlineBulb,
  AiOutlineCalendar,
  AiOutlineCheckCircle,
  AiOutlineCheckSquare,
  AiOutlineFile,
  AiOutlineUser,
} from "react-icons/ai";
import GradeAssignmentCard from "./GradeCard";

interface LessonCardProps {
  title: string;
  description: string;
  price: number;
  duration: number;
  lessonCount: number;
  status: string;
  date: string;
  resources: number;
  curriculumCategory: string;
  assignment: number | null;
  backgroundColor: string;
}

const LessonCard: React.FC<LessonCardProps> = ({
  title,
  description,
  price,
  duration,
  lessonCount,
  status,
  date,
  resources,
  curriculumCategory,
  assignment,
  backgroundColor,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  return (
    <motion.div
      className={`rounded-lg overflow-hidden shadow-md ${backgroundColor}`}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6 text-white">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-2xl font-bold">{title}</h3>
          <div className="bg-white text-green-500 px-3 py-1 rounded text-sm">
            ${price} - {t("tutors.payment.paid")}
            <div className="text-xs text-gray-500">
              {duration} {t("tutors.payment.min_lesson")}{" "}
            </div>
          </div>
        </div>
        <p className="mb-4">{description}</p>
      </div>
      <div className="bg-white p-6">
        <div className="space-y-2">
          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineBook className="w-5 h-5 mr-2" />
            {title} - {lessonCount} {t("tutors.payment.lesson")}
          </motion.p>
          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineUser className="w-5 h-5 mr-2" />
            {t("tutors.payment.one_on_one")} -{" "}
            <span className="text-green-500 ml-1">{status}</span>
          </motion.p>
          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineCalendar className="w-5 h-5 mr-2" />
            {date}
          </motion.p>
          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineFile className="w-5 h-5 mr-2" />
            {t("tutors.payment.resource")}{" "}
            {resources.toString().padStart(2, "0")}
          </motion.p>

          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineBulb className="w-5 h-5 mr-2" />
            {t("tutors.payment.curriculum_category")} {curriculumCategory}
          </motion.p>

          <motion.p className="flex items-center" whileHover={{ scale: 1.01 }}>
            <AiOutlineCheckSquare className="w-5 h-5 mr-2" />
            {t("tutors.payment.assignment")}{" "}
            {assignment === null ? "null" : assignment}
          </motion.p>
          <div className="flex items-center mt-4">
            <AiOutlineCheckCircle className="w-5 h-5 mr-2" />
            {t("tutors.payment.status")}{" "}
            <span className="bg-green-100 text-green-500 px-2 py-1 rounded ml-2">
              {t("tutors.payment.submitted")}
            </span>
          </div>
        </div>
        <div className="flex gap-4 mt-6">
          <motion.button
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {t("tutors.payment.assign_assignment")}
          </motion.button>
          <motion.button
            className="flex-1 bg-orange text-white py-2 px-4 rounded"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setIsOpen(true)}
          >
            {t("tutors.payment.grade_assignment")}
          </motion.button>
        </div>
      </div>
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        opacity="bg-opacity-50"
      >
        <GradeAssignmentCard
          onCancel={() => setIsOpen(false)}
          onSave={() => setIsOpen(false)}
        />
      </Modal>
    </motion.div>
  );
};

export default LessonCard;
