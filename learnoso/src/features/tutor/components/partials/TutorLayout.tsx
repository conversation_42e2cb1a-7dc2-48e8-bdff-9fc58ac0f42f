import AuthGuard from "@/features/auth/components/AuthGaurd";
import { RoleGuard } from "@/features/auth/components/RoleGuard";
import React from "react";
import { Outlet } from "react-router-dom";
import TutorHeader from "./TutorHeader";

function TutorLayout() {
  return (
    <AuthGuard>
      <RoleGuard allowedRoles={["tutor"]}>
        <TutorHeader />
        <div className="bg-slate-100 min-h-screen mx-auto px-28 py-8">
          <Outlet />
        </div>
      </RoleGuard>
    </AuthGuard>
  );
}

export default TutorLayout;
