import React from "react";
import useTutorContext from "../../hooks/useTutorContext";
import { t } from "i18next";

export function withCourseComponent(
  Component: React.FC<any>,
  EditingComponent?: React.FC<any>,
) {
  return function ComponentWithEditing(props: any) {
    const { isEditingComponent } = useTutorContext();
    return isEditingComponent ? (
      (EditingComponent && <EditingComponent {...props} />) || (
        <>{t("tutors.withcoursecomponent.editing_component")} </>
      )
    ) : (
      <Component {...props} />
    );
  };
}

const TestComponent = ({ prop }: { prop: any }) => {
  return (
    <div>
      {t("tutors.withcoursecomponent.test_component")} {prop}
    </div>
  );
};

const EditTestComponent = ({ prop }: { prop: any }) => {
  return (
    <div>
      {t("tutors.withcoursecomponent.edit_test_component")}
      {prop}
    </div>
  );
};

export const EditableTestComponent = withCourseComponent(
  TestComponent,
  EditTestComponent,
);
