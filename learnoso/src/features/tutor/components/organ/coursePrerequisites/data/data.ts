import { Concept, Resource, Tool } from "@/features/tutor/types";

export const sampleConcepts: Concept[] = [
  { id: 1, name: "Advanced Mechanics" },
  { id: 2, name: "Electromagnetic Theory" },
  { id: 3, name: "Optics and Modern Physics" },
  { id: 4, name: "Thermodynamics and Statistical Mechanics" },
  { id: 5, name: "Nuclear and Particle Physics" },
];

export const sampleTools: Tool[] = [
  { id: 1, name: "Figma" },
  { id: 2, name: "Pen" },
  { id: 3, name: "Book for sketching" },
];

export const sampleResources: Resource[] = [
  {
    id: 1,
    title: "Beginners guide to UI/UX Design",
    type: "Document",
    filename: "Beginners-guide.pdf",
    link: "https://www.behance.net/search/projects/announcement%20section%20%20design?tracking_source=typeahead_search_direct&",
  },
  {
    id: 2,
    title: "Beginners guide to Accessibility in design",
    type: "Link",
    filename: "",
    link: "https://www.behance.net/search/projects/announcement%20section%20%20design?tracking_source=typeahead_search_direct&",
  },
];
export const concepts: Concept[] = [
  { id: 1, name: "Advanced Mechanics" },
  { id: 2, name: "Electromagnetic Theory" },
  { id: 3, name: "Optics and Modern Physics" },
  { id: 4, name: "Thermodynamics and Statistical Mechanics" },
  { id: 5, name: "Nuclear and Particle Physics" },
];

export const tools: Tool[] = [
  { id: 1, name: "Figma" },
  { id: 2, name: "Pen" },
  { id: 3, name: "Book for sketching" },
];

export const resources: Resource[] = [
  {
    id: 1,
    title: "Beginners guide to UI/UX Design",
    type: "Document",
    filename: "Beginners-guide.pdf",
    link: "https://www.behance.net/search/projects/announcement%20section%20%20design?tracking_source=typeahead_search_direct&",
  },
  {
    id: 2,
    title: "Beginners guide to Accessibility in design",
    type: "Link",
    filename: "",
    link: "https://www.behance.net/search/projects/announcement%20section%20%20design?tracking_source=typeahead_search_direct&",
  },
];
