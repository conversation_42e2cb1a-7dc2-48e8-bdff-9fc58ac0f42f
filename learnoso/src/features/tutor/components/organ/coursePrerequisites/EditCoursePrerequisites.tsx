import { Resource } from "@/features/tutor/types";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { MdDescription } from "react-icons/md";
import AddResourceModal from "./components/AddResourceModal";
import ConceptComponent from "./components/ConceptComponent";
import ResourceComponent from "./components/ResourceComponent";
import ToolComponent from "./components/ToolComponent";
import { sampleConcepts, sampleResources, sampleTools } from "./data/data";

const EditCoursePrerequisites = () => {
  // Concepts
  const [concepts, setConcepts] = useState(sampleConcepts);

  const handleEditConcept = (id: number, newName: string) => {
    setConcepts((current) => {
      const updatedConcepts = current.map((concept) =>
        concept.id === id ? { ...concept, name: newName } : concept,
      );
      return updatedConcepts;
    });
  };

  const handleDeleteConcept = (id: number) => {
    setConcepts((current) => {
      return current.filter((concept) => concept.id !== id);
    });
  };

  const handleAddConcept = () => {
    const newConcept = {
      id: concepts.length + 1,
      name: "This is the name for the new Concept",
    };
    setConcepts((current) => [...current, newConcept]);
  };

  // Tools
  const [tools, setTools] = useState(sampleTools);

  const handleEditTool = (id: number, newName: string) => {
    setTools((current) => {
      const updatedTools = current.map((concept) =>
        concept.id === id ? { ...concept, name: newName } : concept,
      );
      return updatedTools;
    });
  };

  const handleDeleteTool = (id: number) => {
    setTools((current) => {
      return current.filter((concept) => concept.id !== id);
    });
  };

  const handleAddTool = () => {
    const newTool = {
      id: tools.length + 1,
      name: "This is the name for the new tool",
    };
    setTools((current) => [...current, newTool]);
  };

  // Resources
  const [resources, setResources] = useState(sampleResources);
  const [isAddingResource, setIsAddingResource] = useState(false);

  const handleEditResource = (id: number, data: Resource) => {
    const updatedResources = resources.map((resource) =>
      resource.id === id ? { ...data } : resource,
    );
    setResources(updatedResources);
  };

  const handleDeleteResource = (id: number) => {
    setResources((current) => current.filter((resource) => resource.id === id));
  };

  const handleAddResource = (data: Resource) => {
    const newResource = { ...data, id: resources.length + 1 };
    setResources((current) => [...current, newResource]);
  };

  const handleCloseAddModal = () => {
    setIsAddingResource(false);
  };
  const { t } = useTranslation();
  return (
    <>
      <div className="space-y-4">
        <div className="">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              {t("tutors.editcourseprerequisites.concepts")}{" "}
            </h2>
            <p className="text-gray-500 mb-4">
              {t("tutors.editcourseprerequisites.description")}
            </p>
            <ul className="list-decimal pl-5">
              {concepts.map((concept) => (
                <ConceptComponent
                  key={concept.id}
                  id={concept.id}
                  name={concept.name}
                  handleEditConcept={handleEditConcept}
                  handleDeleteConcept={handleDeleteConcept}
                />
              ))}
            </ul>
            <button
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={handleAddConcept}
            >
              + {t("tutors.editcourseprerequisites.add_concepts")}
            </button>
          </div>
        </div>
        <div>
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              {" "}
              {t("tutors.editcourseprerequisites.tools")}
            </h2>
            <p className="text-gray-500 mb-4">
              {" "}
              {t("tutors.editcourseprerequisites.tool_needed")}
            </p>
            <ul className="list-decimal pl-5">
              {tools.map((tool) => (
                <ToolComponent
                  id={tool.id}
                  name={tool.name}
                  handleEditTool={handleEditTool}
                  handleDeleteTool={handleDeleteTool}
                />
              ))}
            </ul>
            <button
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={handleAddTool}
            >
              + {t("tutors.editcourseprerequisites.add_tool")}
            </button>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold flex items-center">
              <MdDescription className="mr-2 text-dark" /> {/* Updated icon */}
              {t("tutors.editcourseprerequisites.resources")}
            </h2>
            <button
              className="text-blue-600 font-medium flex items-center"
              onClick={() => setIsAddingResource(true)}
            >
              <span className="mr-1">+</span>{" "}
              {t("tutors.editcourseprerequisites.add_resources")}
            </button>
          </div>
          {resources.map((resource) => (
            <ResourceComponent
              resource={resource}
              handleDeleteResource={handleDeleteResource}
              handleEditResource={handleEditResource}
            />
          ))}
        </div>
      </div>
      {isAddingResource && (
        <AddResourceModal
          handleAddResource={handleAddResource}
          handleClose={handleCloseAddModal}
          isOpen={isAddingResource}
        />
      )}
    </>
  );
};

export default EditCoursePrerequisites;
