import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import { MdDescription, MdLink } from "react-icons/md";
import { concepts, resources, tools } from "./data/data";

const CoursePrerequisites: React.FC = () => {
  const { t } = useTranslation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.2 }}
      className=""
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              {t("tutors.courseprerequisites.concepts")}
            </h2>
            <p className="text-gray-500 mb-4">
              {t("tutors.courseprerequisites.description")}
            </p>
            <ul className="list-decimal pl-5">
              {concepts.map((concept) => (
                <li key={concept.id} className="text-dark mb-2">
                  {concept.name}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div>
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">
              {" "}
              {t("tutors.courseprerequisites.tools")}
            </h2>
            <p className="text-gray-500 mb-4">
              {" "}
              {t("tutors.courseprerequisites.tool_needed")}{" "}
            </p>
            <ul className="list-decimal pl-5">
              {tools.map((tool) => (
                <li key={tool.id} className="text-dark mb-2">
                  {tool.name}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      <div className="mt-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold flex items-center">
              <MdDescription className="mr-2 text-dark" /> {/* Updated icon */}
              {t("tutors.courseprerequisites.resources")}
            </h2>
            <button className="text-blue-600 font-medium flex items-center">
              <span className="mr-1">+</span>{" "}
              {t("tutors.courseprerequisites.add_resources")}
            </button>
          </div>
          {resources.map((resource) => (
            <div key={resource.id} className="border-t border-slate-400 py-4">
              <h3 className="font-semibold mb-2">
                {resource.id}. {resource.title}
              </h3>
              <div className="flex items-center text-gray-500">
                <span className="mr-4">{resource.type}</span>
                {resource.type === "Document" && (
                  <span>{resource.filename}</span>
                )}
              </div>
              <a
                href={resource.link}
                className="text-blue-600 flex items-center mt-2"
                target="_blank"
                rel="noopener noreferrer"
              >
                <MdLink className="mr-1" size={16} /> {/* Updated icon */}
                {resource.link}
              </a>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default CoursePrerequisites;
