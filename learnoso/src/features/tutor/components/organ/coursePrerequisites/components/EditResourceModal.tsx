import Modal from "@/components/molecules/ModalWrapper";
import { Resource } from "@/features/tutor/types";
import React from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

const EditResourceModal: React.FC<{
  resource: Resource;
  handleEditResource: (id: number, data: Resource) => void;
  handleClose: () => void;
  isOpen: boolean;
}> = ({ resource, handleEditResource, handleClose, isOpen }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Resource>({
    defaultValues: {
      ...resource,
    },
  });

  const onSubmit = (data: Resource) => {
    handleEditResource(resource.id, data);
    handleClose();
  };
  const { t } = useTranslation();
  return (
    <Modal isOpen={isOpen} onClose={handleClose} opacity="bg-opacity-25">
      <div className="bg-white p-8 rounded-md text-dark max-w-2xl w-full">
        <h1 className="text-primary font-bold text-2xl text-center mb-4">
          {t("tutors.courseprerequisites.editresourcemodal.edit_resource")}
        </h1>
        <form className="flex flex-col gap-2" onSubmit={handleSubmit(onSubmit)}>
          <label className="space-y-1">
            <div>{t("tutors.courseprerequisites.editresourcemodal.title")}</div>
            <input
              type="text"
              {...register("title")}
              className="w-full p-2 outline-none border border-dark/40 rounded-md"
            />
          </label>
          <label className="space-y-1">
            <div>{t("tutors.courseprerequisites.editresourcemodal.type")}</div>
            <input
              type="text"
              {...register("type")}
              className="w-full p-2 outline-none border border-dark/40 rounded-md"
            />
          </label>
          <label className="space-y-1">
            <div>
              {t("tutors.courseprerequisites.editresourcemodal.filename")}
            </div>
            <input
              type="text"
              {...register("filename")}
              className="w-full p-2 outline-none border border-dark/40 rounded-md"
            />
          </label>
          <label className="space-y-1">
            <div>{t("tutors.courseprerequisites.editresourcemodal.link")}</div>
            <input
              type="text"
              {...register("link", {
                required: {
                  value: true,
                  message: "This field is required",
                },
                pattern: {
                  value:
                    /[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gi,
                  message: "invalid pattern",
                },
              })}
              className="w-full p-2 outline-none border border-dark/40 rounded-md"
            />
            {errors.link && <p className="error">{errors.link.message}</p>}
          </label>
          <div className="flex gap-4 justify-between items-center mt-4">
            <button
              className="bg-blue-700 hover:bg-primary border border-primary text-white py-2 px-6 rounded-md text-lg font-semibold transition duration-300 ease-in-out"
              type="submit"
            >
              {t("tutors.courseprerequisites.editresourcemodal.save")}
            </button>
            <button
              className="bg-white border border-primary hover:bg-primary/10 text-primary py-2 px-6 rounded-md text-lg font-semibold transition duration-300 ease-in-out"
              type="button"
              onClick={handleClose}
            >
              {t("tutors.courseprerequisites.editresourcemodal.cancel")}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default EditResourceModal;
