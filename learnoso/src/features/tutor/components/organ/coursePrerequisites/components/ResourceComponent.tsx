import { Resource } from "@/features/tutor/types";
import React, { useState } from "react";
import { MdLink } from "react-icons/md";
import EditResourceModal from "./EditResourceModal";

const ResourceComponent: React.FC<{
  resource: Resource;
  handleDeleteResource: (id: number) => void;
  handleEditResource: (id: number, data: Resource) => void;
}> = ({ resource, handleDeleteResource, handleEditResource }) => {
  const [isEditing, setIsEditing] = useState(false);

  const handleClose = () => {
    setIsEditing(false);
  };

  return (
    <>
      <div className="border-t border-slate-400 py-4 flex gap-2 items-center">
        <div>
          <h3 className="font-semibold mb-2">
            {resource.id}. {resource.title}
          </h3>
          <div className="flex items-center text-gray-500">
            <span className="mr-4">{resource.type}</span>
            {resource.type === "Document" && <span>{resource.filename}</span>}
          </div>
          <a
            href={resource.link}
            className="text-blue-600 flex items-center mt-2"
            target="_blank"
            rel="noopener noreferrer"
          >
            <MdLink className="mr-1" size={16} />
            {resource.link}
          </a>
        </div>
        <div className="flex flex-col gap-4">
          <button
            className="ml-2 text-blue-500"
            onClick={() => setIsEditing(true)}
          >
            ✏️
          </button>
          <button
            className="ml-2 text-red-500-500"
            onClick={() => handleDeleteResource(resource.id)}
          >
            🗑️
          </button>
        </div>
      </div>
      {isEditing && (
        <EditResourceModal
          resource={resource}
          handleClose={handleClose}
          handleEditResource={handleEditResource}
          isOpen={isEditing}
        />
      )}
    </>
  );
};

export default ResourceComponent;
