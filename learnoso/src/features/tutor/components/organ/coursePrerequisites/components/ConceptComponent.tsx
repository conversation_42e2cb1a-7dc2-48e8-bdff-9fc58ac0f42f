import React, { useState } from "react";

const ConceptComponent: React.FC<{
  id: number;
  name: string;
  handleEditConcept: (id: number, newName: string) => void;
  handleDeleteConcept: (id: number) => void;
}> = ({ id, name, handleEditConcept, handleDeleteConcept }) => {
  const [isEditing, setIsEditing] = useState(false);
  return (
    <li className="text-dark mb-2">
      {isEditing ? (
        <input
          type="text"
          value={name}
          onChange={(e) => handleEditConcept(id, e.target.value)}
          onBlur={() => setIsEditing(false)}
          autoFocus
          className="border border-gray-300 p-1 rounded"
        />
      ) : (
        <div>
          <div className="flex gap-2 items-center">
            {name}
            <button
              className="ml-2 text-blue-500"
              onClick={() => setIsEditing(true)}
            >
              ✏️
            </button>
            <button
              className="ml-2 text-red-500-500"
              onClick={() => handleDeleteConcept(id)}
            >
              🗑️
            </button>
          </div>
        </div>
      )}
    </li>
  );
};

export default ConceptComponent;
