import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import { questions } from "./data/data";

const CourseModuleQuestions: React.FC = () => {
  const { t } = useTranslation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.2 }}
      className="bg-slate-100 my-2"
    >
      <ul className="space-y-6">
        {questions.map((question, index) => (
          <li key={question.id} className="flex items-start ">
            <div className="flex-shrink-0 w-5 h-5 mt-1 mr-4 relative z-10">
              <div className="w-full h-full border-2 border-slate-500 rounded-full"></div>
            </div>
            <div className="flex-grow  bg-white p-2 rounded-md">
              <h3 className="text-lg font-semibold mb-2">
                {t("tutors.couremodulequestions.question")} {index + 1}
              </h3>
              <p className="text-gray-500">{question.content}</p>
            </div>
          </li>
        ))}
      </ul>
    </motion.div>
  );
};

export default CourseModuleQuestions;
