import { Question } from "@/features/tutor/types";
import React, { useState } from "react";

const QuestionComponent: React.FC<{
  question: Question;
  handleEditQuestion: (id: number, data: string) => void;
  handleDeleteQuestion: (id: number) => void;
  index: number;
}> = ({ question, handleEditQuestion, handleDeleteQuestion, index }) => {
  const [isEditing, setIsEditing] = useState(false);

  return (
    <li className="flex items-start">
      <div className="flex-shrink-0 w-5 h-5 mt-1 mr-4 relative z-10">
        <div className="w-full h-full border-2 border-slate-500 rounded-full"></div>
      </div>
      <div className="flex-grow  bg-white p-2 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Question {index + 1}</h3>
        <div>
          {isEditing ? (
            <textarea
              onFocus={(e) => {
                e.target.style.height = "";
                e.target.style.height = e.target.scrollHeight + 3 + "px";
              }}
              onChange={(e) => {
                handleEditQuestion(question.id, e.target.value);
                e.target.style.height = "";
                e.target.style.height = e.target.scrollHeight + 3 + "px";
              }}
              onBlur={() => setIsEditing(false)}
              value={question.content}
              autoFocus
              className="border border-gray-300 rounded w-full outline-none p-2"
            ></textarea>
          ) : (
            <div>
              <p className="text-gray-500">
                {question.content}
                <button
                  className="ml-2 text-blue-500"
                  onClick={() => setIsEditing(true)}
                >
                  ✏️
                </button>
                <button
                  className="ml-2 text-red-500-500"
                  onClick={() => handleDeleteQuestion(question.id)}
                >
                  🗑️
                </button>
              </p>
            </div>
          )}
        </div>
      </div>
    </li>
  );
};

export default QuestionComponent;
