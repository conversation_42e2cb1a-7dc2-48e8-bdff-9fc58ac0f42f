import useTutorContext from "@/features/tutor/hooks/useTutorContext";
import { Question } from "@/features/tutor/types";
import { motion } from "framer-motion";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaSave } from "react-icons/fa";
import QuestionComponent from "./components/QuestionComponent";
import { questions as _questions } from "./data/data";

const EditCourseModuleQuestions: React.FC = () => {
  const [questions, setQuestions] = useState(_questions);

  const { setIsEditingComponent } = useTutorContext();

  const handleEditQuestion = (id: number, content: string) => {
    const updatedQuestions = questions.map((question) =>
      id === question.id ? { ...question, content: content } : question,
    );
    setQuestions(updatedQuestions);
  };

  const handleDeleteQuestion = (id: number) => {
    setQuestions((current) => current.filter((question) => question.id !== id));
  };

  const handleAddQuestion = () => {
    const newQuestion: Question = {
      id: questions.length + 1,
      content: "This the content for the question added",
    };
    setQuestions((current) => [...current, newQuestion]);
  };
  const { t } = useTranslation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.2 }}
      className="bg-slate-100 my-2"
    >
      <ul className="space-y-6">
        {questions.map((question, index) => (
          <QuestionComponent
            question={question}
            key={question.id}
            handleEditQuestion={handleEditQuestion}
            index={index}
            handleDeleteQuestion={handleDeleteQuestion}
          />
        ))}
      </ul>
      <div className="flex gap-4 items-center justify-end mt-4">
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          onClick={handleAddQuestion}
        >
          {t(
            "tutors.couremodulequestions.editcoursemodulequestion.add_question",
          )}{" "}
          +
        </button>
        <button
          className="mt-4 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-500/50 flex items-center gap-2"
          onClick={() => setIsEditingComponent(false)}
        >
          <FaSave className="inline-block" />
          {t(
            "tutors.couremodulequestions.editcoursemodulequestion.save_changes",
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default EditCourseModuleQuestions;
