import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { useTranslation } from "react-i18next";
import { FaSearch } from "react-icons/fa";
import { MdMessage, MdStar, MdThumbUp } from "react-icons/md";

interface RatingSummary {
  average: number;
  total: number;
  distribution: { [key: number]: number };
}

interface Review {
  id: number;
  author: string;
  avatar: string;
  rating: number;
  content: string;
  votes: number;
  replies: number;
  timeAgo: string;
}

const ratingSummary: RatingSummary = {
  average: 4.3,
  total: 8,
  distribution: {
    5: 60,
    4: 20,
    3: 11,
    2: 7,
    1: 2,
  },
};

const reviews: Review[] = [
  {
    id: 1,
    author: "<PERSON>",
    avatar: "https://i.pravatar.cc/100?img=62",
    rating: 4,
    content: t("tutors.coursereviews.recommend"),
    votes: 5,
    replies: 2,
    timeAgo: t("tutors.coursereviews.min_ago", { value: `10` }),
  },
  {
    id: 2,
    author: "<PERSON>",
    avatar: "https://i.pravatar.cc/100?img=12",
    rating: 5,
    content: t("tutors.coursereviews.nice"),
    votes: 5,
    replies: 2,
    timeAgo: t("tutors.coursereviews.min_ago", { value: `10` }),
  },
  {
    id: 3,
    author: "John Doe",
    avatar: "https://i.pravatar.cc/100?img=37",
    rating: 5,
    content: "Lala lala lala lalal lalal lalalalalalalala.",
    votes: 5,
    replies: 2,
    timeAgo: t("tutors.coursereviews.min_ago", { value: `10` }),
  },
  {
    id: 4,
    author: "Mary Jones",
    avatar: "https://i.pravatar.cc/100?img=30",
    rating: 1,
    content: t("tutors.coursereviews.you_better"),
    votes: 1,
    replies: 0,
    timeAgo: t("tutors.coursereviews.min", { value: `50` }),
  },
];

// const reviews = t("coursereviews.reviews", {
//   returnObjects: true,
// }) as Array<{
//   id: number;
//   author: string;
//   avatar: string;
//   rating: number;
//   content: string;
//   votes: number;
//   replies: number;
//   timeAgo: string;
// }>;

const StarRating: React.FC<{ rating: number }> = ({ rating }) => (
  <div className="flex">
    {[1, 2, 3, 4, 5].map((star) => (
      <MdStar
        key={star}
        size={16}
        className={
          star <= rating ? "text-orange-500 fill-current" : "text-slate-400"
        }
      />
    ))}
  </div>
);

const CourseReviewsSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.2 }}
      className=""
    >
      <h2 className="text-2xl font-semibold mb-6">
        {t("tutors.coursereviews.student_feedback")}
      </h2>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center mb-6">
          <div className="text-5xl font-bold text-orange-500 mr-4">
            {ratingSummary.average}
          </div>
          <div className="flex-grow">
            {Object.entries(ratingSummary.distribution)
              .reverse()
              .map(([rating, percentage]) => (
                <div key={rating} className="flex items-center mb-1">
                  <StarRating rating={parseInt(rating)} />
                  <div className="w-full bg-slate-200 rounded-full h-2 ml-2">
                    <div
                      className="bg-orange-500 h-2 rounded-full"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-500 ml-2">
                    {percentage}%
                  </span>
                </div>
              ))}
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4 flex items-center">
            {t("tutors.coursereviews.review")}
            <span className="ml-2 bg-orange-500 text-white text-sm rounded-full px-2 py-1">
              {ratingSummary.total}
            </span>
          </h3>
          <div className="flex justify-between">
            <div className="relative">
              <select className="appearance-none bg-slate-200 border border-slate-400 text-dark py-2 px-4 pr-8 rounded leading-tight focus:outline-none focus:bg-white focus:border-gray">
                <option>{t("tutors.coursereviews.all_ratings")}</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-dark">
                <MdStar className="fill-current h-4 w-4" />
              </div>
            </div>
            <div className="relative">
              <input
                type="text"
                placeholder={t("tutors.coursereviews.search_here_placeholder")}
                className="bg-slate-200 border border-slate-400 text-dark py-2 px-4 pr-10 rounded leading-tight focus:outline-none focus:bg-white focus:border-gray"
              />
              <div className="absolute inset-y-0 right-0 flex items-center px-2">
                <FaSearch className="text-gray-500" size={20} />
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-t border-slate-300 pt-4">
              <div className="flex items-start">
                <img
                  src={review.avatar}
                  alt={review.author}
                  className="w-10 h-10 rounded-full mr-4"
                />
                <div className="flex-grow">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">{review.author}</h4>
                    <span className="text-sm text-gray-500">
                      {review.timeAgo}
                    </span>
                  </div>
                  <StarRating rating={review.rating} />
                  <p className="mt-2 text-dark">{review.content}</p>
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <MdThumbUp size={16} className="mr-1" />
                    <span className="mr-4">
                      {review.votes} {t("tutors.coursereviews.votes")}
                    </span>
                    <MdMessage size={16} className="mr-1" />
                    <span>
                      {review.replies} {t("tutors.coursereviews.replies")}
                    </span>
                  </div>
                </div>
              </div>
              <button className="mt-2 text-blue-600 text-sm font-medium">
                {t("tutors.coursereviews.reply")}
              </button>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <button className="bg-blue-100 text-blue-600 px-4 py-2 rounded-md font-medium">
            {t("tutors.coursereviews.see_more_reviews")}
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default CourseReviewsSection;
