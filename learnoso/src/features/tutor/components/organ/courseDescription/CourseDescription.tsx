import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { FaClock, FaGlobe, FaStar, FaUserGraduate } from "react-icons/fa";
import { CourseDetail } from "../../../types";

const courseDetail: CourseDetail = {
  title: t("tutors.coursedescription.ui_ux"),
  description: t("tutors.coursedescription.description_lerom"),
  shortDescription: t("tutors.coursedescription.short_description"),
  language: t("tutors.coursedescription.language"),
  skillLevel: t("tutors.coursedescription.beginner"),
  lastUpdated: t("tutors.coursedescription.november", { value: `2024` }),
  currency: "FCFA",
  pricePerHour: 2000,
  rating: 4.7,
  students: 52,
  totalHours: 11.5,
};

function CourseDescription() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, delay: 0.2 }}
    >
      <section className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">
          {t("tutors.coursedescription.description")}
        </h3>
        <p className="text-gray-500 mb-4">{courseDetail.description}</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <FaGlobe className="mr-2" />
            <span>{courseDetail.language}</span>
          </div>
          <div className="flex items-center">
            <FaUserGraduate className="mr-2" />
            <span>
              {t("tutors.coursedescription.skill_level")}
              {courseDetail.skillLevel}
            </span>
          </div>
          <div className="flex items-center">
            <FaClock className="mr-2" />
            <span>
              {t("tutors.coursedescription.last_updated")}
              {courseDetail.lastUpdated}
            </span>
          </div>
        </div>
      </section>

      <section className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">
          {t("tutors.coursedescription.pricing")}
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-gray-500">
              {t("tutors.coursedescription.currency")}
            </p>
            <p className="font-semibold">{courseDetail.currency}</p>
          </div>
          <div>
            <p className="text-gray-500">
              {t("tutors.coursedescription.price_per_hour")}
            </p>
            <p className="font-semibold">
              {courseDetail.pricePerHour.toLocaleString()}
            </p>
          </div>
        </div>
      </section>

      <section className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-3xl font-bold mr-2">
              {courseDetail.rating}
            </span>
            <FaStar className="text-yellow-400" />
          </div>
          <div className="text-right">
            <p className="font-semibold">
              {courseDetail.students}
              {t("tutors.coursedescription.students")}
            </p>
            <p className="text-gray-500">
              {courseDetail.totalHours}{" "}
              {t("tutors.coursedescription.hours_total")}
            </p>
          </div>
        </div>
      </section>
    </motion.div>
  );
}

export default CourseDescription;
