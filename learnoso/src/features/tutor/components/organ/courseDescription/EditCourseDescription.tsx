import useTutorContext from "@/features/tutor/hooks/useTutorContext";
import { CourseDetail } from "@/features/tutor/types";
import { currencies } from "@/lib/util";
import React, { useState } from "react";
import { FaGlobe, FaSave, FaStar, FaUserGraduate } from "react-icons/fa";

import { t } from "i18next";

const courseDetail: CourseDetail = {
  title: t("tutors.editcoursedescription.ui_ux"),
  description: t("tutors.editcoursedescription.description_lerom"),
  shortDescription: t("tutors.editcoursedescription.short_description"),
  language: t("tutors.editcoursedescription.language"),
  skillLevel: t("tutors.editcoursedescription.beginner"),
  lastUpdated: t("tutors.editcoursedescription.november", { value: `2024` }),
  currency: "FCFA",
  pricePerHour: 2000,
  rating: 4.7,
  students: 52,
  totalHours: 11.5,
};

const languages: string[] = [
  "English",
  "Spanish",
  "Mandarin",
  "Hindi",
  "Arabic",
  "Portuguese",
  "Russian",
  "Japanese",
  "German",
];

const skillLevels: string[] = [
  t("tutors.editcoursedescription.beginner"),
  t("tutors.editcoursedescription.intermediate"),
  t("tutors.editcoursedescription.advanced"),
];

const EditCourseDescription = () => {
  const [description, setDescription] = useState(courseDetail.description);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [language, setLanguage] = useState(courseDetail.language);
  const [skillLevel, setSkillLevel] = useState(courseDetail.skillLevel);
  const [currency, setCurrency] = useState(courseDetail.currency);
  const [isEditingPricePerHour, setIsEditingPricePerHour] = useState(false);
  const [pricePerHour, setPricePerHour] = useState(courseDetail.pricePerHour);

  const { setIsEditingComponent } = useTutorContext();

  return (
    <div>
      <section className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">
          {t("tutors.editcoursedescription.description")}
        </h3>
        {isEditingDescription ? (
          <textarea
            onChange={(e) => {
              setDescription(e.target.value);
              e.target.style.height = "";
              e.target.style.height = e.target.scrollHeight + 3 + "px";
            }}
            onFocus={(e) => {
              e.target.style.height = "";
              e.target.style.height = e.target.scrollHeight + 3 + "px";
            }}
            onBlur={() => setIsEditingDescription(false)}
            value={description}
            autoFocus
            className="border border-gray-300 rounded w-full outline-none p-2"
          />
        ) : (
          <p className="text-gray-500 mb-4">
            {description}
            <button
              className="ml-2 text-blue-500"
              onClick={() => setIsEditingDescription(true)}
            >
              ✏️
            </button>
          </p>
        )}
        <div className="space-y-2">
          <label className="flex items-center">
            <FaGlobe className="mr-2" />
            <select
              name="language"
              className="outline-none"
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
            >
              {languages.map((language, index) => (
                <option key={index} value={language}>
                  {language}
                </option>
              ))}
            </select>
          </label>
          <label className="flex items-center">
            <FaUserGraduate className="mr-2" />
            <div>
              {t("tutors.editcoursedescription.skill_level")}
              <select
                name="skillLevel"
                className="outline-none"
                value={skillLevel}
                onChange={(e) => setSkillLevel(e.target.value)}
              >
                {skillLevels.map((level, index) => (
                  <option key={index} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>
          </label>
        </div>
      </section>

      <section className="bg-white rounded-lg shadow p-6 mb-6">
        <h3 className="text-xl font-semibold mb-4">
          {t("tutors.editcoursedescription.pricing")}Pricing
        </h3>
        <div className="space-y-4">
          <div className="flex gap-4">
            <p className="text-gray-500">
              {t("tutors.editcoursedescription.currency")}
            </p>
            <select
              name="currency"
              className="font-semibold pr-2"
              value={currency}
              onChange={(e) => setCurrency(e.target.value)}
            >
              {currencies.map((currency, index) => (
                <option value={currency} key={index}>
                  {currency}
                </option>
              ))}
            </select>
          </div>
          <div className="flex gap-4 items-center">
            <p className="text-gray-500">
              {t("tutors.editcoursedescription.price_per_hour")}
            </p>
            {isEditingPricePerHour ? (
              <input
                type="number"
                value={pricePerHour}
                onChange={(e) => setPricePerHour(Number(e.target.value))}
                onBlur={() => setIsEditingPricePerHour(false)}
                autoFocus
                className="border border-gray-300 p-1 rounded"
              />
            ) : (
              <p className="font-semibold">
                {pricePerHour.toLocaleString()}
                <button
                  className="ml-2 text-blue-500"
                  onClick={() => setIsEditingPricePerHour(true)}
                >
                  ✏️
                </button>
              </p>
            )}
          </div>
        </div>
      </section>

      <section className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-3xl font-bold mr-2">
              {courseDetail.rating}
            </span>
            <FaStar className="text-yellow-400" />
          </div>
          <div className="text-right">
            <p className="font-semibold">
              {courseDetail.students}{" "}
              {t("tutors.editcoursedescription.students")}
            </p>
            <p className="text-gray-500">
              {courseDetail.totalHours}{" "}
              {t("tutors.editcoursedescription.hours_total")}
            </p>
          </div>
        </div>
      </section>
      <div>
        <button
          className="mt-4 px-4 py-2 bg-green text-white rounded hover:bg-green-500/50 flex items-center gap-2"
          onClick={() => {
            setIsEditingComponent(false);
          }}
        >
          <FaSave className="inline-block" />
          {t("tutors.editcoursedescription.save_changes")}
        </button>
      </div>
    </div>
  );
};

export default EditCourseDescription;
