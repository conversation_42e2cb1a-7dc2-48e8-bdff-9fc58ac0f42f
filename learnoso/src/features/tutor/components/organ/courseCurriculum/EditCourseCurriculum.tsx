import useTutorContext from "@/features/tutor/hooks/useTutorContext";
import { motion } from "framer-motion";
import { t } from "i18next";
import React, { useState } from "react";
import { FaSave } from "react-icons/fa";

interface Module {
  id: number;
  title: string;
  description: string;
}

interface Category {
  id: number;
  name: string;
  modules: Module[];
}

const initialCategories: Category[] = [
  {
    id: 1,
    name: "<PERSON><PERSON><PERSON>",
    modules: Array(5)
      .fill(0)
      .map((_, index) => ({
        id: index,
        title: t("tutor.organs.editcurriculum.module", { index: index + 1 }),
        description: "This is the description for the module",
      })),
  },
  {
    id: 2,
    name: "Intermediate",
    modules: Array(5)
      .fill(0)
      .map((_, index) => ({
        id: index,
        title: t("tutor.organs.editcurriculum.module", { index: index + 1 }),
        description: "This is the description for the module",
      })),
  },
  { id: 3, name: "Advanced", modules: [] },
  { id: 4, name: "Custom", modules: [] },
];

function ModuleItem({
  module,
  handleDelete,
  handleTitleChange,
  handleDescriptionChange,
  index,
  moveModule,
}: {
  module: Module;
  handleDelete: (id: number) => void;
  handleTitleChange: (id: number, title: string) => void;
  handleDescriptionChange: (id: number, description: string) => void;
  index: number;
  moveModule: (dragIndex: number, hoverIndex: number) => void;
}) {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [tempTitle, setTempTitle] = useState(module.title);
  const [tempDescription, setTempDescription] = useState(module.description);

  const handleClickAwayTitle = () => {
    setIsEditingTitle(false);
    handleTitleChange(module.id, tempTitle);
  };

  const handleClickAwayDescription = () => {
    setIsEditingDescription(false);
    handleDescriptionChange(module.id, tempDescription);
  };

  return (
    <motion.div
      layout
      drag="y"
      dragConstraints={{ top: 0, bottom: 0 }}
      dragElastic={0.2}
      onDragEnd={(event, info) => {
        const dragDistance = Math.round(info.offset.y / 50);
        moveModule(index, index + dragDistance);
      }}
      className="mb-2 flex items-center justify-between"
    >
      <div>
        {isEditingTitle ? (
          <input
            type="text"
            value={tempTitle}
            onChange={(e) => setTempTitle(e.target.value)}
            onBlur={handleClickAwayTitle}
            autoFocus
            className="border border-gray-300 p-1 rounded"
          />
        ) : (
          <h4 className="font-medium flex items-center">
            {module.title}
            <button
              className="ml-2 text-blue-500"
              onClick={() => setIsEditingTitle(true)}
            >
              ✏️
            </button>
          </h4>
        )}

        {isEditingDescription ? (
          <textarea
            value={tempDescription}
            onChange={(e) => setTempDescription(e.target.value)}
            onBlur={handleClickAwayDescription}
            autoFocus
            className="border border-gray-300 p-1 rounded w-full"
          />
        ) : (
          <p className="text-sm text-gray-500 flex items-center">
            {module.description}
            <button
              className="ml-2 text-blue-500"
              onClick={() => setIsEditingDescription(true)}
            >
              ✏️
            </button>
          </p>
        )}
      </div>
      <button
        className="ml-2 text-red-500-500"
        onClick={() => handleDelete(module.id)}
      >
        🗑️
      </button>
    </motion.div>
  );
}

export default function EditCourseCurriculum() {
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [selectedCategory, setSelectedCategory] = useState<Category>(
    categories[0],
  );
  const { setIsEditingComponent } = useTutorContext();

  const handleAddModule = () => {
    const newModule = {
      id: selectedCategory.modules.length + 1,
      title: t("tutor.organs.editcurriculum.module", {
        index: selectedCategory.modules.length + 1,
      }),
      description: "This is the description for the new module",
    };
    const updatedCategories = categories.map((category) =>
      category.id === selectedCategory.id
        ? { ...category, modules: [...category.modules, newModule] }
        : category,
    );
    setCategories(updatedCategories);
    setSelectedCategory({
      ...selectedCategory,
      modules: [...selectedCategory.modules, newModule],
    });
  };

  const handleDeleteModule = (moduleId: number) => {
    const updatedCategories = categories.map((category) =>
      category.id === selectedCategory.id
        ? {
            ...category,
            modules: category.modules.filter(
              (module) => module.id !== moduleId,
            ),
          }
        : category,
    );
    setCategories(updatedCategories);
    setSelectedCategory({
      ...selectedCategory,
      modules: selectedCategory.modules.filter(
        (module) => module.id !== moduleId,
      ),
    });
  };

  const handleTitleChange = (moduleId: number, newTitle: string) => {
    const updatedCategories = categories.map((category) =>
      category.id === selectedCategory.id
        ? {
            ...category,
            modules: category.modules.map((module) =>
              module.id === moduleId ? { ...module, title: newTitle } : module,
            ),
          }
        : category,
    );
    setCategories(updatedCategories);
    setSelectedCategory({
      ...selectedCategory,
      modules: selectedCategory.modules.map((module) =>
        module.id === moduleId ? { ...module, title: newTitle } : module,
      ),
    });
  };

  const handleDescriptionChange = (
    moduleId: number,
    newDescription: string,
  ) => {
    const updatedCategories = categories.map((category) =>
      category.id === selectedCategory.id
        ? {
            ...category,
            modules: category.modules.map((module) =>
              module.id === moduleId
                ? { ...module, description: newDescription }
                : module,
            ),
          }
        : category,
    );
    setCategories(updatedCategories);
    setSelectedCategory({
      ...selectedCategory,
      modules: selectedCategory.modules.map((module) =>
        module.id === moduleId
          ? { ...module, description: newDescription }
          : module,
      ),
    });
  };

  // Function to move a module's position
  const moveModule = (dragIndex: number, hoverIndex: number) => {
    if (hoverIndex < 0 || hoverIndex >= selectedCategory.modules.length) return;

    const updatedModules = [...selectedCategory.modules];
    const [draggedModule] = updatedModules.splice(dragIndex, 1);
    updatedModules.splice(hoverIndex, 0, draggedModule);

    const updatedCategories = categories.map((category) =>
      category.id === selectedCategory.id
        ? { ...category, modules: updatedModules }
        : category,
    );
    setCategories(updatedCategories);
    setSelectedCategory({
      ...selectedCategory,
      modules: updatedModules,
    });
  };

  return (
    <>
      <h2 className="text-lg font-semibold mb-2">
        {t("tutor.organs.editcurriculum.categories")}
      </h2>
      <div className="flex space-x-2 mb-4">
        {categories.map((category) => (
          <button
            key={category.id}
            className={`px-3 py-1 rounded ${
              selectedCategory.id === category.id
                ? "bg-blue-600 text-white"
                : "bg-slate-300 text-dark"
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            {category.name}
          </button>
        ))}
      </div>
      <div className="bg-white p-4 rounded shadow">
        <h3 className="text-lg font-semibold mb-2">
          {t("tutors.editcurriculum.category_name")} {selectedCategory.name}
        </h3>
        {selectedCategory.modules.map((module, index) => (
          <ModuleItem
            key={module.id}
            module={module}
            index={index}
            handleDelete={handleDeleteModule}
            handleTitleChange={handleTitleChange}
            handleDescriptionChange={handleDescriptionChange}
            moveModule={moveModule}
          />
        ))}
        <div className="flex items-center gap-2">
          <button
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={handleAddModule}
          >
            {t("tutors.editcurriculum.add_module")}
          </button>
          {selectedCategory.modules.length > 0 && (
            <button
              className="mt-4 px-4 py-2 bg-green text-white rounded hover:bg-green-500/50 flex items-center gap-2"
              onClick={() => setIsEditingComponent(false)}
            >
              <FaSave className="inline-block" />{" "}
              {t("tutors.editcurriculum.save_changes")}
            </button>
          )}
        </div>
      </div>
    </>
  );
}
