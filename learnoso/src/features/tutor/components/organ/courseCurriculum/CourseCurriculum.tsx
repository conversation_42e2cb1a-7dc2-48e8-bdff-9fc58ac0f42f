import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";

function CourseCurriculum() {
  const { t } = useTranslation();
  return (
    <div>
      <motion.div
        className="mb-6 flex items-center justify-between"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.2, delay: 0.2 }}
      >
        <h2 className="text-xl font-semibold">
          {t("tutors.coursecurriculum.curriculum")}
        </h2>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2, delay: 0.2 }}
      >
        {[
          t("tutors.coursecurriculum.beginner"),
          t("tutors.coursecurriculum.intermediate"),
          t("tutors.coursecurriculum.advance"),
        ].map((level, index) => (
          <motion.div
            key={index}
            className="bg-white rounded-lg shadow-md p-6"
            whileHover={{ scale: 1.02 }}
          >
            <h3 className="text-lg font-semibold mb-4">{level}</h3>
            <ul className="space-y-2">
              {level === t("tutors.coursecurriculum.beginner")
                ? [
                    t("tutors.coursecurriculum.introduction_to_physics"),
                    t("tutors.coursecurriculum.mechanics"),
                    t("tutors.coursecurriculum.thermodynamics"),
                    t("tutors.coursecurriculum.wave"),
                    t("tutors.coursecurriculum.electricity_and_magnetism"),
                  ].map((topic, i) => (
                    <li key={i} className="flex items-center">
                      <svg
                        className="w-5 h-5 mr-2 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {topic}
                    </li>
                  ))
                : [
                    t("tutors.coursecurriculum.advanced_mechanics"),
                    t("tutors.coursecurriculum.electromagnetic"),
                    t("tutors.coursecurriculum.optics_physics"),
                    t("tutors.coursecurriculum.thermodynamics_statistical"),
                    t("tutors.coursecurriculum.nuclear_particle"),
                  ].map((topic, i) => (
                    <li key={i} className="flex items-center">
                      {i + 1}. {topic}
                    </li>
                  ))}
            </ul>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}

export default CourseCurriculum;
