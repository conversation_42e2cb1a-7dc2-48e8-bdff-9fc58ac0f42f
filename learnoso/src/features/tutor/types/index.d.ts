export type LessonStatus = "Pending" | "Completed" | "Cancelled";
export type Lesson = {
  sn: string;
  student: string;
  course: string;
  hours: number;
  date: string;
  amount: number;
  status: LessonStatus;
};
export type DashboardCardType = {
  icon?: any;
  title: string;
  value: any;
  change: number;
  changePeriod: any;
  isCta?: boolean;
};
export interface Course {
  id: number;
  name: string;
  description?: string;
  rating?: number;
  reviews?: number;
  resources: ?number;
  color?: string;
}
export interface CourseDetail {
  title: string;
  description: string;
  shortDescription: string;
  language: string;
  skillLevel: string;
  lastUpdated: string;
  currency: string;
  pricePerHour: number;
  rating: number;
  students: number;
  totalHours: number;
}

export interface Concept {
  id: number;
  name: string;
}

export interface Tool {
  id: number;
  name: string;
}

export interface Resource {
  id: number;
  title: string;
  type: string;
  filename: string;
  link: string;
}

export interface Question {
  id: number;
  content: string;
}
