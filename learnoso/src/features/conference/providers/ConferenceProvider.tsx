import React, { createContext, useState } from "react";
import { ConferenceValue } from "../@types";
// import axios from "axios"
// import {useNavigate } from "react-router-dom";
const initialConferenceValue: ConferenceValue = {
  pauseVideo: () => {},
  resumeVideo: () => {},
  timer: 0,
  totalNumberOfTimesPaused: 0,
  totalTime: 0,
};
export const ConferenceContext = createContext<ConferenceValue>(
  initialConferenceValue,
);

export function ConferenceProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // pause or resume a video
  // timer,
  // numberOftimesPaused,
  // totalTime

  return (
    <ConferenceContext.Provider value={initialConferenceValue}>
      {children}
    </ConferenceContext.Provider>
  );
}
