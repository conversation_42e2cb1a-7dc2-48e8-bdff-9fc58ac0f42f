// src/App.tsx
import { useAuth } from "@/hooks";
import ThemeProvider from "@/providers/ThemeProvider";
import React, { useEffect, useState } from "react";
import VideoConference from "../components/VideoConference";
import { ConferenceProvider } from "../providers/ConferenceProvider";

export default function LiveConference() {
  const [inMeeting, setInMeeting] = useState(false);
  const [username, setUsername] = useState("");
  // check for

  // request for a token here

  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      setInMeeting(true);
    }
  }, []);

  // Simple join screen when not in meeting
  const JoinScreen = () => (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="bg-gray-800 p-8 rounded-lg shadow-lg w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6 text-white text-center">
          Learnoso Video Conference
        </h1>

        <div className="mb-4">
          <label htmlFor="username" className="block text-gray-500-300 mb-2">
            Your Name
          </label>
          <input
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            type="text"
            id="username"
            className="w-full bg-gray-700 text-white rounded px-3 py-2 border border-gray-600 focus:outline-none focus:border-blue-500"
            placeholder="Enter your name"
          />
        </div>

        <div className="mb-6">
          <label htmlFor="meeting-id" className="block text-gray-500-300 mb-2">
            Meeting ID
          </label>
          <input
            type="text"
            id="meeting-id"
            className="w-full bg-gray-700 text-white rounded px-3 py-2 border border-gray-600 focus:outline-none focus:border-blue-500"
            placeholder="Enter meeting ID"
            defaultValue={import.meta.env.VITE_CHANNEL}
          />
        </div>

        <div className="flex space-x-4">
          <button
            disabled={!username}
            onClick={() => {
              sessionStorage.setItem("name", username);
              setInMeeting(true);
            }}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded transition-colors"
          >
            Join Meeting
          </button>
          <button
            disabled={!username}
            onClick={() => {
              sessionStorage.setItem("name", username);
              setInMeeting(true);
            }}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded transition-colors"
          >
            Create Meeting
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <ConferenceProvider>
      <ThemeProvider>
        {inMeeting ? <VideoConference /> : <JoinScreen />}
      </ThemeProvider>
    </ConferenceProvider>
  );
}
