// eslint-disable-next-line @typescript-eslint/no-unused-vars
// @ts-nocheck
import {
  createClient,
  createMicrophoneAndCameraTracks,
  createScreenVideoTrack,
  type IAgoraRTCClient,
  type IAgoraRTCRemoteUser,
  type ILocalAudioTrack,
  type ILocalVideoTrack,
} from "agora-rtc-sdk-ng";
import AgoraRTM from "agora-rtm-sdk";
import { useEffect, useRef, useState } from "react";

import MeetingPrompt from "./MeetingPrompt";

import { ImeetingMetadata } from "@/app/services/lesson/interface";
import { useUpdateMeetingMetadataMutation } from "@/app/services/lesson/lesson.service";
import { useAuth } from "@/hooks";
import useQuery from "@/hooks/useQuery";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { useNavigate } from "react-router-dom";
import { useKeyboardShortcuts } from "../hooks/useKeyBoardShortcuts";
import ChatPanel, { type ChatMessage } from "./ChatPanel";
import ControlBar from "./ControlBar";
import TopBar from "./TopBar";
import VideoGrid from "./VideoGrid";
// import {lessonId} from '@/features/student/components/molecules/UpcomingSessionCard'

// Load credentials from Vite env
const appId = import.meta.env.VITE_AGORA_APP_ID as string;

export type ViewMode = "gallery" | "speaker";
export function encodeHex(json: string): string {
  const bytes = new TextEncoder().encode(json);
  return Array.from(bytes)
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
}

export function decodeHex(hex: string): string {
  const bytes = hex.match(/.{1,2}/g)?.map((h) => parseInt(h, 16)) || [];
  return new TextDecoder().decode(new Uint8Array(bytes));
}

export default function VideoConference() {
  // Initialize Agora client once
  const { user } = useAuth();
  const query = useQuery();
  const navigate = useNavigate();
  const title = query.get("title");
  const token = window.localStorage.getItem("learnoso-agora-token");
  const channel = window.localStorage.getItem("learnoso-agora-channel");
  // get temporal token here....
  const client = useRef<IAgoraRTCClient>(
    createClient({ mode: "rtc", codec: "vp8" })
  ).current;
  const { user: authenticatedUser } = useAuth();
  // let channelName = window.sessionStorage.getItem("learnoso-agora-channel");
  // let agoraToken = window.sessionStorage.getItem("learnoso-agora-token");

  // RTM client and channel
  const [rtmClient, setRtmClient] = useState<any>(null);
  const [rtmChannel, setRtmChannel] = useState<any>(null);

  // Chat state
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState("");
  const [unreadCount, setUnreadCount] = useState(0);

  // State for tracks & controls
  const [audioTrack, setAudioTrack] = useState<ILocalAudioTrack | null>(null);
  const [videoTrack, setVideoTrack] = useState<ILocalVideoTrack | null>(null);
  const [screenTrack, setScreenTrack] = useState<ILocalVideoTrack | null>(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isAudioMuted, setIsAudioMuted] = useState(false);
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [isPauseIncomingAudio, setIsPauseIncomingAudio] = useState(false);
  const [isPauseIncomingVideo, setIsPauseIncomingVideo] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>("gallery");
  const [remoteUsers, setRemoteUsers] = useState<IAgoraRTCRemoteUser[]>([]);
  const [activeUser, setActiveUser] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [isSpeakingWhileMuted, setIsSpeakingWhileMuted] = useState(false);
  const [handRaised, setHandRaised] = useState(false);
  const [meetingStartTime, setMeetingStartTime] = useState<Date | null>(null);
  const lessonId = localStorage.getItem("learnoso-lesson-id");

  console.log("lessons number 2", lessonId);
  // State for pinned user
  const [pinnedUser, setPinnedUser] = useState<string | null>(null);

  // Meeting state
  const [isMeetingStarted, setIsMeetingStarted] = useState(false);

  // Pause-related state
  const [elapsedTime, setElapsedTime] = useState(0); // Active meeting time
  const [totalTime, setTotalTime] = useState(0); // Total time including pauses
  const [isPaused, setIsPaused] = useState(false);
  const [pauseCount, setPauseCount] = useState(0);

  const pauseIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const totalTimeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const meetingTimeIntervalRef = useRef<NodeJS.Timeout | null>(null);
  //leavemeeting mutation
  const [updateMeetingMetadata, { meetingMetadata, isLoading }] =
    useUpdateMeetingMetadataMutation();

  const formatTime = (seconds: number) => {
    const hrs = Math.floor(seconds / 3600)
      .toString()
      .padStart(2, "0");
    const mins = Math.floor((seconds % 3600) / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${hrs}:${mins}:${secs}`;
  };

  const pauseVideo = () => {
    if (!isPaused) {
      // Stop the meeting time interval
      clearInterval(meetingTimeIntervalRef.current!);
      meetingTimeIntervalRef.current = null;

      setPauseCount((prev) => prev + 1);
      setIsPaused(true);
    }
  };

  const resumeVideo = () => {
    if (isPaused) {
      // Resume the meeting time interval
      meetingTimeIntervalRef.current = setInterval(() => {
        setElapsedTime((prev) => prev + 1);
      }, 1000);

      setIsPaused(false);
    }
  };

  // Start meeting function
  const startMeeting = () => {
    if (!isMeetingStarted) {
      setIsMeetingStarted(true);
      setMeetingStartTime(new Date());
      setIsPaused(false);

      // Start total time counter (never stops)
      totalTimeIntervalRef.current = setInterval(() => {
        setTotalTime((prev) => prev + 1);
      }, 1000);

      // Start meeting time counter (can be paused)
      meetingTimeIntervalRef.current = setInterval(() => {
        setElapsedTime((prev) => prev + 1);
      }, 1000);
    }
  };

  // Check if we should start the meeting
  useEffect(() => {
    const totalParticipants = remoteUsers.length + 1; // +1 for local user
    if (totalParticipants >= 2 && !isMeetingStarted) {
      startMeeting();
    }
  }, [remoteUsers.length, isMeetingStarted]);

  // Send message function
  const sendMessage = async () => {
    if (!inputMessage.trim() || !rtmChannel) return;

    try {
      // Create message object
      const messageData = {
        text: inputMessage.trim(),
        timestamp: Date.now(),
      };

      // Send through RTM
      await rtmChannel.sendMessage({ text: JSON.stringify(messageData) });

      // Add to local messages (optimistic update)
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          sender: client.uid?.toString() || "local",
          text: messageData.text,
          timestamp: new Date(),
          isMine: true,
        },
      ]);

      // Clear input
      setInputMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
      // Add system error message
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now().toString(),
          sender: "system",
          text: "Failed to send message. Please try again.",
          timestamp: new Date(),
          isMine: true,
          isSystem: true,
        },
      ]);
    }
  };

  // Toggle functions
  const toggleAudio = async () => {
    if (!audioTrack) return;
    if (isAudioMuted) {
      await audioTrack.setEnabled(true);
    } else {
      await audioTrack.setEnabled(false);
    }
    setIsAudioMuted(!isAudioMuted);
  };

  const toggleVideo = async () => {
    if (!videoTrack) return;
    if (isVideoMuted) {
      await videoTrack.setEnabled(true);
    } else {
      await videoTrack.setEnabled(false);
    }
    setIsVideoMuted(!isVideoMuted);
  };

  const toggleScreenShare = async () => {
    if (!isScreenSharing) {
      try {
        // Unpublish camera track if it exists
        if (videoTrack) {
          await client.unpublish(videoTrack);
          videoTrack.close();
        }

        // Create screen track
        const screen = await createScreenVideoTrack();
        setScreenTrack(screen);
        await client.publish(screen);
        setIsScreenSharing(true);
      } catch (error) {
        console.error("Screen sharing failed:", error);
        // Recover camera if screen sharing fails
        const [, camera] = await createMicrophoneAndCameraTracks();
        setVideoTrack(camera);
        await client.publish(camera);
      }
    } else {
      // Stop screen sharing
      if (screenTrack) {
        await client.unpublish(screenTrack);
        screenTrack.close();
        setScreenTrack(null);
      }

      // Restore camera
      const [, camera] = await createMicrophoneAndCameraTracks();
      setVideoTrack(camera);
      await client.publish(camera);
      setIsScreenSharing(false);
    }
  };

  const toggleIncomingAudio = () => {
    remoteUsers.forEach((user) => {
      if (user.audioTrack) {
        user.audioTrack.setEnabled(!isPauseIncomingAudio);
      }
    });
    setIsPauseIncomingAudio(!isPauseIncomingAudio);
  };

  const toggleIncomingVideo = () => {
    setIsPauseIncomingVideo(!isPauseIncomingVideo);
    if (isPauseIncomingVideo) {
      resumeVideo();
    } else {
      pauseVideo();
    }
  };

  const toggleViewMode = () => {
    setViewMode((prevMode) => (prevMode === "gallery" ? "speaker" : "gallery"));
    // When switching to gallery view, unpin any pinned user
    if (viewMode === "speaker") {
      setPinnedUser(null);
    }
  };

  const toggleChat = () => {
    setShowChat(!showChat);
    if (!showChat) {
      setUnreadCount(0); // Reset unread count when opening chat
    }
    if (showParticipants) setShowParticipants(false);
  };

  const toggleParticipants = () => {
    setShowParticipants(!showParticipants);
    if (showChat) setShowChat(false);
  };

  const toggleHandRaise = () => {
    setHandRaised(!handRaised);
    // Here you would also send a signal to other participants
    // You could use RTM to broadcast hand raise status
    if (rtmChannel) {
      try {
        rtmChannel.sendMessage({
          text: JSON.stringify({
            type: "handRaise",
            raised: !handRaised,
            timestamp: Date.now(),
          }),
        });
      } catch (error) {
        console.error("Failed to send hand raise signal:", error);
      }
    }
  };

  // New function to pin/unpin a user
  const togglePinUser = (uid: string) => {
    setPinnedUser((current) => (current === uid ? null : uid));
    // When pinning a user, automatically switch to speaker view
    if (viewMode === "gallery") {
      setViewMode("speaker");
    }
  };

  const leaveMeeting = async () => {
    const meetingStats: ImeetingMetadata = {
      meeting: {
        title: title,
        startTime: meetingStartTime?.toLocaleString(),
        endTime: new Date().toLocaleString(),
        status: isMeetingStarted ? "Completed" : "Not Started",
      },
      host: {
        id: user?.id,
        name: user?.first_name,
        email: user?.email,
      },
      duration: {
        total: formatTime(totalTime),
        active: formatTime(elapsedTime),
        paused: formatTime(totalTime - elapsedTime),
      },
      participants: {
        total: remoteUsers.length + 1,
        list: [
          {
            id: user?.id,
            name: user?.first_name,
            role: "Host",
          },
          ...remoteUsers.map((user) => ({
            id: user.uid.toString(),
            name: `Participant ${user.uid.toString().slice(5)}`,
            role: "Participant",
          })),
        ],
      },
      activity: {
        pauses: {
          count: pauseCount,
          totalDuration: formatTime(totalTime - elapsedTime),
        },
        screenShare: isScreenSharing ? "Yes" : "No",
        videoMuted: isVideoMuted ? "Yes" : "No",
        audioMuted: isAudioMuted ? "Yes" : "No",
      },
    };

    // Clean up resources
    if (audioTrack) audioTrack.close();
    if (videoTrack) videoTrack.close();
    if (screenTrack) screenTrack.close();

    if (rtmChannel) await rtmChannel.leave();
    if (rtmClient) await rtmClient.logout();

    await client.leave();
    setIsConnected(false);

    try {
      await RequestInterceptor.handleRequest(
        () => updateMeetingMetadata({ lessonId, meetingStats }).unwrap(),
        { onSuccess: () => {} }
      );
      console.log("Lesson updated successfully!");
      navigate(`/${user?.roles[0]}`, { replace: true });
    } catch (error) {
      console.error("Failed to update meeting metadata:", error);
      if (user?.roles.includes("tutor")) {
        navigate("/dashboard/tutor", { replace: true });
      } else {
        navigate("/dashboard/student", { replace: true });
      }
    }
  };

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    "Alt+A": toggleAudio,
    "Alt+V": toggleVideo,
    "Alt+S": toggleScreenShare,
    "Alt+G": toggleViewMode,
    "Alt+H": toggleHandRaise,
    "Alt+P": toggleParticipants,
    "Alt+C": toggleChat,
    "Alt+L": leaveMeeting,
    "Alt+Enter": sendMessage, // Add shortcut for sending messages
  });

  // Initialize connection and join channel
  useEffect(() => {
    const setupClient = async () => {
      try {
        // Add event listeners
        client.on("user-published", async (user, mediaType) => {
          await client.subscribe(user, mediaType);

          // Update remote users list
          setRemoteUsers((prev) => {
            if (prev.some((u) => u.uid === user.uid)) {
              return prev.map((u) => (u.uid === user.uid ? user : u));
            }
            return [...prev, user];
          });

          // Play tracks
          if (mediaType === "audio" && !isPauseIncomingAudio) {
            user.audioTrack?.play();
          }
        });

        client.on("user-unpublished", (user, mediaType) => {
          // Handle user unpublishing
          if (mediaType === "audio") {
            user.audioTrack?.stop();
          }
          if (mediaType === "video") {
            user.videoTrack?.stop();
          }
        });

        client.on("user-left", (user) => {
          // If the pinned user leaves, unpin them
          if (pinnedUser === user.uid.toString()) {
            setPinnedUser(null);
          }
          setRemoteUsers((prev) => prev.filter((u) => u.uid !== user.uid));
        });

        client.on("volume-indicator", (volumes) => {
          // Detect active speaker
          let highestVolume = 0;
          let activeUid = null;

          volumes.forEach((volume) => {
            if (volume.level > highestVolume && volume.level > 5) {
              highestVolume = volume.level;
              activeUid = volume.uid;
            }

            // Detect speaking while muted
            if (volume.uid === client.uid && isAudioMuted && volume.level > 5) {
              setIsSpeakingWhileMuted(true);
              // Reset after 2 seconds
              setTimeout(() => setIsSpeakingWhileMuted(false), 2000);
            }
          });

          if (activeUid) {
            setActiveUser(activeUid.toString());
          }
        });

        // Join RTC channel
        // The user id here is the JSON string of the user object
        // const userObj = JSON.stringify({
        //   first_name: authenticatedUser.first_name,
        //   // last_name: authenticatedUser.last_name,
        //   id: authenticatedUser.id,
        // });

        await client.join(appId, channel, token, null);
        console.log("Successfully joined RTC channel");

        // Enable volume detection
        client.enableAudioVolumeIndicator();

        // Create and publish tracks
        const [mic, camera] = await createMicrophoneAndCameraTracks();
        setAudioTrack(mic);
        setVideoTrack(camera);
        await client.publish([mic, camera]);

        setIsConnected(true);
        // Remove auto-start here since we'll start based on participant count

        // Initialize RTM client
        const rtmClientInstance = new AgoraRTM.RTM(
          appId,
          client.uid?.toString() || "user_" + Date.now()
        );
        setRtmClient(rtmClientInstance);

        // Login to RTM with the same UID as RTC for consistency
        await rtmClientInstance.login({
          token: token,
        });

        // Create and join RTM channel
        const rtmChannelInstance =
          rtmClientInstance.createStreamChannel(channel);
        await rtmChannelInstance.join();
        setRtmChannel(rtmChannelInstance);
        console.log("Successfully joined RTM channel");

        // Add system message
        setMessages([
          {
            id: Date.now().toString(),
            sender: "system",
            text: "You joined the chat",
            timestamp: new Date(),
            isMine: true,
            isSystem: true,
          },
        ]);

        // Set up RTM message listener
        rtmChannelInstance.on(
          "ChannelMessage",
          (message: any, senderId: string) => {
            try {
              const data = JSON.parse(message.text);

              // Handle different message types
              if (data.type === "handRaise") {
                // Handle hand raise signal
                // You could update a state to show who has their hand raised
              } else {
                // Regular chat message
                setMessages((prev) => [
                  ...prev,
                  {
                    id: Date.now().toString(),
                    sender: senderId,
                    text: data.text,
                    timestamp: new Date(data.timestamp),
                    isMine: senderId === client.uid?.toString(),
                  },
                ]);

                // Increment unread count if chat is not visible
                if (!showChat && senderId !== client.uid?.toString()) {
                  setUnreadCount((prev) => prev + 1);
                }
              }
            } catch (error) {
              console.error("Error processing message:", error);
            }
          }
        );

        // Handle member joined events
        rtmChannelInstance.on("MemberJoined", (memberId: string) => {
          setMessages((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              sender: "system",
              text: `User ${memberId} joined the chat`,
              timestamp: new Date(),
              isMine: false,
              isSystem: true,
            },
          ]);
        });

        // Handle member left events
        rtmChannelInstance.on("MemberLeft", (memberId: string) => {
          setMessages((prev) => [
            ...prev,
            {
              id: Date.now().toString(),
              sender: "system",
              text: `User ${memberId} left the chat`,
              timestamp: new Date(),
              isMine: false,
              isSystem: true,
            },
          ]);
        });

        // Handle RTM connection state changes
        rtmClientInstance.on(
          "ConnectionStateChanged",
          (newState: string, reason: string) => {
            console.log(
              "RTM connection state changed to:",
              newState,
              "reason:",
              reason
            );

            if (newState === "ABORTED") {
              // Add system message
              setMessages((prev) => [
                ...prev,
                {
                  id: Date.now().toString(),
                  sender: "system",
                  text: "Chat disconnected. Trying to reconnect...",
                  timestamp: new Date(),
                  isMine: false,
                  isSystem: true,
                },
              ]);

              // Try to reconnect after 3 seconds
              setTimeout(async () => {
                try {
                  await rtmClientInstance.login({
                    token: token,
                  });
                  const newChannel =
                    rtmClientInstance.createStreamChannel(channel);
                  await newChannel.join();
                  setRtmChannel(newChannel);

                  // Add system message
                  setMessages((prev) => [
                    ...prev,
                    {
                      id: Date.now().toString(),
                      sender: "system",
                      text: "Reconnected to chat",
                      timestamp: new Date(),
                      isMine: false,
                      isSystem: true,
                    },
                  ]);
                } catch (error) {
                  console.error("Failed to reconnect to RTM:", error);
                }
              }, 3000);
            }
          }
        );
      } catch (error) {
        console.error("Error setting up video conference:", error);
      }
    };

    setupClient();

    // Cleanup function
    return () => {
      client.removeAllListeners();
      clearInterval(meetingTimeIntervalRef.current!);
      clearInterval(totalTimeIntervalRef.current!);

      // RTM cleanup
      if (rtmChannel) {
        rtmChannel.leave();
      }
      if (rtmClient) {
        rtmClient.logout();
      }
    };
  }, [client]);

  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      <TopBar
        meetingTitle={`Video Conference: ${title}`}
        participantCount={remoteUsers.length + 1}
        startTime={meetingStartTime?.toISOString() || ""}
        formattedTime={formatTime(elapsedTime)}
        totalMeetingTime={formatTime(totalTime)}
        pausedSeconds={totalTime - elapsedTime}
        totalNumberOfTimesPaused={pauseCount}
      />

      <div className="flex-1 relative overflow-hidden">
        {/* Main video area */}
        <VideoGrid
          localUser={{
            uid: client.uid?.toString() || "local",
            videoTrack: isScreenSharing ? screenTrack : videoTrack,
            audioTrack,
            isVideoMuted,
            isAudioMuted,
            isScreenSharing,
            handRaised,
          }}
          remoteUsers={remoteUsers}
          viewMode={viewMode}
          activeUser={activeUser}
          isPauseIncomingVideo={isPauseIncomingVideo}
          pinnedUser={pinnedUser}
          onPinUser={togglePinUser}
        />

        {/* Show meeting invite prompt when alone */}
        {remoteUsers.length === 0 && <MeetingPrompt channel={channel} />}

        {/* Mute reminder overlay */}
        {isSpeakingWhileMuted && (
          <div className="absolute top-5 left-1/2 transform -translate-x-1/2 bg-red-500 px-4 py-2 rounded-lg animate-pulse">
            You are muted
          </div>
        )}
      </div>

      {/* Participants panel */}
      {showParticipants && (
        <div className="absolute right-0 top-16 bottom-16 w-64 bg-gray-800 border-l border-gray-700 p-4 overflow-auto z-10">
          <h3 className="text-lg font-semibold mb-4">
            Participants ({remoteUsers.length + 1})
          </h3>
          <ul>
            <li className="flex items-center justify-between mb-2 p-2 rounded hover:bg-gray-700">
              <div className="flex items-center">
                <span>
                  You {client.uid ? `(${authenticatedUser?.first_name})` : ""}{" "}
                  {handRaised && "✋"}
                </span>
                {pinnedUser === client.uid?.toString() && (
                  <span className="ml-2 text-xs bg-blue-500 px-1.5 py-0.5 rounded">
                    Pinned
                  </span>
                )}
              </div>
              <div className="flex items-center space-x-2">
                {isAudioMuted && <span>🔇</span>}
                {isVideoMuted && <span>🚫</span>}
                <button
                  onClick={() =>
                    togglePinUser(client.uid?.toString() || "local")
                  }
                  className="text-xs bg-gray-600 hover:bg-gray-500 px-1.5 py-0.5 rounded"
                >
                  {pinnedUser === client.uid?.toString() ? "Unpin" : "Pin"}
                </button>
              </div>
            </li>
            {remoteUsers.map((user) => (
              <li
                key={user.uid}
                className="flex items-center justify-between mb-2 p-2 rounded hover:bg-gray-700"
              >
                <div className="flex items-center">
                  <span>
                    User ({user.uid.toString().slice(5)}){" "}
                    {/* {(JSON.parse(user.uid) as IUser).last_name} */}
                  </span>
                  {pinnedUser === user.uid.toString() && (
                    <span className="ml-2 text-xs bg-blue-500 px-1.5 py-0.5 rounded">
                      Pinned
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {!user.hasAudio && <span>🔇</span>}
                  {!user.hasVideo && <span>🚫</span>}
                  <button
                    onClick={() => togglePinUser(user.uid.toString())}
                    className="text-xs bg-gray-600 hover:bg-gray-500 px-1.5 py-0.5 rounded"
                  >
                    {pinnedUser === user.uid.toString() ? "Unpin" : "Pin"}
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Chat panel */}
      {showChat && (
        <ChatPanel
          messages={messages}
          inputMessage={inputMessage}
          setInputMessage={setInputMessage}
          sendMessage={sendMessage}
          onClose={toggleChat}
        />
      )}

      <ControlBar
        isAudioMuted={isAudioMuted}
        isVideoMuted={isVideoMuted}
        isScreenSharing={isScreenSharing}
        isPauseIncomingAudio={isPauseIncomingAudio}
        isPauseIncomingVideo={isPauseIncomingVideo}
        isHandRaised={handRaised}
        showChat={showChat}
        showParticipants={showParticipants}
        isPinned={!!pinnedUser}
        unreadCount={unreadCount}
        toggleAudio={toggleAudio}
        toggleVideo={toggleVideo}
        toggleScreenShare={toggleScreenShare}
        toggleIncomingAudio={toggleIncomingAudio}
        toggleIncomingVideo={toggleIncomingVideo}
        toggleViewMode={toggleViewMode}
        toggleChat={toggleChat}
        toggleParticipants={toggleParticipants}
        toggleHandRaise={toggleHandRaise}
        leaveMeeting={leaveMeeting}
        meetingMetadata={meetingMetadata || undefined}
        // meetingResponse={responseData}
      />
    </div>
  );
}
