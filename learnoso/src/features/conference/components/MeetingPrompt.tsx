// src/components/MeetingPrompt.tsx
import React, { useState } from "react";

type MeetingPromptProps = {
  channel: string;
};

export default function MeetingPrompt({ channel }: MeetingPromptProps) {
  const [copied, setCopied] = useState(false);

  // Generate a meeting link (this is a placeholder)
  const meetingLink = `https://learnoso.com/conference/?channel=${channel}&meetingId=12345`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(meetingLink).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
    });
  };

  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 p-6 rounded-lg shadow-lg text-center max-w-md">
      <h2 className="text-xl font-semibold mb-4">Waiting for others to join</h2>
      <p className="text-gray-500-300 mb-6">
        You're the only one here. Invite others to join this meeting.
      </p>

      <div className="flex items-center mb-4">
        <input
          type="text"
          value={meetingLink}
          readOnly
          className="flex-1 bg-gray-700 rounded-l px-3 py-2 border border-gray-600 focus:outline-none focus:border-blue-500"
          aria-label="Meeting link"
        />
        <button
          onClick={copyToClipboard}
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-r transition-colors"
          aria-label="Copy meeting link"
        >
          {copied ? "Copied!" : "Copy"}
        </button>
      </div>

      <div className="flex justify-center space-x-4">
        <button
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center transition-colors"
          aria-label="Invite via email"
        >
          <span className="mr-2">📧</span>
          Email Invite
        </button>

        <button
          className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center transition-colors"
          aria-label="Share meeting link"
        >
          <span className="mr-2">🔗</span>
          Share Link
        </button>
      </div>
    </div>
  );
}
