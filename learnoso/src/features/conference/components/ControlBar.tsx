import React from "react";

type ControlBarProps = {
  isAudioMuted: boolean;
  isVideoMuted: boolean;
  isScreenSharing: boolean;
  isPauseIncomingAudio: boolean;
  isPauseIncomingVideo: boolean;
  isHandRaised: boolean;
  showChat: boolean;
  showParticipants: boolean;
  isPinned?: boolean;
  unreadCount?: number;
  toggleAudio: () => void;
  toggleVideo: () => void;
  toggleScreenShare: () => void;
  toggleIncomingAudio: () => void;
  toggleIncomingVideo: () => void;
  toggleViewMode: () => void;
  toggleChat: () => void;
  toggleParticipants: () => void;
  toggleHandRaise: () => void;
  leaveMeeting: () => void;
};

export default function ControlBar({
  isAudioMuted,
  isVideoMuted,
  isScreenSharing,
  isPauseIncomingAudio,
  isPauseIncomingVideo,
  isHandRaised,
  showChat,
  showParticipants,
  isPinned = false,
  unreadCount = 0,
  toggleAudio,
  toggleVideo,
  toggleScreenShare,
  toggleIncomingAudio,
  toggleIncomingVideo,
  toggleViewMode,
  toggleChat,
  toggleParticipants,
  toggleHandRaise,
  leaveMeeting,
}: ControlBarProps) {
  return (
    <div className="bg-gray-800 p-4 flex flex-wrap items-center justify-center gap-2 md:gap-4">
      {/* Audio control */}
      <button
        onClick={toggleAudio}
        className={`p-3 rounded-full ${
          isAudioMuted
            ? "bg-red-500 hover:bg-red-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={isAudioMuted ? "Unmute microphone" : "Mute microphone"}
        title={isAudioMuted ? "Unmute" : "Mute"}
      >
        {isAudioMuted ? (
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 3l18 18"
            />
          </svg>
        ) : (
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
            />
          </svg>
        )}
      </button>

      {/* Video control */}
      <button
        onClick={toggleVideo}
        className={`p-3 rounded-full ${
          isVideoMuted
            ? "bg-red-500 hover:bg-red-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={isVideoMuted ? "Turn on camera" : "Turn off camera"}
        title={isVideoMuted ? "Start Video" : "Stop Video"}
      >
        {isVideoMuted ? (
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 3l18 18"
            />
          </svg>
        ) : (
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
            />
          </svg>
        )}
      </button>

      {/* Screen share */}
      <button
        onClick={toggleScreenShare}
        className={`p-3 rounded-full ${
          isScreenSharing
            ? "bg-green-500 hover:bg-green-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={isScreenSharing ? "Stop screen sharing" : "Share screen"}
        title={isScreenSharing ? "Stop Sharing" : "Share Screen"}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
          />
        </svg>
      </button>

      {/* Pause incoming audio */}
      <button
        onClick={toggleIncomingAudio}
        className={`p-3 rounded-full ${
          isPauseIncomingAudio
            ? "bg-orange-500 hover:bg-orange-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={
          isPauseIncomingAudio ? "Resume others' audio" : "Pause others' audio"
        }
        title={isPauseIncomingAudio ? "Resume Audio" : "Pause Audio"}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {isPauseIncomingAudio ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15.536a5 5 0 01-1.536-5m0 0a5 5 0 017.072-5"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z"
            />
          )}
        </svg>
      </button>

      {/* Pause incoming video */}
      <button
        onClick={toggleIncomingVideo}
        className={`p-3 rounded-full ${
          isPauseIncomingVideo
            ? "bg-orange-500 hover:bg-orange-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={
          isPauseIncomingVideo ? "Resume others' video" : "Pause others' video"
        }
        title={isPauseIncomingVideo ? "Resume Video" : "Pause Video"}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {isPauseIncomingVideo ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          )}
        </svg>
      </button>

      {/* View mode / Pin toggle */}
      <button
        onClick={toggleViewMode}
        className={`p-3 rounded-full ${
          isPinned
            ? "bg-purple-500 hover:bg-purple-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={isPinned ? "Unpin view" : "Change view mode"}
        title={isPinned ? "Unpin" : "Change View"}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {isPinned ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            />
          )}
        </svg>
      </button>

      {/* Raise hand */}
      <button
        onClick={toggleHandRaise}
        className={`p-3 rounded-full ${
          isHandRaised
            ? "bg-yellow-500 hover:bg-yellow-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label={isHandRaised ? "Lower hand" : "Raise hand"}
        title={isHandRaised ? "Lower Hand" : "Raise Hand"}
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"
          />
        </svg>
      </button>

      {/* Participants */}
      <button
        onClick={toggleParticipants}
        className={`p-3 rounded-full ${
          showParticipants
            ? "bg-blue-500 hover:bg-blue-600"
            : "bg-gray-700 hover:bg-gray-600"
        }`}
        aria-label="Show participants"
        title="Participants"
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      </button>

      {/* Chat with unread count */}
      <button
        onClick={toggleChat}
        className={`p-3 rounded-full ${
          showChat
            ? "bg-blue-500 hover:bg-blue-600"
            : "bg-gray-700 hover:bg-gray-600"
        } relative`}
        aria-label="Show chat"
        title="Chat"
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
          />
        </svg>
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </button>

      {/* Leave meeting */}

      <button
        onClick={leaveMeeting}
        className="p-3 rounded-full bg-red-600 hover:bg-red-700"
        aria-label="Leave meeting"
        title="Leave"
      >
        <svg
          className="w-6 h-6 text-white"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
          />
        </svg>
      </button>
    </div>
  );
}
