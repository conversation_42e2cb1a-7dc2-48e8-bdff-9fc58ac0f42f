import React from "react";

import type {
  IAgoraRTCRemoteUser,
  ILocalAudioTrack,
  ILocalVideoTrack,
} from "agora-rtc-sdk-ng";
import { useEffect, useRef, useState } from "react";
import type { ViewMode } from "./VideoConference";

type LocalUserType = {
  uid: string;
  videoTrack: ILocalVideoTrack | null;
  audioTrack: ILocalAudioTrack | null;
  isVideoMuted: boolean;
  isAudioMuted: boolean;
  isScreenSharing: boolean;
  handRaised: boolean;
};

type VideoGridProps = {
  localUser: LocalUserType;
  remoteUsers: IAgoraRTCRemoteUser[];
  viewMode: ViewMode;
  activeUser: string | null;
  isPauseIncomingVideo: boolean;
  pinnedUser?: string | null;
  onPinUser?: (uid: string) => void;
};

export default function VideoGrid({
  localUser,
  remoteUsers,
  viewMode,
  activeUser,
  isPauseIncomingVideo,
  pinnedUser = null,
  onPinUser,
}: VideoGridProps) {
  const localVideoRef = useRef<HTMLDivElement>(null);
  const remoteRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const [hoveredUser, setHoveredUser] = useState<string | null>(null);

  // Helper to get all participants (local + remote)
  const allParticipants = [
    {
      uid: localUser.uid,
      isLocal: true,
      hasVideo: !localUser.isVideoMuted && localUser.videoTrack !== null,
      hasAudio: !localUser.isAudioMuted && localUser.audioTrack !== null,
      handRaised: localUser.handRaised,
      isScreenSharing: localUser.isScreenSharing,
    },
    ...remoteUsers.map((user) => ({
      uid: user.uid.toString(),
      isLocal: false,
      hasVideo: user.hasVideo,
      hasAudio: user.hasAudio,
      handRaised: false, // Would come from signaling in a real implementation
      isScreenSharing: false, // Would be detected from track info
    })),
  ];

  // Check if any user is sharing screen
  const screenSharingUser = allParticipants.find(
    (user) => user.isScreenSharing,
  );

  // Handle local video rendering
  useEffect(() => {
    if (localUser.videoTrack && localVideoRef.current) {
      localUser.videoTrack.play(localVideoRef.current);
      return () => {
        localUser.videoTrack?.stop();
      };
    }
  }, [localUser.videoTrack]);

  // Handle remote video rendering
  useEffect(() => {
    remoteUsers.forEach((user) => {
      if (user.videoTrack && !isPauseIncomingVideo) {
        const refElement = remoteRefs.current.get(user.uid.toString());
        if (refElement) {
          user.videoTrack.play(refElement);
        }
      }
    });

    return () => {
      remoteUsers.forEach((user) => {
        if (user.videoTrack) {
          user.videoTrack.stop();
        }
      });
    };
  }, [remoteUsers, isPauseIncomingVideo]);

  // Get array of participants based on view mode, screen sharing, and pinned user
  const getDisplayedParticipants = () => {
    // If someone is sharing screen, prioritize them
    if (screenSharingUser) {
      return [
        screenSharingUser,
        ...allParticipants.filter((p) => p.uid !== screenSharingUser.uid),
      ];
    }

    // If a user is pinned, prioritize them
    if (pinnedUser) {
      return [
        ...allParticipants.filter((p) => p.uid === pinnedUser),
        ...allParticipants.filter((p) => p.uid !== pinnedUser),
      ];
    }

    // Otherwise use speaker view logic if applicable
    if (viewMode === "speaker" && activeUser) {
      return [
        ...allParticipants.filter((p) => p.uid === activeUser),
        ...allParticipants.filter((p) => p.uid !== activeUser),
      ];
    }

    return allParticipants;
  };

  // Get ref for a participant
  const getRef = (uid: string, isLocal: boolean) => {
    if (isLocal) return localVideoRef;

    return {
      current: remoteRefs.current.get(uid) || null,
      set: (element: HTMLDivElement | null) => {
        if (element) {
          remoteRefs.current.set(uid, element);
        } else {
          remoteRefs.current.delete(uid);
        }
      },
    };
  };

  // Generate avatar color based on user ID
  const getAvatarColor = (uid: string) => {
    const colors = [
      "bg-blue-600",
      "bg-emerald-600",
      "bg-violet-600",
      "bg-amber-600",
      "bg-rose-600",
      "bg-cyan-600",
      "bg-indigo-600",
      "bg-teal-600",
    ];

    // Simple hash function to get consistent color for same user ID
    const hash = uid
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const participants = getDisplayedParticipants();

  // Determine if we should use screen sharing or pinned layout
  const useScreenSharingLayout = !!screenSharingUser;
  const usePinnedLayout = !useScreenSharingLayout && !!pinnedUser;

  // Get the appropriate grid layout class
  const getGridLayoutClass = () => {
    // Screen sharing layout takes precedence
    if (useScreenSharingLayout || usePinnedLayout) {
      return "grid-cols-1 md:grid-cols-4 md:grid-rows-3 gap-3";
    }

    // Otherwise use speaker or grid view based on viewMode
    if (viewMode === "speaker") {
      return "grid-cols-1 md:grid-cols-3 md:grid-rows-3 gap-3";
    }

    // Grid view based on participant count
    const count = participants.length;
    if (count <= 1) return "grid-cols-1 gap-3";
    if (count === 2) return "grid-cols-2 gap-3";
    if (count <= 4) return "grid-cols-2 gap-3";
    if (count <= 6) return "grid-cols-2 md:grid-cols-3 gap-3";
    return "grid-cols-3 gap-3";
  };

  // Handle pin button click
  const handlePinClick = (uid: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (onPinUser) {
      onPinUser(uid);
    }
  };

  return (
    <div
      className={`w-full h-full p-3 bg-gray-900 rounded-xl grid ${getGridLayoutClass()}`}
    >
      {participants.map((participant, index) => {
        const isActive = participant.uid === activeUser;
        const isScreenSharing = participant.isScreenSharing;
        const isPinned = participant.uid === pinnedUser;
        const isHovered = participant.uid === hoveredUser;
        const ref = getRef(participant.uid, participant.isLocal);
        const avatarColor = getAvatarColor(participant.uid);

        // Determine if this participant should be featured (screen sharing, pinned, or active speaker)
        const isFeatured =
          (useScreenSharingLayout && index === 0) ||
          (usePinnedLayout && index === 0) ||
          (!useScreenSharingLayout &&
            !usePinnedLayout &&
            viewMode === "speaker" &&
            index === 0);

        return (
          <div
            key={participant.uid}
            className={`
              relative overflow-hidden rounded-xl transition-all duration-300
              ${isFeatured ? "md:col-span-3 md:row-span-3" : ""}
              ${
                isScreenSharing
                  ? "ring-4 ring-green-500 shadow-xl"
                  : isPinned
                    ? "ring-4 ring-purple-500 shadow-xl"
                    : isActive
                      ? "ring-4 ring-blue-500 shadow-lg"
                      : isHovered
                        ? "ring-2 ring-blue-400"
                        : "ring-1 ring-gray-700"
              }
              ${isHovered ? "transform scale-[1.02]" : ""}
              ${isFeatured ? "z-10" : "z-0"}
            `}
            onMouseEnter={() => setHoveredUser(participant.uid)}
            onMouseLeave={() => setHoveredUser(null)}
            ref={(element) => {
              if (participant.isLocal) {
                // ts-ignore
                // localVideoRef.current = element;
              } else if (element) {
                remoteRefs.current.set(participant.uid, element);
              }
            }}
          >
            {/* Video container with gradient overlay - lighter for screen sharing */}
            <div
              className={`
              absolute inset-0 
              ${
                isScreenSharing
                  ? "bg-gradient-to-b from-black/10 via-transparent to-black/40"
                  : "bg-gradient-to-b from-black/30 via-transparent to-black/70"
              }
            `}
            ></div>

            {/* Placeholder when video is off */}
            {!participant.hasVideo && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-800">
                <div
                  className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  text-2xl font-bold text-white shadow-lg
                  ${avatarColor}
                `}
                >
                  {participant.isLocal
                    ? "Y"
                    : participant.uid.charAt(0).toUpperCase()}
                </div>
              </div>
            )}

            {/* Screen sharing indicator banner */}
            {isScreenSharing && (
              <div className="absolute top-0 left-0 right-0 bg-green-600 text-white text-xs font-medium py-1 px-3 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3.5 w-3.5 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                    clipRule="evenodd"
                  />
                </svg>
                Sharing Screen
              </div>
            )}

            {/* Pinned indicator banner */}
            {isPinned && !isScreenSharing && (
              <div className="absolute top-0 left-0 right-0 bg-purple-600 text-white text-xs font-medium py-1 px-3 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3.5 w-3.5 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 5a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2H7a2 2 0 01-2-2V5z" />
                  <path d="M10 10a2 2 0 00-2 2v5a2 2 0 002 2h5a2 2 0 002-2v-5a2 2 0 00-2-2h-5z" />
                </svg>
                Pinned
              </div>
            )}

            {/* Status indicators */}
            <div className="absolute top-2 right-2 flex space-x-1.5">
              {!participant.hasAudio && (
                <div className="bg-red-500 p-1.5 rounded-full shadow-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}

              {participant.handRaised && (
                <div className="bg-yellow-500 p-1.5 rounded-full shadow-md">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Pin button (shows on hover) */}
            {onPinUser && isHovered && !isScreenSharing && (
              <div className="absolute top-2 left-2 z-20">
                <button
                  onClick={(e) => handlePinClick(participant.uid, e)}
                  className={`
                    p-1.5 rounded-full shadow-md transition-colors
                    ${
                      isPinned
                        ? "bg-purple-600 hover:bg-purple-700"
                        : "bg-gray-700 hover:bg-gray-600"
                    }
                  `}
                  aria-label={
                    isPinned ? "Unpin participant" : "Pin participant"
                  }
                  title={isPinned ? "Unpin" : "Pin"}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-3.5 w-3.5 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M5 5a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2H7a2 2 0 01-2-2V5z" />
                    <path d="M10 10a2 2 0 00-2 2v5a2 2 0 002 2h5a2 2 0 002-2v-5a2 2 0 00-2-2h-5z" />
                  </svg>
                </button>
              </div>
            )}

            {/* Name label with frosted glass effect */}
            <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
              <div
                className={`
                backdrop-blur-sm px-3 py-1.5 rounded-lg text-sm font-medium text-white shadow-md
                ${
                  isScreenSharing
                    ? "bg-green-900/40"
                    : isPinned
                      ? "bg-purple-900/40"
                      : "bg-black/40"
                }
              `}
              >
                {participant.isLocal
                  ? "You"
                  : `User ${participant.uid.substring(0, 4)}`}
                {isScreenSharing && " (Sharing)"}
                {isPinned && !isScreenSharing && " (Pinned)"}
              </div>

              {/* Active speaker indicator */}
              {isActive && !isScreenSharing && !isPinned && (
                <div className="bg-blue-500 bg-opacity-70 backdrop-blur-sm px-2 py-1 rounded-lg text-xs font-medium text-white shadow-md animate-pulse">
                  Speaking
                </div>
              )}
            </div>

            {/* Hover overlay with subtle animation */}
            <div
              className={`
                absolute inset-0 bg-gradient-to-t 
                ${
                  isScreenSharing || isPinned
                    ? "from-black/20 to-black/5"
                    : "from-black/40 to-black/10"
                }
                pointer-events-none transition-opacity duration-300
                ${isHovered ? "opacity-100" : "opacity-0"}
              `}
            />

            {/* Focus overlay */}
            {isFeatured && (
              <div
                className={`
                absolute inset-0 border-4 rounded-xl opacity-50 pointer-events-none
                ${
                  isScreenSharing
                    ? "border-green-500"
                    : isPinned
                      ? "border-purple-500"
                      : "border-blue-500"
                }
              `}
              ></div>
            )}
          </div>
        );
      })}
    </div>
  );
}
