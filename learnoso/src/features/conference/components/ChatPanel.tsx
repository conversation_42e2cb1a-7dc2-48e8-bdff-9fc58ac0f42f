// src/components/ChatPanel.tsx
import React, { useEffect, useRef } from "react";

export interface ChatMessage {
  id: string;
  text: string;
  sender: string;
  timestamp: Date;
  isMine: boolean;
  isSystem?: boolean;
}

interface ChatPanelProps {
  messages: ChatMessage[];
  inputMessage: string;
  setInputMessage: (message: string) => void;
  sendMessage: () => void;
  onClose: () => void;
}

export default function ChatPanel({
  messages,
  inputMessage,
  setInputMessage,
  sendMessage,
  onClose,
}: ChatPanelProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  return (
    <div className="absolute right-0 top-16 bottom-16 w-80 bg-gray-800 border-l border-gray-700 flex flex-col z-10">
      <div className="p-4 border-b border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-semibold">Chat</h3>
        <button
          onClick={onClose}
          className="text-gray-500-400 hover:text-white rounded-full p-1 hover:bg-gray-700"
          aria-label="Close chat"
        >
          <svg
            className="w-5 h-5"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6 18L18 6M6 6L18 18"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      <div className="flex-1 p-4 overflow-y-auto">
        {messages.length === 0 ? (
          <p className="text-gray-500-400 text-center italic mt-10">
            No messages yet
          </p>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`mb-4 ${
                message.isSystem
                  ? "text-center"
                  : message.isMine
                    ? "text-right"
                    : "text-left"
              }`}
            >
              {!message.isSystem && (
                <div className="text-xs text-gray-500-400 mb-1">
                  {message.isMine
                    ? "You"
                    : `User ${message.sender.substring(0, 4)}`}
                </div>
              )}

              <div
                className={`inline-block rounded-lg px-4 py-2 max-w-[80%] break-words ${
                  message.isSystem
                    ? "bg-gray-700 text-gray-500-300 text-xs"
                    : message.isMine
                      ? "bg-blue-600 text-white"
                      : "bg-gray-700 text-white"
                }`}
              >
                {message.text}
              </div>

              <div className="text-xs text-gray-500-400 mt-1">
                {message.timestamp.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t border-gray-700">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (inputMessage.trim()) {
              sendMessage();
            }
          }}
          className="flex items-center"
        >
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey && inputMessage.trim()) {
                e.preventDefault();
                sendMessage();
              }
            }}
            placeholder="Type a message..."
            className="flex-1 bg-gray-700 text-white rounded-l px-3 py-2 focus:outline-none focus:ring-1 focus:ring-blue-500"
            aria-label="Chat message input"
          />
          <button
            type="submit"
            disabled={!inputMessage.trim()}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:opacity-50 text-white rounded-r px-4 py-2"
          >
            <svg
              className="w-5 h-5"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 19L21 12L12 5V9.5L3 12L12 14.5V19Z"
                fill="currentColor"
              />
            </svg>
          </button>
        </form>
      </div>
    </div>
  );
}
