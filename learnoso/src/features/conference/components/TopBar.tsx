import { Clock, Users } from "lucide-react";
import React, { useState } from "react";

interface TopBarProps {
  meetingTitle: string;
  participantCount: number;
  startTime: string;
  formattedTime: string;
  totalMeetingTime: string;
  pausedSeconds: number;
  totalNumberOfTimesPaused: number;
  leaveMeeting: any;
}

interface MeetingMetadata {
  id?: number;
  lessons_id: number;
  duration?: string;
  started_at?: string;
  ended_at?: string;
  [key: string]: any;
}
export default function TopBar({
  meetingTitle,
  participantCount,
  startTime,
  formattedTime,
  totalMeetingTime,
  pausedSeconds,
  totalNumberOfTimesPaused,
}: TopBarProps) {
  return (
    <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
      <div className="flex items-center space-x-4">
        <h1 className="text-lg font-semibold">{meetingTitle}</h1>
      </div>
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2 text-sm text-gray-300">
          <Users className="w-4 h-4" />
          <span>{participantCount} participant(s)</span>
        </div>
        <div className="flex items-center space-x-2 text-sm text-gray-300">
          <Clock className="w-4 h-4" />
          <span>{totalMeetingTime}</span>
        </div>
      </div>
    </div>
  );
}
