// src/hooks/useKeyboardShortcuts.tsx
import { useEffect } from "react";

type ShortcutMap = {
  [key: string]: () => void;
};

export function useKeyboardShortcuts(shortcuts: ShortcutMap) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Create key identifier (e.g., "Alt+A")
      const pressedKey = [
        event.altKey ? "Alt" : "",
        event.ctrlKey ? "Ctrl" : "",
        event.shiftKey ? "Shift" : "",
        event.key.toUpperCase(),
      ]
        .filter(Boolean)
        .join("+");

      // Find matching shortcut if exists
      const matchingKey = Object.keys(shortcuts).find(
        (key) => key.toUpperCase() === pressedKey,
      );

      if (matchingKey) {
        event.preventDefault();
        shortcuts[matchingKey]();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [shortcuts]);
}
