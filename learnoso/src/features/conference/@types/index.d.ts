import { IAgoraRTCRemoteUser } from "agora-rtc-react";

export interface AgoraContextValue {
  start: boolean;
  setStart: React.Dispatch<React.SetStateAction<boolean>>;
  inCall: boolean;
  setInCall: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface VideoPlayerProps {
  user: IAgoraRTCRemoteUser | { uid: string | number };
  videoTrack: any;
}

export interface ConferenceValue {
  pauseVideo: () => void;
  resumeVideo: () => void;
  timer: number;
  totalTime: number;
  totalNumberOfTimesPaused: number;
}
