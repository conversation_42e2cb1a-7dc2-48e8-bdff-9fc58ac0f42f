import { useOnboardTutorDescriptionMutation } from "@/app/services/tutor/tutor.service";
import { useFetchLanguagesQuery } from "@/app/services/util/util.service";
import { Learno<PERSON>B<PERSON>on, Spinner } from "@/components/atoms";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { Language } from "@/types";
import React from "react";
import { useForm } from "react-hook-form";
import { useTutorOnboardContext } from "../../hooks";
import { OnboardTwoFormData } from "../../types";
import { useTranslation } from "react-i18next";

const OnboardTwo = () => {
  const { handleNext, handlePrev } = useTutorOnboardContext();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OnboardTwoFormData>();

  const { user } = useAuth();
  const [postTutorDescription, { isLoading: isPostingTutorDescriptionData }] =
    useOnboardTutorDescriptionMutation();
  const { data: languages, isLoading: isFetchingLanguages } =
    useFetchLanguagesQuery("");

  const onSubmit = async (data: OnboardTwoFormData) => {
    const form: ITutorDescription = {
      description: data.experience,
      motivation: data.motivation,
      primary_language_id: +data.language,
      languages: [+data.language],
      user_id: user!.id,
    };
    console.log(form);

    await RequestInterceptor.handleRequest(
      () => postTutorDescription(form).unwrap(),
      { onSuccess: handleNext },
    );
  };

  const { t } = useTranslation();

  return (
    <>
      <section className="text-dark p-4">
        <div className="flex min-h-screen items-center max-w-7xl mx-auto gap-6">
          <div className="flex-1 sm:flex hidden self-start mt-28">
            <img src="/assets/images/account1.png" alt="" />
          </div>
          <div className="flex-1 p-4 sm:p-16 shadow-md rounded-md border-dark/10 border">
            <form
              className="flex flex-col gap-6"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="space-y-4">
                <h1 className="text-2xl font-bold">
                  {t("tutoronboarding.onboardtwo.about")}{" "}
                  <span className="text-primary">{user?.first_name}</span>
                </h1>
                <div className="flex flex-col gap-3">
                  <label htmlFor="experience" className="space-y-1">
                    <h2>
                      {" "}
                      {t("tutoronboarding.onboardtwo.teaching_experience")}{" "}
                    </h2>
                    <p className="text-xs">
                      {t("tutoronboarding.onboardtwo.description")}.
                    </p>
                  </label>
                  <textarea
                    {...register("experience", {
                      required: {
                        value: true,
                        message: "This field is required",
                      },
                    })}
                    id="experience"
                    className="w-full h-24 outline-none border border-dark/40 p-2 rounded-md text-sm"
                  ></textarea>

                  {errors.experience && (
                    <p className="error">{errors.experience.message}</p>
                  )}
                </div>

                <div className="flex flex-col gap-3">
                  <label htmlFor="motivation" className="space-y-1">
                    <h2>{t("tutoronboarding.onboardtwo.motivation")}</h2>
                    <p className="text-xs">
                      {t("tutoronboarding.onboardtwo.second_section")}
                    </p>
                  </label>
                  <textarea
                    {...register("motivation", {
                      required: {
                        value: true,
                        message: "This field is required",
                      },
                    })}
                    id="motivation"
                    className="w-full h-24 outline-none border border-dark/40 p-2 rounded-md text-sm"
                  ></textarea>

                  {errors.motivation && (
                    <p className="error">{errors.motivation.message}</p>
                  )}
                </div>
              </div>

              <div className="flex flex-col gap-3">
                <label htmlFor="languages" className="space-y-1">
                  <h2>{t("tutoronboarding.onboardtwo.language")}Languages</h2>
                  <p className="text-xs">
                    {t("tutoronboarding.onboardtwo.select_atleast_a_language")}
                  </p>
                </label>
                <select
                  {...register("language", {
                    required: {
                      value: true,
                      message: "This field is required",
                    },
                  })}
                  id="language"
                  className="w-full py-3 outline-none border border-dark/40 rounded-md px-2"
                  defaultValue=""
                >
                  <option value="" disabled>
                    {t("tutoronboarding.onboardtwo.select_languages")}
                  </option>
                  {languages?.data?.map((language: Language) => (
                    <option key={language.id} value={language.id}>
                      {language.name}
                    </option>
                  ))}
                </select>

                {errors.language && (
                  <p className="error">{errors.language.message}</p>
                )}
              </div>

              <div className="flex gap-4 justify-between">
                <LearnosoButton
                  title={t("tutoronboarding.onboardtwo.prev")}
                  animated
                  width="w-fit"
                  action={handlePrev}
                />

                {isPostingTutorDescriptionData ? (
                  <Spinner />
                ) : (
                  <LearnosoButton
                    title={t("tutoronboarding.onboardtwo.next")}
                    animated
                    type="submit"
                    width="w-fit"
                  />
                )}
              </div>
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default OnboardTwo;
