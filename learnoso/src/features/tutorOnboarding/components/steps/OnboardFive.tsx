// OnboardFive.jsx
import { useOnboardTutorPriceAvailabilityMutation } from "@/app/services/tutor/tutor.service";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import React from "react";
import { useForm } from "react-hook-form";
import useTutorOnboardContext from "../../hooks/useTutorOnboardContext";
import { OnboardFiveFormData } from "../../types";
import { AvailabilitySection } from "./molecules/stepfive/AvailabilitySection";
import { FormContainer } from "./molecules/stepfive/FormContainer";
import { NavigationButtons } from "./molecules/stepfive/NavigationButtons";
import { PricingSection } from "./molecules/stepfive/PricingSection";
import { SideImage } from "./molecules/stepfive/SideImage";
import { useTranslation } from "react-i18next";

const OnboardFive = () => {
  const { handleNext, handlePrev } = useTutorOnboardContext();
  const {
    register,
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<OnboardFiveFormData>({
    defaultValues: {
      currency: "",
      price: 0,
      availability: false,
      timezone: "",
      timeslots: [
        {
          enable: false,
          day: "Monday",
          startTime: "09:00",
          endTime: "15:00",
        },
      ],
    },
  });

  const { user } = useAuth();
  const [postTutorAvailability, { isLoading }] =
    useOnboardTutorPriceAvailabilityMutation();
  const onSubmit = async (data: OnboardFiveFormData) => {
    const transformedData: ITutorPriceAvailability = {
      user_id: user!.id,
      currency: data.currency.toLocaleLowerCase(),
      price: data.price,
      availability: data.timeslots
        .filter((slot) => slot.enable)
        .map((slot) => ({
          time: data.timezone,
          availability: {
            from: slot.startTime,
            to: slot.endTime,
          },
          day_of_the_week: slot.day,
        })),
    };

    await RequestInterceptor.handleRequest(
      () => postTutorAvailability(transformedData).unwrap(),
      { onSuccess: handleNext },
      "Tutor Availability",
    );
  };
  const { t } = useTranslation();
  return (
    <section className="text-dark p-4">
      <div className="flex min-h-screen items-center max-w-7xl mx-auto gap-6">
        <SideImage />
        <FormContainer onSubmit={handleSubmit(onSubmit)}>
          <h1 className="text-2xl font-bold">
            {t("tutoronboarding.onboardfive.pricing_and_availability")}
          </h1>
          <PricingSection register={register} errors={errors} />
          <AvailabilitySection
            register={register}
            control={control}
            errors={errors}
            watch={watch}
          />
          <NavigationButtons handlePrev={handlePrev} isLoading={isLoading} />
        </FormContainer>
      </div>
    </section>
  );
};

export default OnboardFive;
