import { useOnboardTutorProfileAndVideoMutation } from "@/app/services/tutor/tutor.service";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner } from "@/components/atoms";
import { EmbedVideo } from "@/components/molecules";
import { useAuth, useDragAndDrop } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { videoUrlRegex } from "@/lib/util";
import { t } from "i18next";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useTutorOnboardContext } from "../../hooks";
import { OnboardFourFormData } from "../../types";

const OnboardFour = () => {
  const { handleNext, handlePrev } = useTutorOnboardContext();
  const { user } = useAuth();
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [
    postProfileVideoAndPicture,
    { isLoading: isPostingProfileVideoAndPicture },
  ] = useOnboardTutorProfileAndVideoMutation();
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<OnboardFourFormData>({
    defaultValues: {
      profilePhoto: [] as FileList[],
      profileVideo: "",
    },
  });

  const onSubmit = async (data: OnboardFourFormData) => {
    const { profilePhoto, profileVideo } = data;
    const profile: ITutorProfileAndVideo = {
      profile_picture: profilePhoto[0] as unknown as File,
      video_url: profileVideo,
      user_id: user!.id,
    };
    const formData = new FormData();
    formData.append("profile_picture", profile.profile_picture);
    formData.append("video_url", profile.video_url);
    formData.append("user_id", `${profile.user_id}`);
    await RequestInterceptor.handleRequest(
      () => postProfileVideoAndPicture(formData).unwrap(),
      { onSuccess: handleNext },
      "Profile Picture and Video",
    );
  };

  const onFileDrop = (files: FileList) => {
    const file = files[0];
    if (file) {
      setPhotoPreview(URL.createObjectURL(file));
    }
  };

  const {
    isDragging,
    handleDragEnter,
    handleDragLeave,
    handleDrop,
    handleDragOver,
  } = useDragAndDrop(onFileDrop);

  return (
    <>
      <section className="text-dark p-4">
        <div className="flex min-h-screen items-center max-w-7xl mx-auto gap-6">
          <div className="flex-1 sm:flex hidden self-start mt-28">
            <img src="/assets/images/cuate.png" alt="" className="w-full" />
          </div>
          <div className="flex-1 p-4 sm:p-16 shadow-md rounded-md border-dark/10 border">
            <form
              className="flex flex-col gap-6"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="flex flex-col gap-4">
                <h1 className="text-2xl font-bold">
                  {" "}
                  {t("tutoronboarding.onboardfour.your_profile")}
                </h1>
                <div className="flex flex-col gap-1">
                  <div className="space-y-1">
                    <h2 className="font-semibold">
                      {" "}
                      {t("tutoronboarding.onboardfour.profile_photo")}
                    </h2>
                    <p className="text-xs">
                      {t("tutoronboarding.onboardfour.choose_photo")}
                    </p>
                  </div>
                  <div className="flex gap-6 py-8">
                    <div className="h-52 min-w-52 bg-[#E9ECF5] text-xs w-full flex-1 flex items-center justify-center p-4 rounded-md">
                      {photoPreview ? (
                        <img
                          src={photoPreview}
                          alt="profile"
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        "Your photo will be previewed here"
                      )}
                    </div>
                    <div
                      className={`flex-1 min-w-52 border-4 border-dotted ${
                        isDragging ? "border-primary" : "border-primary/20"
                      } rounded-md`}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDragOver={handleDragOver}
                      onDrop={handleDrop}
                    >
                      <label
                        htmlFor="photo"
                        className="cursor-pointer flex flex-col gap-4 items-center justify-center h-full"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="25"
                          height="25"
                          viewBox="0 0 25 25"
                          fill="none"
                        >
                          <path
                            d="M2.68918 19.0647L2.66918 19.0847C2.39918 18.4947 2.22918 17.8247 2.15918 17.0847C2.22918 17.8147 2.41918 18.4747 2.68918 19.0647Z"
                            fill="#292D32"
                          />
                          <path
                            d="M9.10949 10.4347C10.4239 10.4347 11.4895 9.36912 11.4895 8.05468C11.4895 6.74024 10.4239 5.67468 9.10949 5.67468C7.79505 5.67468 6.72949 6.74024 6.72949 8.05468C6.72949 9.36912 7.79505 10.4347 9.10949 10.4347Z"
                            fill="#292D32"
                          />
                          <path
                            d="M16.2994 2.05469H7.91937C4.27937 2.05469 2.10938 4.22469 2.10938 7.86469V16.2447C2.10938 17.3347 2.29937 18.2847 2.66937 19.0847C3.52937 20.9847 5.36937 22.0547 7.91937 22.0547H16.2994C19.9394 22.0547 22.1094 19.8847 22.1094 16.2447V13.9547V7.86469C22.1094 4.22469 19.9394 2.05469 16.2994 2.05469ZM20.4794 12.5547C19.6994 11.8847 18.4394 11.8847 17.6594 12.5547L13.4994 16.1247C12.7194 16.7947 11.4594 16.7947 10.6794 16.1247L10.3394 15.8447C9.62937 15.2247 8.49938 15.1647 7.69938 15.7047L3.95937 18.2147C3.73937 17.6547 3.60938 17.0047 3.60938 16.2447V7.86469C3.60938 5.04469 5.09938 3.55469 7.91937 3.55469H16.2994C19.1194 3.55469 20.6094 5.04469 20.6094 7.86469V12.6647L20.4794 12.5547Z"
                            fill="#292D32"
                          />
                        </svg>
                        <div className="text-center">
                          <p className="text-lg">
                            {t("tutoronboarding.onboardfour.drag_drop")}
                          </p>
                          <p className="text-sm">
                            {t("tutoronboarding.onboardfour.or")}{" "}
                            <span className="text-primary font-bold ">
                              {t("tutoronboarding.onboardfour.upload")}
                            </span>{" "}
                            {t("tutoronboarding.onboardfour.from_system")}
                          </p>
                        </div>
                      </label>
                      <input
                        type="file"
                        className="hidden"
                        id="photo"
                        accept="image/*"
                        {...register("profilePhoto", {
                          required: {
                            value: true,
                            message: "Your profile image is required",
                          },
                          onChange: (e) => {
                            const file = e.target.files?.[0];
                            if (file)
                              setPhotoPreview(URL.createObjectURL(file));
                          },
                        })}
                      />
                    </div>
                  </div>
                  {errors.profilePhoto && (
                    <p className="error">
                      {errors.profilePhoto.message}
                      {". "}
                      <span className="text-primary cursor-pointer underline">
                        {t("tutoronboarding.onboardfour.learn_more")}
                      </span>
                    </p>
                  )}
                </div>

                <div className="flex flex-col gap-4">
                  <div className="space-y-1">
                    <h2 className="font-semibold">
                      {t("tutoronboarding.onboardfour.video_url")}{" "}
                    </h2>
                    <p className="text-xs">
                      {t("tutoronboarding.onboardfour.short_video")}
                    </p>
                  </div>
                  <input
                    type="text"
                    placeholder={t(
                      "tutoronboarding.onboardfour.video_placeholder",
                    )}
                    className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                    {...register("profileVideo", {
                      required: {
                        value: true,
                        message: "The video is required",
                      },
                      pattern: {
                        value: videoUrlRegex,
                        message: "Invalid Video link",
                      },
                    })}
                  />
                  {errors.profileVideo && (
                    <p className="error">
                      {errors.profileVideo.message}
                      {". "}
                      <span className="text-primary cursor-pointer underline">
                        {t("tutoronboarding.onboardfour.learn_more")}
                      </span>
                    </p>
                  )}
                  <div className="flex gap-6">
                    <div className="h-52 min-w-52 bg-[#E9ECF5] text-xs w-full flex-1 flex items-center justify-center ">
                      {!watch("profileVideo") ? (
                        <p className="text-gray-500">
                          {t("tutoronboarding.onboardfour.your_video")}
                        </p>
                      ) : (
                        <EmbedVideo videoUrl={watch("profileVideo")} />
                      )}
                    </div>
                    {/* <div className="flex-1 min-w-52"></div> */}
                  </div>
                </div>
              </div>

              <div className="flex gap-4 justify-between">
                <LearnosoButton
                  title={t("tutoronboarding.onboardfour.prev")}
                  animated
                  width="w-fit"
                  action={handlePrev}
                />

                {isPostingProfileVideoAndPicture ? (
                  <Spinner />
                ) : (
                  <LearnosoButton
                    title={t("tutoronboarding.onboardfour.next")}
                    animated
                    type="submit"
                    width="w-fit"
                  />
                )}
              </div>
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default OnboardFour;
