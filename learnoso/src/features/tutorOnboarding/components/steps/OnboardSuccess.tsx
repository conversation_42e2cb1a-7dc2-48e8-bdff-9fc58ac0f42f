import { LearnosoButton } from "@/components/atoms";
import { t } from "i18next";
import React from "react";
import { useNavigate } from "react-router-dom";

const OnboardSuccess: React.FC = () => {
  const navigate = useNavigate();
  const goHome = () => navigate("/", { replace: true });
  return (
    <>
      <section className="text-dark p-4 min-h-screen flex items-center justify-center">
        <div className="flex flex-col gap-10 items-center">
          <div className="text-green-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="144"
              height="145"
              viewBox="0 0 144 145"
              fill="none"
            >
              <path
                d="M72 0.5C57.7598 0.5 43.8393 4.72273 31.999 12.6342C20.1586 20.5457 10.9302 31.7905 5.48071 44.9468C0.0311962 58.1031 -1.39464 72.5799 1.38349 86.5465C4.16163 100.513 11.019 113.342 21.0883 123.412C31.1577 133.481 43.9869 140.338 57.9535 143.117C71.9202 145.895 86.397 144.469 99.5533 139.019C112.71 133.57 123.954 124.341 131.866 112.501C139.777 100.661 144 86.7403 144 72.5C144 53.4044 136.414 35.0909 122.912 21.5883C109.409 8.08569 91.0956 0.5 72 0.5ZM119.025 48.335L59.895 107.42L24.975 72.5C23.7816 71.3065 23.1111 69.6878 23.1111 68C23.1111 66.3122 23.7816 64.6935 24.975 63.5C26.1685 62.3065 27.7872 61.636 29.475 61.636C31.1629 61.636 32.7816 62.3065 33.975 63.5L59.985 89.51L110.115 39.425C110.706 38.8341 111.408 38.3653 112.18 38.0455C112.952 37.7257 113.779 37.561 114.615 37.561C115.451 37.561 116.278 37.7257 117.05 38.0455C117.823 38.3653 118.524 38.8341 119.115 39.425C119.706 40.0159 120.175 40.7175 120.495 41.4896C120.814 42.2617 120.979 43.0893 120.979 43.925C120.979 44.7607 120.814 45.5883 120.495 46.3604C120.175 47.1325 119.706 47.8341 119.115 48.425L119.025 48.335Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div className="flex flex-col items-center gap-3">
            <h1 className="text-3xl">
              {t("tutoronboarding.onboardsuccess.account_updated_successful")}{" "}
              {/* Account Updated <span className="text-green-500">Successful</span> important */}
            </h1>
            <p>{t("tutoronboarding.onboardsuccess.notified_application")} </p>
            <LearnosoButton title="Go Home" action={goHome} />
          </div>
        </div>
      </section>
    </>
  );
};

export default OnboardSuccess;
