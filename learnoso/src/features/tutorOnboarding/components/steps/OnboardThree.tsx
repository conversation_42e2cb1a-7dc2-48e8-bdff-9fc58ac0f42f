import { useOnboardTutorEducationCertificationMutation } from "@/app/services/tutor/tutor.service";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Spinner } from "@/components/atoms";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import dayjs from "dayjs";
import { t } from "i18next";
import React from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { useTutorOnboardContext } from "../../hooks";
import { OnboardThreeFormData } from "../../types";

const subjects: string[] = [
  "Computer Science",
  "Software Engineering",
  "Data Science",
  "Artificial Intelligence",
  "Web Development",
  "Cybersecurity",
  "Digital Marketing",
  "Project Management",
  "Business Administration",
  "Human Resources",
  "Accounting",
  "Finance",
  "Marketing",
  "Law",
  "Psychology",
  "Sociology",
  "History",
  "Philosophy",
  "English Language",
  "Mathematics",
  "Physics",
  "Chemistry",
  "Biology",
  "Design",
  "Music",
  "Art",
  "Languages",
];

const OnboardThree = () => {
  const { user } = useAuth();
  const { handleNext, handlePrev } = useTutorOnboardContext();
  const [
    postTutorEducationAndCertifications,
    { isLoading: isPostingTutorEducationAndCertification },
  ] = useOnboardTutorEducationCertificationMutation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<OnboardThreeFormData>({
    defaultValues: {
      certificates: [
        {
          certificateSubject: "",
          certificateTitle: "",
          certificateDescription: "",
          certificateIssuedBy: "",
          startDate: "",
          endDate: "",
          fileUpload: [] as FileList[],
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    name: "certificates",
    control,
  });

  const onSubmit = async (data: OnboardThreeFormData) => {
    const dataOfInterest = data.certificates[0];
    const certifications: ITutorEducationCertification = {
      attachment: dataOfInterest.fileUpload[0] as unknown as File,
      certificate: dataOfInterest.certificateTitle,
      description: dataOfInterest.certificateDescription,
      end_date: dataOfInterest.endDate,
      start_date: dataOfInterest.startDate,
      institution: dataOfInterest.certificateIssuedBy,
      subject: dataOfInterest.certificateSubject,
      user_id: user!.id,
    };
    const formData = new FormData();
    formData.append("attachment", certifications.attachment);
    formData.append("certificate", certifications.certificate);
    formData.append("description", certifications.description);
    formData.append(
      "end_date",
      dayjs(certifications.end_date).format("YYYY-MM-DD"),
    );
    formData.append(
      "start_date",
      dayjs(certifications.start_date).format("YYYY-MM-DD"),
    );
    formData.append("institution", certifications.institution);
    formData.append("subject", certifications.subject);
    formData.append("user_id", `${certifications.user_id}`);

    await RequestInterceptor.handleRequest(
      () => postTutorEducationAndCertifications(formData).unwrap(),
      { onSuccess: handleNext },
      "Upload Tutor Certifications",
    );
  };

  return (
    <>
      <section className="text-dark p-4">
        <div className="flex min-h-screen items-center max-w-7xl mx-auto gap-6">
          <div className="flex-1 sm:flex hidden self-start mt-28">
            <img src="/assets/images/undraw_certificate_re_yadi 1.png" alt="" />
          </div>
          <div className="flex-1 p-4 sm:p-16 shadow-md rounded-md border-dark/10 border">
            <form
              className="flex flex-col gap-6"
              onSubmit={handleSubmit(onSubmit)}
            >
              <div className="flex flex-col gap-4">
                <div className="space-y-1">
                  <h1 className="text-2xl font-bold">
                    {t(
                      "tutoronboarding.onboardthree.education_and_certification",
                    )}
                  </h1>
                  <p className="text-sm">
                    {t("tutoronboarding.onboardthree.description")}
                  </p>
                </div>
                {fields.map((field, index) => (
                  <div key={field.id} className="flex flex-col gap-4">
                    {index > 0 && (
                      <h2 className="text-xl font-semibold">
                        {t("tutoronboarding.onboardthree.certicate")} #
                        {index + 1}
                      </h2>
                    )}

                    <div className="flex flex-col gap-1">
                      <label htmlFor="subject">
                        {t("tutoronboarding.onboardthree.subject")}
                      </label>
                      <select
                        {...register(
                          `certificates.${index}.certificateSubject`,
                          {
                            required: {
                              value: true,
                              message: "field is required",
                            },
                          },
                        )}
                        id="subject"
                        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                        defaultValue=""
                      >
                        <option value="" disabled>
                          {t("tutoronboarding.onboardthree.select_subject")}
                        </option>
                        {subjects.map((item, index) => (
                          <option key={index} value={item}>
                            {item}
                          </option>
                        ))}
                      </select>

                      {errors.certificates?.[index]?.certificateSubject && (
                        <p className="error">
                          {
                            errors.certificates?.[index]?.certificateSubject
                              ?.message
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      <label htmlFor="certificateTitle">Certificate</label>
                      <input
                        {...register(`certificates.${index}.certificateTitle`, {
                          required: {
                            value: true,
                            message: "field is required",
                          },
                        })}
                        type="text"
                        placeholder="Type certificate here"
                        id="certificateTitle"
                        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                      />

                      {errors.certificates?.[index]?.certificateTitle && (
                        <p className="error">
                          {
                            errors.certificates?.[index]?.certificateTitle
                              .message
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      <label htmlFor="certificateDescription">
                        {t(
                          "tutoronboarding.onboardthree.description_of_certicate",
                        )}
                      </label>
                      <input
                        type="text"
                        id="certificateDescription"
                        {...register(
                          `certificates.${index}.certificateDescription`,
                          {
                            required: {
                              value: true,
                              message: "field is required",
                            },
                          },
                        )}
                        placeholder={t(
                          "tutoronboarding.onboardthree.certificate_placeholder",
                        )}
                        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                      />

                      {errors.certificates?.[index]?.certificateDescription && (
                        <p className="error">
                          {
                            errors.certificates?.[index]?.certificateDescription
                              .message
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      <label htmlFor="certificateIssuedBy">
                        {t("tutoronboarding.onboardthree.issued_by")}
                      </label>
                      <input
                        type="text"
                        id="certificateIssuedBy"
                        {...register(
                          `certificates.${index}.certificateIssuedBy`,
                          {
                            required: {
                              value: true,
                              message: "field is required",
                            },
                          },
                        )}
                        placeholder={t(
                          "tutoronboarding.onboardthree.issued_by_placeholder",
                        )}
                        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                      />

                      {errors.certificates?.[index]?.certificateIssuedBy && (
                        <p className="error">
                          {
                            errors.certificates?.[index]?.certificateIssuedBy
                              .message
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex flex-col gap-1">
                      <label>
                        {" "}
                        {t("tutoronboarding.onboardthree.years_of_study")}
                      </label>

                      <div className="flex gap-2">
                        <div>
                          <input
                            type="text"
                            placeholder={t(
                              "tutoronboarding.onboardthree.start_date_placeholder",
                            )}
                            onFocus={(e) => (e.target.type = "date")}
                            {...register(`certificates.${index}.startDate`, {
                              valueAsDate: true,
                              required: {
                                value: true,
                                message: "field is required",
                              },
                            })}
                            className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                          />

                          {errors.certificates?.[index]?.startDate && (
                            <p className="error">
                              {errors.certificates?.[index]?.startDate.message}
                            </p>
                          )}
                        </div>

                        <div>
                          <input
                            type="text"
                            placeholder={t(
                              "tutoronboarding.onboardthree.end_date_placeholder",
                            )}
                            onFocus={(e) => (e.target.type = "date")}
                            {...register(`certificates.${index}.endDate`, {
                              valueAsDate: true,
                              required: {
                                value: true,
                                message: "field is required",
                              },
                            })}
                            className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                          />

                          {errors.certificates?.[index]?.endDate && (
                            <p className="error">
                              {errors.certificates?.[index]?.endDate.message}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-1">
                      <p>
                        {" "}
                        {t("tutoronboarding.onboardthree.upload_certificate")}
                      </p>
                      <label
                        htmlFor="fileUpload"
                        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4 flex justify-center items-center gap-2 cursor-pointer"
                      >
                        <span>
                          <svg
                            width="24"
                            height="25"
                            viewBox="0 0 24 25"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M9 17.4453V11.4453L7 13.4453"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M9 11.4453L11 13.4453"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M22 10.4453V15.4453C22 20.4453 20 22.4453 15 22.4453H9C4 22.4453 2 20.4453 2 15.4453V9.44531C2 4.44531 4 2.44531 9 2.44531H14"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M22 10.4453H18C15 10.4453 14 9.44531 14 6.44531V2.44531L22 10.4453Z"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </span>
                        <span>
                          {" "}
                          {t("tutoronboarding.onboardthree.select_file")}
                        </span>
                      </label>
                      <input
                        type="file"
                        id="fileUpload"
                        {...register(`certificates.${index}.fileUpload`, {
                          required: {
                            value: true,
                            message: "field is required",
                          },
                        })}
                        className="hidden"
                      />

                      {errors.certificates?.[index]?.fileUpload && (
                        <p className="error">
                          {errors.certificates?.[index]?.fileUpload.message}
                        </p>
                      )}
                    </div>

                    {index > 0 && (
                      <div>
                        <button
                          className="bg-red-500 text-white py-2 px-6 rounded-md border border-red"
                          onClick={() => remove(index)}
                        >
                          {" "}
                          {t("tutoronboarding.onboardthree.remove_certificate")}
                          {index + 1}
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <div>
                <button
                  onClick={() => {
                    append({
                      certificateSubject: "",
                      certificateTitle: "",
                      certificateDescription: "",
                      certificateIssuedBy: "",
                      startDate: "",
                      endDate: "",
                      fileUpload: [] as FileList[],
                    });
                  }}
                  className="flex text-primary gap-2 items-center justify-center capitalize py-2 px-6 rounded-md hover:bg-primary/5 cursor-not-allowed"
                  disabled
                >
                  <svg
                    width="19"
                    height="18"
                    viewBox="0 0 19 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.1094 9.75H9.85938V15H8.35938V9.75H3.10938V8.25H8.35938V3H9.85938V8.25H15.1094V9.75Z"
                      fill="currentColor"
                    />
                  </svg>
                  <span>
                    {t("tutoronboarding.onboardthree.add_another_certificate")}
                  </span>
                </button>
              </div>

              <div className="flex gap-4 justify-between">
                <LearnosoButton
                  title={t("tutoronboarding.onboardthree.prev")}
                  animated
                  width="w-fit"
                  action={handlePrev}
                />

                {isPostingTutorEducationAndCertification ? (
                  <Spinner />
                ) : (
                  <LearnosoButton
                    title={t("tutoronboarding.onboardthree.next")}
                    animated
                    type="submit"
                    width="w-fit"
                  />
                )}
              </div>
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default OnboardThree;
