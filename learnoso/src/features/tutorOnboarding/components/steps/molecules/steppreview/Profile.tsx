import { EmbedVideo } from "@/components/molecules";
import { motion } from "framer-motion";
import React from "react";
import { t } from "i18next";

const Profile: React.FC<{
  profilePhoto: string;
  profileVideo: string;
}> = ({ profilePhoto, profileVideo }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.8 }}
  >
    <h2 className="text-xl font-bold">Your Profile</h2>
    <div className="space-y-2">
      <h3 className="font-bold">Profile Photo:</h3>
      <img
        className="w-56 h-56 object-cover object-center"
        src={profilePhoto}
      />
    </div>
    <div className="space-y-2">
      <h3 className="font-bold leading-3">
        {t("tutoronboarding.molecules.steppreview.profile.video_url")}
      </h3>
      <p className="max-w-2xl text-xs">
        {t("tutoronboarding.molecules.steppreview.profile.short_video")}
      </p>
      <a target="_blank" href={profileVideo}>
        {profileVideo}
      </a>
      <div className="h-56 w-56">
        <EmbedVideo videoUrl={profileVideo} />
      </div>
    </div>
  </motion.div>
);

export default Profile;
