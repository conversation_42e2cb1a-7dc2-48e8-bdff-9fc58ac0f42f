import { OnboardStep } from "@/features/tutorOnboarding/types";
import { motion } from "framer-motion";
import React from "react";

const NavigationSteps = ({
  steps,
  setCurrentStep,
}: {
  steps?: OnboardStep[];
  setCurrentStep: any;
}) => (
  <motion.div
    className="flex gap-8 justify-between my-4"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 1.2 }}
  >
    {steps &&
      steps.map((step, index) => (
        <button
          key={index}
          className="flex gap-1 items-center text-primary"
          onClick={() => setCurrentStep(index)}
        >
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill={`${step.isComplete ? "blue" : "grey"}`}
            >
              <path
                d="M9 16.5C13.125 16.5 16.5 13.125 16.5 9C16.5 4.875 13.125 1.5 9 1.5C4.875 1.5 1.5 4.875 1.5 9C1.5 13.125 4.875 16.5 9 16.5Z"
                stroke="#292D32"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M5.8125 8.99994L7.935 11.1224L12.1875 6.87744"
                stroke="white"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </span>
          <span className="relative bottom-[1.2px]">{step.name}</span>
        </button>
      ))}
  </motion.div>
);

export default NavigationSteps;
