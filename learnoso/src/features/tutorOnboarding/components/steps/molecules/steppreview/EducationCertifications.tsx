import { motion } from "framer-motion";
import React from "react";
import { t } from "i18next";

const EducationCertifications: React.FC<{
  certificates: {
    certificateSubject: string;
    certificateTitle: string;
    certificateDescription: string;
    certificateIssuedBy: string;
    startDate: string;
    endDate: string;
    fileUploaded: string;
  }[];
}> = ({ certificates }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.6 }}
  >
    <h2 className="text-xl font-bold">
      {" "}
      {t(
        "tutoronboarding.molecules.steppreview.educationcertification.education_certification",
      )}
    </h2>
    <p className="text-xs">
      {t(
        "tutoronboarding.molecules.steppreview.educationcertification.mention",
      )}
    </p>
    {certificates.map((certificate, index) => {
      const {
        certificateSubject,
        certificateDescription,
        certificateIssuedBy,
        certificateTitle,
        startDate,
        endDate,
        fileUploaded,
      } = certificate;
      return (
        <div className="space-y-2" key={index}>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.subject",
              )}{" "}
            </h3>
            <p>{certificateSubject}</p>
          </div>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.certificate",
              )}{" "}
            </h3>
            <p>{certificateTitle}</p>
          </div>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.description_of_certificate",
              )}
            </h3>
            <div className="border border-dark/40 p-4 text-sm">
              {certificateDescription}
            </div>
          </div>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.issued_by",
              )}{" "}
            </h3>
            <p>{certificateIssuedBy}</p>
          </div>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.year_of_study",
              )}
            </h3>
            <div className="flex gap-4 max-w-2xl">
              <div className="border border-dark/40 p-4 flex-1 text-sm">
                {startDate}
              </div>
              <div className="border border-dark/40 p-4 flex-1 text-sm">
                {endDate}
              </div>
            </div>
          </div>
          <div>
            <h3 className="font-bold">
              {t(
                "tutoronboarding.molecules.steppreview.educationcertification.upload_file",
              )}{" "}
            </h3>
            <a href="#" target="_blank">
              {fileUploaded}
            </a>
          </div>
        </div>
      );
    })}
  </motion.div>
);

export default EducationCertifications;
