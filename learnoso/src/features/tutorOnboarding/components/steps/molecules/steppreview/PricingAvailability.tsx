import { motion } from "framer-motion";
import React from "react";
import { t } from "i18next";

const PricingAvailability: React.FC<{
  currency: string;
  price: number;
  timeslots: {
    day:
      | "Monday"
      | "Tuesday"
      | "Wednesday"
      | "Thursday"
      | "Friday"
      | "Saturday"
      | "Sunday";
    startTime: string;
    endTime: string;
  }[];
}> = ({ currency, price, timeslots }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 1 }}
  >
    <h2 className="text-xl font-bold">
      {t(
        "tutoronboarding.molecules.steppreview.pricingavailable.pricing_available",
      )}
    </h2>
    <div>
      <h3 className="font-bold">
        {t("tutoronboarding.molecules.steppreview.pricingavailable.pricing")}
      </h3>
      <p className="text-xs">
        {t(
          "tutoronboarding.molecules.steppreview.pricingavailable.pricing_per_hour",
        )}
      </p>
      <p>
        {price} {currency}
      </p>
    </div>
    <div>
      <h3 className="font-bold">
        {t(
          "tutoronboarding.molecules.steppreview.pricingavailable.availability",
        )}
      </h3>
      <p className="text-xs">
        {t(
          "tutoronboarding.molecules.steppreview.pricingavailable.working_hour",
        )}
      </p>
      <div className="grid grid-cols-2 gap-2 mt-4">
        {timeslots.map(({ day, startTime, endTime }) => (
          <div className="space-y-1">
            <p>{day}</p>
            <div className="flex gap-2">
              <div className="border border-dark/40 rounded-md px-4 flex items-center gap-16 p-2 justify-between">
                <div className="text-xs">
                  {t(
                    "tutoronboarding.molecules.steppreview.pricingavailable.from",
                  )}
                </div>
                <div className="font-bold">{startTime}</div>
              </div>
              <div className="border border-dark/40 rounded-md px-4 flex items-center gap-16 p-2 justify-between">
                <div className="text-xs">
                  {t(
                    "tutoronboarding.molecules.steppreview.pricingavailable.to",
                  )}
                </div>
                <div className="font-bold">{endTime}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </motion.div>
);

export default PricingAvailability;
