import { motion } from "framer-motion";
import React from "react";
import { t } from "i18next";

const SelectedCourses: React.FC<{ courses: string[] }> = ({ courses }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5 }}
  >
    <h2 className="text-xl font-semibold">
      {t("tutoronboarding.molecules.steppreview.selectedcourses.select_course")}
    </h2>
    <div className="flex gap-8 p-6">
      {courses.map((course, index) => (
        <div className="flex gap-3 items-center" key={index}>
          <div className="w-6 h-6 rounded-full border border-[#D5D5D7] bg-primary" />
          <span>{course}</span>
        </div>
      ))}
    </div>
  </motion.div>
);

export default SelectedCourses;
