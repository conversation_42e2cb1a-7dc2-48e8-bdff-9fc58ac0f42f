import exp from "constants";
import { motion } from "framer-motion";

import React from "react";
import { t } from "i18next";

const AboutYou: React.FC<{
  experience: string;
  motivation: string;
}> = ({ experience, motivation }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.2 }}
  >
    <h2 className="text-xl font-bold mb-2">
      {t("tutoronboarding.molecules.steppreview.aboutyou.aboutyou")}{" "}
    </h2>
    <div className="flex flex-col gap-3">
      <div className="space-y-1">
        <h3 className="font-semibold">
          {t(
            "tutoronboarding.molecules.steppreview.aboutyou.teaching_experience",
          )}
        </h3>
        <p className="text-xs">
          {t("tutoronboarding.molecules.steppreview.aboutyou.tutor_required")}
        </p>
      </div>
      <div className="border border-dark/40 p-5 pb-8 text-sm">{experience}</div>
      <div className="space-y-1">
        <h3 className="font-semibold">
          {t("tutoronboarding.molecules.steppreview.aboutyou.motivation")}
        </h3>
        <p className="text-xs">
          {t("tutoronboarding.molecules.steppreview.aboutyou.tutor_motivation")}
        </p>
      </div>
      <div className="border border-dark/40 p-5 pb-8 text-sm">{motivation}</div>
    </div>
  </motion.div>
);

export default AboutYou;
