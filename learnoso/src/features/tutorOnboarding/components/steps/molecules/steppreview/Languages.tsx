import { motion } from "framer-motion";
import React from "react";
import { t } from "i18next";

const Languages: React.FC<{ languages: string[] }> = ({ languages }) => (
  <motion.div
    initial={{ y: 20, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ duration: 0.5, delay: 0.4 }}
  >
    <h3 className="font-semibold">
      {t("tutoronboarding.molecules.steppreview.languages.language")}
    </h3>
    <ul className="list-disc flex gap-6 list-inside">
      {languages.map((language, index) => (
        <li key={index}>{language}</li>
      ))}
    </ul>
  </motion.div>
);

export default Languages;
