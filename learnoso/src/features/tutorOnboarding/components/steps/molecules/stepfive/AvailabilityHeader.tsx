import { motion } from "framer-motion";
import React from "react";

import { t } from "i18next";

export const AvailabilityHeader = () => (
  <motion.div
    initial={{ opacity: 0, y: -20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    <h2 className="font-bold">
      {" "}
      {t(
        "tutoronboarding.molecules.stepfive.availableheader.availability",
      )}{" "}
    </h2>
    <p className="text-xs">
      {t("tutoronboarding.molecules.stepfive.availableheader.description")}
    </p>
  </motion.div>
);
