import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";

export const AddTimeSlotButton = ({
  append,
}: {
  append: ({}: any) => void;
}) => {
  return (
    <motion.button
      className="flex text-primary gap-2 items-center justify-center capitalize py-2 px-6 rounded-md hover:bg-primary/5"
      onClick={() => {
        append({
          enable: false,
          day: "Monday",
          startTime: "09:00",
          endTime: "15:00",
        });
      }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ duration: 0.2 }}
    >
      <svg
        width="19"
        height="18"
        viewBox="0 0 19 18"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.1094 9.75H9.85938V15H8.35938V9.75H3.10938V8.25H8.35938V3H9.85938V8.25H15.1094V9.75Z"
          fill="currentColor"
        />
      </svg>
      <span>
        {t("tutoronboarding.molecules.stepfive.addtimeslotbutton.time_slot")}{" "}
      </span>
    </motion.button>
  );
};
