import { Learnoso<PERSON>utton, Spinner } from "@/components/atoms";
import { motion } from "framer-motion";
import React from "react";

export const NavigationButtons: React.FC<{
  handlePrev: () => void;
  isLoading?: boolean;
}> = ({ handlePrev, isLoading }) => (
  <motion.div
    className="flex gap-4 justify-between"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <LearnosoButton title="Prev" animated width="w-fit" action={handlePrev} />
    {isLoading ? (
      <Spinner />
    ) : (
      <LearnosoButton title="Next" animated type="submit" width="w-fit" />
    )}
  </motion.div>
);
