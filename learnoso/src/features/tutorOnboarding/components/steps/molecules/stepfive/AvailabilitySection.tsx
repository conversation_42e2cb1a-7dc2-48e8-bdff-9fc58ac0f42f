import { motion } from "framer-motion";
import React from "react";
import { useFieldArray } from "react-hook-form";
import { AddTimeSlotButton } from "./AddTimeSlotButton";
import { AvailabilityHeader } from "./AvailabilityHeader";
import { EnableAvailability } from "./EnableAvailability";
import { TimeSlots } from "./TimeSlots";
import { TimezoneSelect } from "./TimeZoneSelect";

export const AvailabilitySection = ({
  register,
  control,
  errors,
  watch,
}: any) => {
  const { fields, append, remove } = useFieldArray({
    name: "timeslots",
    control,
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-4"
    >
      <AvailabilityHeader />
      <EnableAvailability control={control} watch={watch} />
      <TimezoneSelect register={register} errors={errors} />
      <TimeSlots
        fields={fields}
        register={register}
        control={control}
        remove={remove}
      />
      <AddTimeSlotButton append={append} />
    </motion.div>
  );
};
