import { IOSSwitch } from "@/components/atoms/IOSSwitch";
import { daysOfWeek } from "@/lib/util";
import { FormControlLabel } from "@mui/material";
import { AnimatePresence, motion } from "framer-motion";
import React from "react";
import { Controller } from "react-hook-form";
import { FaTrash } from "react-icons/fa";
import { TimeInput } from "./TimeInput";

export const TimeSlots = ({ fields, register, control, remove }: any) => (
  <AnimatePresence>
    {fields.map(
      (field: { id: React.Key | null | undefined }, index: number) => (
        <motion.div
          key={field.id}
          className="flex gap-4 justify-between items-center"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex gap-3">
            <Controller
              name={`timeslots.${index}.enable`}
              control={control}
              render={({ field: { onChange, value } }) => (
                <FormControlLabel
                  control={<IOSSwitch checked={value} onChange={onChange} />}
                  label=""
                />
              )}
            />
            <select
              {...register(`timeslots.${index}.day`)}
              className="px-2 outline-none"
            >
              {daysOfWeek.map((day) => (
                <option value={day} key={day}>
                  {day.slice(0, 3)}
                </option>
              ))}
            </select>
          </div>
          <div className="flex items-center gap-2">
            <TimeInput
              label="From:"
              id={`startTime-${index}`}
              register={register}
              name={`timeslots.${index}.startTime`}
            />
            <TimeInput
              label="To:"
              id={`endTime-${index}`}
              register={register}
              name={`timeslots.${index}.endTime`}
            />
            <button
              disabled={index === 0}
              className={`self-center ${
                index === 0 ? "cursor-not-allowed" : "cursor-pointer"
              }`}
              onClick={() => remove(index)}
            >
              <FaTrash className="text-red-500" />
            </button>
          </div>
        </motion.div>
      ),
    )}
  </AnimatePresence>
);
