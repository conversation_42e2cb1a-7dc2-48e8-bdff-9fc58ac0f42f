import { motion } from "framer-motion";
import React from "react";

export const TimeInput = ({ label, id, register, name }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="text-xs w-full outline-none border border-dark/40 rounded-md px-4 flex items-center gap-4 justify-between"
  >
    <motion.label
      htmlFor={id}
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: 0.1 }}
    >
      {label}
    </motion.label>
    <motion.input
      type="time"
      id={id}
      {...register(name)}
      className="text-base py-3 outline-none"
      initial={{ opacity: 0, x: 10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    />
  </motion.div>
);
