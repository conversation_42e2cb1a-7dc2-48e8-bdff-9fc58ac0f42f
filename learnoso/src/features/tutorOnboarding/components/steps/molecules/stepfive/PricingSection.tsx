import { currencies } from "@/lib/util";
import { motion } from "framer-motion";

import React from "react";
import { t } from "i18next";

export const PricingSection = ({ register, errors }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    className="space-y-2"
  >
    <div>
      <h2 className="font-bold">
        {t("tutoronboarding.molecules.stepfive.pricingsection.pricing")}
      </h2>
      <p className="text-xs">
        {t(
          "tutoronboarding.molecules.stepfive.pricingsection.pricing_per_hour",
        )}
      </p>
    </div>
    <div className="flex flex-col gap-2">
      <motion.label
        htmlFor="currency"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="text-xs"
      >
        {t("tutoronboarding.molecules.stepfive.pricingsection.select_country")}
      </motion.label>
      <motion.select
        {...register("currency", {
          required: {
            value: true,
            message: "This field is required",
          },
        })}
        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
        defaultValue=""
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <option value="" disabled>
          {t(
            "tutoronboarding.molecules.stepfive.pricingsection.select_currency",
          )}
        </option>
        {currencies.map((currency) => (
          <option value={currency} key={currency}>
            {currency}
          </option>
        ))}
      </motion.select>
      {errors.currency && <p className="error">{errors.currency.message}</p>}
      <motion.input
        type="number"
        placeholder={t(
          "tutoronboarding.molecules.stepfive.pricingsection.enter_price",
        )}
        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
        {...register("price", {
          valueAsNumber: true,
          required: {
            value: true,
            message: "This field is required",
          },
          validate: {
            nonZero: (fieldValue: number) =>
              fieldValue !== 0 || "Enter a valid value apart from zero",
          },
        })}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      />
      {errors.price && <p className="error">{errors.price.message}</p>}
    </div>
  </motion.div>
);
