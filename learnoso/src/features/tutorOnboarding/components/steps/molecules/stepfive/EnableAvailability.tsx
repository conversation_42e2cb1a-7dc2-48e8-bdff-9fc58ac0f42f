import { IOSSwitch } from "@/components/atoms/IOSSwitch";
import { FormControlLabel } from "@mui/material";
import { motion } from "framer-motion";
import React from "react";
import { Controller } from "react-hook-form";
import { t } from "i18next";

export const EnableAvailability = ({ control, watch }: any) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
  >
    <h3 className="font-bold">
      {t("tutoronboarding.molecules.stepfive.enableavailability.enable")}
    </h3>
    <div className="flex gap-4 justify-between items-center">
      <p>
        {t(
          "tutoronboarding.molecules.stepfive.enableavailability.quick_enable",
        )}
      </p>
      <div className="flex gap-3 items-center">
        <Controller
          name="availability"
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              control={<IOSSwitch checked={value} onChange={onChange} />}
              label=""
            />
          )}
        />
        <span className="text-[#949197]">
          {watch("availability") ? "Enabled" : "Disabled"}
        </span>
      </div>
    </div>
  </motion.div>
);
