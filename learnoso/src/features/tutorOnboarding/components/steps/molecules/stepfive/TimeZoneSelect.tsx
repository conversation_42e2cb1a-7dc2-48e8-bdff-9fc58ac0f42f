import { useFetchTimezonesQuery } from "@/app/services/util/util.service";
import { Spinner } from "@/components/atoms";
import { motion } from "framer-motion";
import React from "react";
import i18n from "@/lib/i18n";
import { t } from "i18next";

export const TimezoneSelect = ({ register, errors }: any) => {
  const { data: timezonesGMT, isLoading: isTimezonesLoading } =
    useFetchTimezonesQuery("");

  if (isTimezonesLoading) {
    return <Spinner />;
  }
  return (
    <motion.div
      className="flex flex-col gap-1"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <label htmlFor="timezone">
        {t(
          "tutoronboarding.molecules.stepfive.timezoneselect.select_time_zone",
        )}
      </label>
      <select
        id="timezone"
        className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
        {...register("timezone", {
          required: {
            value: true,
            message: "This field is required",
          },
        })}
        defaultValue=""
      >
        <option value="" disabled>
          {t(
            "tutoronboarding.molecules.stepfive.timezoneselect.select_time_zone",
          )}
        </option>
        {timezonesGMT?.data?.map((timezone: string) => (
          <option value={timezone} key={timezone}>
            {timezone}
          </option>
        ))}
      </select>
      {errors.timezone && <p className="error">{errors.timezone.message}</p>}
    </motion.div>
  );
};
