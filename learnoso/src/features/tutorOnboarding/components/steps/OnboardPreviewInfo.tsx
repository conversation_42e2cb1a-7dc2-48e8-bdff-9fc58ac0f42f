import { useGetTutorProfileMutation } from "@/app/services/tutor/tutor.service";
import { Spinner } from "@/components/atoms";
import { ModalWrapper } from "@/components/molecules";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { Language } from "@/types";
import React, { useEffect } from "react";
import { toast } from "react-toastify";
import { useTutorOnboardContext } from "../../hooks";
import { Course, Education } from "../../types";
import AboutYou from "./molecules/steppreview/AboutYou";
import EducationCertifications from "./molecules/steppreview/EducationCertifications";
import Languages from "./molecules/steppreview/Languages";
import NavigationSteps from "./molecules/steppreview/NavigationSteps";
import PricingAvailability from "./molecules/steppreview/PricingAvailability";
import Profile from "./molecules/steppreview/Profile";
import SelectedCourses from "./molecules/steppreview/SelectedCourses";
import { useTranslation } from "react-i18next";

const OnboardPreviewInfo = () => {
  const { setCurrentStep, steps } = useTutorOnboardContext();

  const submitData = (): void => {
    toast.success("submitted successfully");
    setCurrentStep(7);
  };

  const [getTutorProfile, { data: tutor, isLoading: isLoadingTutorProfile }] =
    useGetTutorProfileMutation();

  const { user } = useAuth();

  const getProfileData = async () => {
    await RequestInterceptor.handleRequest(() =>
      getTutorProfile({ user_id: user!.id }).unwrap(),
    );
  };

  useEffect(() => {
    getProfileData();
  }, []);

  if (isLoadingTutorProfile || !tutor) {
    return (
      <ModalWrapper
        isOpen={isLoadingTutorProfile}
        shouldStayOpenOnOverlayClicked
        onClose={() => {}}
      >
        <Spinner />
      </ModalWrapper>
    );
  }

  const courses = tutor.data.courses.map((course: Course) => course.name);
  const motivation = tutor.data.tutor.motivation_to_students;
  const experience = tutor.data.tutor.bio || "Experience not provided";
  const languages = tutor.data.languages.map((lang: Language) => lang.name);
  const certificates = tutor.data.educations.map((education: Education) => ({
    certificateSubject: education.subject,
    certificateDescription: education.description,
    certificateIssuedBy: education.institution,
    certificateTitle: education.certificate,
    startDate: education.start_date,
    endDate: education.end_date,
    fileUploaded: education.url,
  }));
  const profilePhoto =
    tutor.data.tutor.profile_picture || `https://picsum.photos/200/300`;
  const profileVideo =
    tutor.data.tutor.video_url || `https://www.youtube.com/watch?v=SqcY0GlETPk`;
  const { currency, price } = tutor.data.tutor;
  const timeslots = JSON.parse(tutor.data.tutor.availability).map(
    (slot: Availability) => ({
      day: slot.day_of_the_week,
      startTime: slot.availability.from,
      endTime: slot.availability.to,
    }),
  );
  const { t } = useTranslation();

  return (
    <section className="text-dark p-4 py-8">
      <div className="min-h-screen max-w-7xl mx-auto p-8 pb-0 border border-dark/10 rounded-md shadow-md flex flex-col gap-8">
        <div className="flex flex-col items-center gap-2">
          <h1 className="text-3xl font-bold tracking-wide">
            {t("tutoronboarding.onboardpreviewinfo.information_preview")}
          </h1>
          <p> {t("tutoronboarding.onboardpreviewinfo.information_submit")}</p>
        </div>
        <div>
          <SelectedCourses courses={courses} />
          <hr />
          <AboutYou experience={experience} motivation={motivation} />
          <hr />
          <Languages languages={languages} />
          <hr />
          <EducationCertifications certificates={certificates} />
          <hr />
          <Profile profilePhoto={profilePhoto} profileVideo={profileVideo} />
          <hr />
          <PricingAvailability
            currency={currency}
            price={price}
            timeslots={timeslots}
          />
          <hr />
          <NavigationSteps steps={steps} setCurrentStep={setCurrentStep} />
        </div>
      </div>
      <button
        onClick={submitData}
        className="bg-primary text-white py-2 px-12 rounded-md border border-primary w-fit mx-auto mt-12 mb-4 block"
      >
        {t("tutoronboarding.onboardpreviewinfo.submit")}
      </button>
    </section>
  );
};

export default OnboardPreviewInfo;
