import { useFetchCoursesQuery } from "@/app/services/course/course.service";
import { useOnboardTutorCourseMutation } from "@/app/services/tutor/tutor.service";
import { LearnosoButton, Spinner } from "@/components/atoms";
import { Course } from "@/features/tutor/types";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import React from "react";
import { Controller, useForm } from "react-hook-form";
import { useTutorOnboardContext } from "../../hooks";
import { OnboardOneFormData } from "../../types";
import { t } from "i18next";

const OnboardOne = () => {
  const { handleNext } = useTutorOnboardContext();
  const {
    data: courses,
    isLoading: isFetchingCourses,
    isError,
  } = useFetchCoursesQuery("");

  const [
    onboardTutorCourse,
    {
      isLoading: isOnboardingTutorCourses,
      isError: isOnboardingTutorCoursesError,
    },
  ] = useOnboardTutorCourseMutation();

  const { user } = useAuth();

  const onSubmit = async (data: OnboardOneFormData) => {
    const courses: ITutorCourse = {
      course_ids: data.courses,
      user_id: user?.id!,
    };

    await RequestInterceptor.handleRequest(
      () => onboardTutorCourse(courses).unwrap(),
      {
        onSuccess: handleNext,
      },
      "Tutor Onboard Courses",
    );
    console.log(data);
  };

  const {
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<OnboardOneFormData>({
    defaultValues: { courses: [] },
  });
  const selectedCourses = watch("courses");
  return (
    <>
      <section className="text-dark px-4">
        <div className="flex min-h-screen items-center max-w-7xl mx-auto gap-6">
          <div className="flex-1 sm:flex hidden mt-28">
            <img
              src="/assets/images/account1.png"
              alt=""
              className="w-full h-full"
            />
          </div>
          <div className="flex-1 flex flex-col gap-6 justify-center p-4 sm:p-16 shadow-md rounded-md border-dark/10 border">
            <div className="w-fit flex flex-col gap-4">
              <h1 className="text-3xl text-center">
                {t("tutoronboarding.onboardone.welcome_to_learnoso")}{" "}
                <span className="font-bold text-primary">
                  {user?.first_name}
                </span>
              </h1>
              <p className="text-sm">
                {t("tutoronboarding.onboardone.select_max_two")}
              </p>
            </div>
            <form
              className="flex flex-col gap-3"
              onSubmit={handleSubmit(onSubmit)}
            >
              <p className="text-xl font-semibold">
                {t("tutoronboarding.onboardone.select_course")}

                {/* Select course to be <span className="text-primary">taught</span> */}
              </p>
              {isFetchingCourses ? (
                <Spinner size="small" />
              ) : isError ? (
                <> {t("tutoronboarding.onboardone.error_fetching")} </>
              ) : (
                courses?.data?.map((course: Course, index: number) => (
                  <div key={course.id} className="flex gap-2">
                    <Controller
                      name="courses"
                      control={control}
                      render={({ field }) => (
                        <input
                          type="checkbox"
                          id={`course-${index}`}
                          value={course.id}
                          checked={selectedCourses.includes(course.id)}
                          onChange={(e) => {
                            const checked = e.target.checked;
                            const newSelectedCourses = checked
                              ? [...selectedCourses, course.id]
                              : selectedCourses.filter(
                                  (id: number) => id !== course.id,
                                );
                            field.onChange(newSelectedCourses);
                          }}
                          disabled={
                            selectedCourses.length === 2 &&
                            !selectedCourses.includes(course.id)
                          }
                        />
                      )}
                    />
                    <label htmlFor={`course-${index}`}>{course.name}</label>
                  </div>
                ))
              )}
              {errors.courses && (
                <p className="error">{errors.courses.message}</p>
              )}
              {isOnboardingTutorCourses ? (
                <Spinner />
              ) : (
                <LearnosoButton
                  title={t("tutoronboarding.onboardone.next")}
                  animated
                  type="submit"
                  disabled={isFetchingCourses}
                />
              )}
            </form>
          </div>
        </div>
      </section>
    </>
  );
};

export default OnboardOne;
