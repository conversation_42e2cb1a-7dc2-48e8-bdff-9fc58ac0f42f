import React from "react";

export type OnboardOneFormData = {
  courses: number[];
};

export type OnboardTwoFormData = {
  experience: string;
  language: number;
  motivation: string;
};

export type TutorCertificate = {
  certificateSubject: string;
  certificateTitle: string;
  certificateDescription: string;
  certificateIssuedBy: string;
  startDate: string;
  endDate: string;
  fileUpload: FileList[];
};

export type OnboardThreeFormData = {
  certificates: TutorCertificate[];
};

export type OnboardFourFormData = {
  profilePhoto: FileList[];
  profileVideo: string;
};

export type TimeSlot = {
  enable: boolean;
  day:
    | "Monday"
    | "Tuesday"
    | "Wednesday"
    | "Thursday"
    | "Friday"
    | "Saturday"
    | "Sunday";
  startTime: string;
  endTime: string;
};

export type OnboardFiveFormData = {
  currency: string;
  price: number;
  availability: boolean;
  timezone: string;
  timeslots: TimeSlot[];
};

export type OnboardStep = {
  name: string;
  isComplete: boolean;
  id: number;
};

export type OnboardPropsTypeOne = { handleNext: () => void };

export type OnboardPropsTypeTwo = {
  handlePrev: () => void;
  handleNext: () => void;
};

export type OnboardPropsTypeThree = {
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>;
};
export interface ITutorProviderContext {
  currentStep: number;
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>;
  handleNext: () => void;
  handlePrev: () => void;
  steps?: OnboardStep[];
  updateOnboardStepStatus?: (step: Partial<OnboardStep>) => void;
  markCurrentStepAsCompleted?: () => void;
  tutorOnboardData: TutorOnboardDataType;
  updateTutorOnboardData: (_key: TutorDataKey, _value: TutorValueType) => void;
}

export type TutorDataKey =
  | "stepOneData"
  | "stepTwoData"
  | "stepThreeData"
  | "stepFourData"
  | "stepFiveData";

export type TutorValueType =
  | OnboardOneFormData
  | OnboardTwoFormData
  | OnboardThreeFormData
  | OnboardFourFormData
  | OnboardFiveFormData;

export interface ITutorProviderProps {
  children: React.ReactNode;
}
export interface StepComponents {
  1: () => React.JSX.Element;
  2: () => React.JSX.Element;
  3: () => React.JSX.Element;
  4: () => React.JSX.Element;
  5: () => React.JSX.Element;
  6: () => React.JSX.Element;
  7: React.Element;
}

export type TutorOnboardStepNumber = 1 | 2 | 3 | 4 | 5 | 6 | 7;
export type TutorOnboardStepAbsolutePathName =
  | "OnboardOne"
  | "OnboardTwo"
  | "OnboardThree"
  | "OnboardFour"
  | "OnboardFive"
  | "OnboardPreviewInfo"
  | "OnboardSuccess";
export type JSX = () => React.JSX.Element;
export type LazyLoadedOnboardStep = {
  [key: number]: React.LazyExoticComponent<JSX>;
};

export type TutorOnboardDataType = {
  stepOneData: OnboardOneFormData;
  stepTwoData: OnboardTwoFormData;
  stepThreeData: OnboardThreeFormData;
  stepFourData: OnboardFourFormData;
  stepFiveData: OnboardFiveFormData;
};

interface TutorProfileResponse {
  success: boolean;
  message: string;
  data: TutorProfileData;
}

export interface TutorProfileData {
  id: number;
  first_name: string;
  last_name: string;
  country: string;
  email: string;
  email_verified_at: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  tutor: TutorDetails;
  courses: Course[];
  educations: Education[];
  languages: Language[];
  onboarding_status: OnboardingStatus;
}

export interface TutorDetails {
  id: number;
  user_id: number;
  profile_picture: string | null;
  bio: string | null;
  phone_number: string | null;
  city: string | null;
  native_language: string | null;
  rating: number | null;
  price: number;
  availability: Availability[];
  currency: string;
  is_active: number;
  short_description: string;
  motivation_to_students: string;
  created_at: string;
  updated_at: string;
  video_url: string;
}

export interface Availability {
  time: string;
  availability: {
    from: string;
    to: string;
  };
  day_of_the_week: string;
}

interface Course {
  id: number;
  name: string;
  description: string;
  deleted_at: string | null;
  pivot: {
    user_id: number;
    course_id: number;
  };
}

export interface Education {
  id: number;
  user_id: number;
  subject: string;
  certificate: string;
  institution: string;
  start_date: string;
  end_date: string;
  description: string;
  url: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface Language {
  name: string;
  id: number;
}

export interface OnboardingStatus {
  id: number;
  user_id: number;
  current_step: number;
  data: any | null;
  created_at: string;
  updated_at: string;
  onboarding_type: string;
}
