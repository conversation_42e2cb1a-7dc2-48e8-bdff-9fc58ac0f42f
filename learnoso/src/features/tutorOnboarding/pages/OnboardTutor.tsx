import Spinner from "@/components/atoms/Spinner";
import AuthGaurd from "@/features/auth/components/AuthGaurd";
import React from "react";
import OnboardFive from "../components/steps/OnboardFive";
import OnboardFour from "../components/steps/OnboardFour";
import OnboardOne from "../components/steps/OnboardOne";
import OnboardPreviewInfo from "../components/steps/OnboardPreviewInfo";
import OnboardSuccess from "../components/steps/OnboardSuccess";
import OnboardThree from "../components/steps/OnboardThree";
import OnboardTwo from "../components/steps/OnboardTwo";
import { useTutorOnboardContext } from "../hooks";
import TutorOnboardProvider from "../provider/TutorOnboardProvider";
import { TutorOnboardStepNumber } from "../types";

const OnboardTutorComponent: React.FC = () => {
  const { currentStep } = useTutorOnboardContext();

  const mappedSteps: Record<TutorOnboardStepNumber, React.FC> = {
    1: OnboardOne,
    2: OnboardTwo,
    3: OnboardThree,
    4: OnboardFour,
    5: OnboardFive,
    6: OnboardPreviewInfo,
    7: OnboardSuccess,
  };

  const CurrentComponent = mappedSteps[currentStep as TutorOnboardStepNumber];

  return (
    <React.Suspense
      fallback={
        <div className="min-h-screen grid place-items-center">
          <Spinner size="small" />
        </div>
      }
    >
      <CurrentComponent />
    </React.Suspense>
  );
};

const OnboardTutor: React.FC = () => {
  return (
    <TutorOnboardProvider>
      <AuthGaurd>
        <OnboardTutorComponent />
      </AuthGaurd>
    </TutorOnboardProvider>
  );
};

export default OnboardTutor;
