import { usePreventUserOnboard } from "@/hooks";
import { assertTutorType } from "@/lib/assertions";
import React, { useState } from "react";
import {
  ITutorProviderContext,
  ITutorProviderProps,
  OnboardStep,
  TutorDataKey,
  TutorOnboardDataType,
  TutorValueType,
} from "../types";

const initialTutorOnboardData: TutorOnboardDataType = {
  stepOneData: { courses: [] },
  stepTwoData: {
    experience: "",
    language: 0,
    motivation: "",
  },
  stepThreeData: { certificates: [] },
  stepFourData: { profilePhoto: [], profileVideo: "" },
  stepFiveData: {
    currency: "",
    price: 0,
    availability: false,
    timezone: "",
    timeslots: [],
  },
};

const initialOnboardSteps: OnboardStep[] = [
  { name: "Introduction", isComplete: false, id: 1 },
  { name: "Experience and Language", isComplete: false, id: 2 },
  { name: "Certificates", isComplete: false, id: 3 },
  { name: "Profile Media", isComplete: false, id: 4 },
  { name: "Availability and Pricing", isComplete: false, id: 5 },
];

export const TutorOnboardContext = React.createContext<ITutorProviderContext>({
  currentStep: 1,
  setCurrentStep: () => {},
  handleNext: () => {},
  handlePrev: () => {},
  tutorOnboardData: initialTutorOnboardData,
  updateTutorOnboardData: (_key: TutorDataKey, _value: TutorValueType) => {},
});

const TutorOnboardProvider: React.FC<ITutorProviderProps> = ({ children }) => {
  const MAX_STEPS = 6;
  const INITIAL_STEP = 1;
  const [currentStep, setCurrentStep] = useState<number>(INITIAL_STEP);
  const [steps, setOnboardSteps] = useState<OnboardStep[]>(initialOnboardSteps);
  const [tutorOnboardData, setTutorOnboardData] =
    useState<TutorOnboardDataType>(initialTutorOnboardData);

  const updateTutorOnboardData = (key: TutorDataKey, value: TutorValueType) => {
    assertTutorType(key, value);
    setTutorOnboardData((prevData) => ({
      ...prevData,
      [key]: value,
    }));
  };

  const updateOnboardStepStatus = (step: Partial<OnboardStep>) => {
    const { id, isComplete } = step;
    setOnboardSteps((prevSteps) =>
      prevSteps.map((s) =>
        s.id === id ? { ...s, isComplete: !!isComplete } : s,
      ),
    );
  };

  const markCurrentStepAsCompleted = () => {
    setOnboardSteps((prevSteps) =>
      prevSteps.map((step) =>
        step.id === currentStep ? { ...step, isComplete: true } : step,
      ),
    );
  };

  const handleNext = () => {
    if (currentStep < MAX_STEPS) {
      markCurrentStepAsCompleted();
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrev = () => {
    if (currentStep > INITIAL_STEP) {
      updateOnboardStepStatus({ id: currentStep });
      setCurrentStep((prev) => prev - 1);
    }
  };

  usePreventUserOnboard("tutor");

  return (
    <TutorOnboardContext.Provider
      value={{
        currentStep,
        steps,
        tutorOnboardData,
        setCurrentStep,
        handleNext,
        handlePrev,
        updateOnboardStepStatus,
        markCurrentStepAsCompleted,
        updateTutorOnboardData,
      }}
    >
      {children}
    </TutorOnboardContext.Provider>
  );
};

export default TutorOnboardProvider;
