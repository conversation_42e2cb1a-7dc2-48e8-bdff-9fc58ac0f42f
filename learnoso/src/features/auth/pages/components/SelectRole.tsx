import React, { useState } from "react";
import { FaChalkboardTeacher, FaUserGraduate } from "react-icons/fa";
import { useNavigate } from "react-router-dom";

const RoleSelectionPage = () => {
  const [selectedRole, setSelectedRole] = useState<"student" | "tutor" | null>(
    null,
  );
  const navigate = useNavigate();

  const handleContinue = () => {
    if (selectedRole) {
      navigate(`/${selectedRole}/dashboard`, { replace: true });
    }
  };

  return (
    <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-8">
      <h1 className="text-2xl font-semibold text-center text-dark mb-12">
        Select Account
      </h1>

      {/* Role Selection */}
      <div className="flex justify-center gap-8 mb-12">
        <div
          onClick={() => setSelectedRole("tutor")}
          className={`flex flex-col items-center cursor-pointer ${
            selectedRole === "tutor" ? "opacity-100" : "opacity-70"
          }`}
        >
          <div
            className={`mb-2 p-2 ${
              selectedRole === "tutor"
                ? "border rounded-full border-primary"
                : ""
            }`}
          >
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center bg-green`}
            >
              <FaChalkboardTeacher className="w-8 h-8 text-white" />
            </div>
          </div>
          <span className="text-gray-500-dark">Tutor</span>
        </div>

        <div
          onClick={() => setSelectedRole("student")}
          className={`flex flex-col items-center cursor-pointer  ${
            selectedRole === "student" ? "opacity-100" : "opacity-70"
          }`}
        >
          <div
            className={` mb-2 p-2 ${
              selectedRole === "student"
                ? "border rounded-full border-primary"
                : ""
            }`}
          >
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center bg-blue-600`}
            >
              <FaUserGraduate className="w-8 h-8 text-white" />
            </div>
          </div>
          <span className="text-dark">Student</span>
        </div>
      </div>

      {/* Continue Button */}
      <div className="flex justify-center">
        <button
          onClick={handleContinue}
          disabled={!selectedRole}
          className={`px-6 py-2 rounded ${
            selectedRole
              ? "bg-blue-600 text-white cursor-pointer"
              : "bg-gray/20 text-gray-500/80 cursor-not-allowed"
          }`}
        >
          Continue
        </button>
      </div>
    </div>
  );
};

export default RoleSelectionPage;
