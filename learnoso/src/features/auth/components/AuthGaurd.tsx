import { logOutUser } from "@/app/services/auth";
import { useAuth } from "@/hooks";
import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

interface AuthGuardProps {
  children: React.ReactNode;
}

const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { token, sessionId, sessionExpireAt, user } = useAuth();

  const isUserSessionValid = (): boolean => {
    if (!user) return false;
    if (!sessionExpireAt || !sessionId || !token) return false;
    if (Date.now() > sessionExpireAt) {
      logOutUser();
      return false;
    }
    return true;
  };

  useEffect(() => {
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    const isSessionValid = isUserSessionValid();

    if (!isSessionValid) {
      const timestamp = Date.now();
      const loginUrl = `/login?return=${pathname}&timestamp=${timestamp}&sessionId=${sessionId}`;
      navigate(loginUrl, { replace: true });
    }
  }, [token, pathname, sessionExpireAt, navigate, user, isInitialized]);

  if (!isInitialized) return null;

  return <>{children}</>;
};

export default AuthGuard;
