import { TypeAssertionError } from "@/lib/util";
import { DataKey, ITimeSlot, ValueType } from "../types";

export const isTimeSlot = (obj: ITimeSlot): obj is ITimeSlot => {
  if (typeof obj !== "object" || obj === null) {
    throw TypeAssertionError.from(obj, "ITimeSlot");
  }

  const { time, availability } = obj;
  if (typeof time !== "string") {
    throw TypeAssertionError.from(time, "string");
  }

  if (typeof availability !== "object" || availability === null) {
    throw TypeAssertionError.fromObject(availability, "object");
  }

  const { from, to } = availability.availability;
  if (typeof from !== "string" || typeof to !== "string") {
    throw TypeAssertionError.from(
      availability,
      "valid 'from' and 'to' strings",
    );
  }

  return true;
};

export const isTypeOrUndefined = (value: any, type: any): boolean => {
  return typeof value === type || typeof value === "undefined";
};

export const isArrayOfTypeOrUndefined = (
  value: any,
  typeCheck: (item: any) => boolean,
): boolean => {
  return Array.isArray(value) && value.every(typeCheck);
};

export const assertType = (key: DataKey, value: ValueType): void => {
  switch (key) {
    case "user_id":
    case "budget":
      if (typeof value !== "number" && typeof value !== "undefined") {
        throw TypeAssertionError.from(value, "number or undefined");
      }
      break;
    case "currency":
    case "prefered_language":
    case "timezone":
      if (typeof value !== "string" && typeof value !== "undefined") {
        throw TypeAssertionError.from(value, "string or undefined");
      }
      break;
    case "courses_of_preference":
    case "reasons_for_learning":
      if (
        !Array.isArray(value) ||
        !value.every(
          (item) =>
            typeof item === "string" ||
            typeof item === "number" ||
            typeof item === "undefined",
        )
      ) {
        throw TypeAssertionError.from(value, "array of strings or undefined");
      }
      break;
    case "availability_times":
      if (
        !Array.isArray(value) ||
        !value.every((item) => typeof item === "undefined" || isTimeSlot(item))
      ) {
        throw TypeAssertionError.from(
          value,
          "array of TimeSlot objects or undefined",
        );
      }
      break;
    default:
      throw new TypeAssertionError(`Invalid key: '${key}'`);
  }
};
