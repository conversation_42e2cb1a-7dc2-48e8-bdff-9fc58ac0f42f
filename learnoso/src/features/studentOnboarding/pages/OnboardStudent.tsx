import Spinner from "@/components/atoms/Spinner";
import AuthGaurd from "@/features/auth/components/AuthGaurd";
import React from "react";
import OnboardBudget from "../components/steps/OnboardBudget";
import OnboardFinal from "../components/steps/OnboardFinal";
import OnboardGetStarted from "../components/steps/OnboardGetStarted";
import OnboardGoals from "../components/steps/OnboardGoals";
import OnboardLevel from "../components/steps/OnboardLevel";
import OnboardSession from "../components/steps/OnboardSession";
import OnboardSubject from "../components/steps/OnboardSubject";
import { useStudentOnboardContext } from "../hooks";
import { StudentOnboardProvider } from "../provider";
import { OnboardStepNumber } from "../types";

const OnboardStudentComponent: React.FC = () => {
  const { currentStep } = useStudentOnboardContext();

  const mappedSteps: Record<OnboardStepNumber, React.FC> = {
    1: OnboardGetStarted,
    2: OnboardSubject,
    3: OnboardGoals,
    4: OnboardLevel,
    5: OnboardBudget,
    6: OnboardSession,
    7: OnboardFinal,
  };

  const CurrentComponent = mappedSteps[currentStep as OnboardStepNumber];

  return (
    <React.Suspense
      fallback={
        <div className="min-h-screen grid place-items-center">
          <Spinner size="small" />
        </div>
      }
    >
      <CurrentComponent />
    </React.Suspense>
  );
};

const OnboardStudent: React.FC = () => {
  return (
    <AuthGaurd>
      <StudentOnboardProvider>
        <OnboardStudentComponent />
      </StudentOnboardProvider>
    </AuthGaurd>
  );
};

export default OnboardStudent;
