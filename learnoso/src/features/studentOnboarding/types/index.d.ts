import React from "react";

export type OnboardStep = {
  name: string;
  isComplete: boolean;
  id: number;
};
export type DataKey =
  | "user_id"
  | "budget"
  | "currency"
  | "availability_times"
  | "timezone"
  | "courses_of_preference"
  | "prefered_language"
  | "reasons_for_learning";

export type ValueType =
  | number
  | string
  | number[]
  | string[]
  | TimeSlot[]
  | undefined;

export interface IStudentProviderContext {
  currentStep: number;
  setCurrentStep: React.Dispatch<React.SetStateAction<number>>;
  handleNext: () => void;
  steps?: OnboardStep[];
  updateOnboardStepStatus?: (step: Partial<OnboardStep>) => void;
  markCurrentStepAsCompleted?: () => void;
  updateStudentOnboardData: (key: DataKey, value: ValueType) => void;
  studentOnboardData: IStudentOnboardData;
}

export interface IStudentProviderProps {
  children: React.ReactNode;
}
export interface IStepComponents {
  1: () => React.JSX.Element;
  2: () => React.JSX.Element;
  3: () => React.JSX.Element;
  4: () => React.JSX.Element;
  5: () => React.JSX.Element;
  6: () => React.JSX.Element;
  7: React.Element;
}

export type OnboardStepNumber = 1 | 2 | 3 | 4 | 5 | 6 | 7;

export type OnboardStepAbsolutePathName =
  | "OnboardGetStarted"
  | "OnboardSubject"
  | "OnboardGoals"
  | "OnboardLevel"
  | "OnboardBudget"
  | "OnboardSession"
  | "OnboardFinal";

export type JSX = () => React.JSX.Element;

export type LazyLoadedOnboardStep = {
  [key: number]: React.LazyExoticComponent<JSX>;
};

export type OnboardSubjectFormData = {
  subject: { name: string; id: number };
  language: string;
};

export type OnboardSessionFormData = {
  timezone: string;
  availability: {
    morning: {
      available: boolean;
      from: string;
      to: string;
    };
    afternoon: {
      available: boolean;
      from: string;
      to: string;
    };
    evening: {
      available: boolean;
      from: string;
      to: string;
    };
  };
};

export type OnboardLevelFormData = {
  level: string;
};

export type OnboardGoalsFormData = {
  goals: string[];
};

export type OnboardBudgetFormData = {
  amount: number;
  currency: string;
};

interface IAvailability {
  from: string;
  to: string;
}

export type IAvailabilityTime = "morning" | "afternoon" | "evening";
interface ITimeSlot {
  time: IAvailabilityTime;
  availability: Availability;
}

export interface IStudentOnboardData {
  user_id: number;
  prefered_language: string;
  courses_of_preference?: number[] | undefined;
  budget?: number | undefined;
  reasons_for_learning?: string[] | undefined;
  currency?: string | undefined;
  availability_times?: TimeSlot[] | undefined;
  timezone?: string | undefined;
}
