import AuthGuard from "@/features/auth/components/AuthGaurd";
import { usePreventUserOnboard } from "@/hooks";
import React, { useState } from "react";
import {
  DataKey,
  IStudentOnboardData,
  IStudentProviderContext,
  IStudentProviderProps,
  OnboardStep,
  ValueType,
} from "../types";

const initialStudentOnboardData: IStudentOnboardData = {
  user_id: 1,
  budget: undefined,
  prefered_language: "",
  courses_of_preference: undefined,
  reasons_for_learning: undefined,
  currency: undefined,
  availability_times: undefined,
  timezone: undefined,
};

const initialOnboardStepValue: OnboardStep[] = [
  { name: "Get Started", isComplete: true, id: 1 },
  { name: "Subject and Language", isComplete: false, id: 2 },
  { name: "Goals", isComplete: false, id: 3 },
  { name: "Level", isComplete: false, id: 4 },
  { name: "Budget per lesson", isComplete: false, id: 5 },
  { name: "Session", isComplete: false, id: 6 },
];

export const StudentOnboardContext =
  React.createContext<IStudentProviderContext>({
    currentStep: 1,
    handleNext: () => {},
    setCurrentStep: () => {},
    studentOnboardData: initialStudentOnboardData,
    updateStudentOnboardData: (_key: DataKey, _value: ValueType) => {},
  });

const StudentOnboardProvider: React.FC<IStudentProviderProps> = ({
  children,
}) => {
  const MAX_STEPS = 6;
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [steps, setOnboardSteps] = useState<OnboardStep[]>(
    initialOnboardStepValue,
  );

  const [studentOnboardData, setStudentOnboardData] =
    useState<IStudentOnboardData>(initialStudentOnboardData);

  const updateStudentOnboardData = (key: DataKey, value: ValueType) => {
    // assertType(key, value);
    setStudentOnboardData((prevData) => ({ ...prevData, [key]: value }));
  };

  const updateOnboardStepStatus = (_step: Partial<OnboardStep>) => {
    const { id, isComplete } = _step;
    setOnboardSteps((prevSteps) =>
      prevSteps.map((step) =>
        step.id === id! ? { ...step, isComplete: isComplete! } : step,
      ),
    );
  };

  const markCurrentStepAsCompleted = () => {
    const id = currentStep;
    setOnboardSteps((prevSteps) =>
      prevSteps.map((step) =>
        step.id === id!! ? { ...step, isComplete: true } : step,
      ),
    );
  };

  const handleNext = () => {
    currentStep < MAX_STEPS &&
      setCurrentStep((previousStep) => previousStep + 1);
    markCurrentStepAsCompleted();
  };
  usePreventUserOnboard("student");

  return (
    <StudentOnboardContext.Provider
      value={{
        currentStep,
        steps,
        studentOnboardData,
        setCurrentStep,
        handleNext,
        updateOnboardStepStatus,
        markCurrentStepAsCompleted,
        updateStudentOnboardData,
      }}
    >
      <AuthGuard>{children}</AuthGuard>
    </StudentOnboardContext.Provider>
  );
};

export default StudentOnboardProvider;
