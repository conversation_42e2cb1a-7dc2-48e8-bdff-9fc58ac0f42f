import { LearnosoButton } from "@/components/atoms/Button";
import { motion } from "framer-motion";
import React from "react";
import { useForm } from "react-hook-form";
import { OnboardHeader, SVG1 } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { OnboardGoalsFormData } from "../../types";
import { useTranslation } from "react-i18next";

const goals = [
  "For Fun",
  "Career and Business",
  "Culture and Travel",
  "Academics",
];

const OnboardGoals: React.FC = () => {
  const { handleNext, updateStudentOnboardData } = useStudentOnboardContext();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OnboardGoalsFormData>();

  const onSubmit = (data: OnboardGoalsFormData) => {
    console.log(data);
    updateStudentOnboardData("reasons_for_learning", data.goals);
    handleNext();
  };

  const { t } = useTranslation();

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <OnboardHeader showSkip handleNext={handleNext} />

        <div className="flex flex-1 gap- items-center">
          <motion.div
            className="flex-1 hidden md:flex items-center"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <SVG1 />
          </motion.div>

          <motion.div
            className="flex-1 flex flex-col gap-8 pt-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="space-y-1">
              <h1 className="text-2xl font-bold">
                {" "}
                {t("student.steps.onboardgoals.what_are_your_goals")}
              </h1>
              <p> {t("student.steps.onboardgoals.tell_us_why")}</p>
            </div>

            <form className="space-y-8" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-3">
                <div className="space-y-3">
                  {goals.map((item, index) => (
                    <label
                      className="flex gap-4 items-center text-xl"
                      key={index}
                    >
                      <input
                        type="checkbox"
                        value={item}
                        {...register("goals", {
                          required: t(
                            "student.steps.onboardgoals.select_atleast_a_goal",
                          ),
                        })}
                      />
                      <span>{item}</span>
                    </label>
                  ))}
                </div>
                {errors.goals && (
                  <p className="error">{errors.goals.message}</p>
                )}
              </div>

              <div className="flex justify-end">
                <LearnosoButton
                  action={() => {}}
                  title={t("student.steps.onboardgoals.continue")}
                  icon={null}
                  type="submit"
                />
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default OnboardGoals;
