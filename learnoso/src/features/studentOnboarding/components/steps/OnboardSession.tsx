import { useFetchTimezonesQuery } from "@/app/services/util/util.service";
import { LearnosoButton } from "@/components/atoms/Button";
import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { useForm } from "react-hook-form";
import { OnboardHeader, SVG1, TimeSlot } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { OnboardSessionFormData } from "../../types";

type TimeOfDay = "morning" | "afternoon" | "evening";

const timeOfDay: TimeOfDay[] = ["morning", "afternoon", "evening"];

const OnboardSession: React.FC = () => {
  const { setCurrentStep, updateStudentOnboardData } =
    useStudentOnboardContext();
  const { data: timezones, isLoading: isFetchingTimezones } =
    useFetchTimezonesQuery("fetch timezones");

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    control,
  } = useForm<OnboardSessionFormData>({
    defaultValues: {
      timezone: "",
      availability: {
        morning: {
          available: false,
          from: "09:00",
          to: "12:00",
        },
        afternoon: {
          available: false,
          from: "12:00",
          to: "16:00",
        },
        evening: {
          available: false,
          from: "16:00",
          to: "20:00",
        },
      },
    },
    mode: "onChange", // Enable real-time validation
  });

  const onSubmit = (data: OnboardSessionFormData) => {
    const { availability } = data;
    console.log(availability);
    updateStudentOnboardData("timezone", data.timezone);
    updateStudentOnboardData(
      "availability_times",
      [
        availability.morning.available && {
          time: "morning",
          availability: {
            from: availability.morning.from,
            to: availability.morning.to,
          },
        },
        availability.afternoon.available && {
          time: "afternoon",
          availability: {
            from: availability.afternoon.from,
            to: availability.afternoon.to,
          },
        },
        availability.evening.available && {
          time: "evening",
          availability: {
            from: availability.evening.from,
            to: availability.evening.to,
          },
        },
      ].filter((slot) => slot !== false),
    );
    setCurrentStep(7);
  };

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <OnboardHeader showSkip={true} handleNext={() => setCurrentStep(7)} />

        <div className="flex flex-1 gap-8">
          <motion.div
            className="flex-1 hidden md:flex items-center"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <SVG1 />
          </motion.div>

          <motion.div
            className="flex-1 flex flex-col gap-8 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-2xl font-bold">
              {t(
                "student.steps.onboardsession.when_would_you_like_to_have_your_session",
              )}
            </h1>

            <form
              className="space-y-8"
              onSubmit={handleSubmit(onSubmit)}
              noValidate
            >
              <div className="space-y-3">
                <div className="flex flex-col gap-1 max-w-xl">
                  <label htmlFor="timezone">
                    {" "}
                    {t("student.steps.onboardsession.select_a_timezone")}{" "}
                  </label>
                  <select
                    {...register("timezone", {
                      required: t(
                        "student.steps.onboardsession.please_select_a_timezone",
                      ),
                    })}
                    id="timezone"
                    className={`text-xs w-full py-3 outline-none border ${
                      errors.timezone ? "border-red-500" : "border-dark/40"
                    } rounded-md px-4`}
                  >
                    {isFetchingTimezones ? (
                      <option value="">
                        {" "}
                        {t(
                          "student.steps.onboardsession.select_a_timeone1",
                        )}{" "}
                      </option>
                    ) : (
                      timezones?.data?.map((item: string) => (
                        <option value={item} key={item}>
                          {item}
                        </option>
                      ))
                    )}
                  </select>
                  {errors.timezone && (
                    <p className="text-red-500-500 text-sm mt-1">
                      {errors.timezone.message}
                    </p>
                  )}
                </div>
                {timeOfDay.map((item) => (
                  <TimeSlot
                    item={item}
                    key={item}
                    control={control}
                    register={register}
                  />
                ))}
              </div>
              <div className="flex justify-end">
                <LearnosoButton
                  action={() => {}}
                  title={t("student.steps.onboardsession.done")}
                  icon={null}
                  type="submit"
                  disabled={!isValid || Object.keys(errors).length > 0}
                />
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default OnboardSession;
