import { saveUser } from "@/app/services/auth";
import { useOnboardStudentMutation } from "@/app/services/student/student.service";
import { Spinner } from "@/components/atoms";
import { ErrorCard, ModalWrapper, SuccessCard } from "@/components/molecules";
import { useAuth } from "@/hooks";
import { RequestInterceptor } from "@/lib/api/interceptor";
import { motion } from "framer-motion";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { OnboardHeader } from "..";
import { useStudentOnboardContext } from "../../hooks";
import SVG2 from "../svg2";
import { t } from "i18next";

const OnboardFinal: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [
    submitStudentOnboardData,
    { isLoading: isSubmittingOnboardData, isSuccess },
  ] = useOnboardStudentMutation();
  const [response, setResponse] = useState<IResponse>();
  const { studentOnboardData } = useStudentOnboardContext();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useAuth();

  const submitOnboardData = async () => {
    setIsOpen(true);
    const response = await RequestInterceptor.handleRequest(
      () => submitStudentOnboardData(studentOnboardData).unwrap(),
      {},
      "Student Onboard",
    );
    setResponse(response);
    if (response.success) {
      dispatch(saveUser({ ...user, roles: [...user!.roles, "student"] }));
      setTimeout(() => navigate("/student/dashboard", { replace: true }), 5000);
    }
    console.log("Response", response);
  };

  useEffect(() => {
    submitOnboardData();
  }, []);

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <OnboardHeader showSkip={false} handleNext={() => {}} />

        <div className="flex flex-1 gap-8">
          <motion.div
            className="flex-1 hidden md:flex items-center justify-center"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <SVG2 />
          </motion.div>

          <motion.div
            className="flex-1 flex flex-col justify-center items-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h1 className="text-2xl font-bold text-primary">
              {t("student.steps.onboardfinal.lets_find_the_perfect_tutor")}
            </h1>
            <p> {t("student.steps.onboardfinal.hold_on")}</p>
          </motion.div>
        </div>
      </motion.div>

      <ModalWrapper
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        shouldStayOpenOnOverlayClicked
        opacity="bg-opacity-50"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.4 }}
        >
          {isSubmittingOnboardData ? (
            <Spinner variant="default" size="large" />
          ) : isSuccess ? (
            <SuccessCard
              message={response?.message}
              action={() => navigate("/student/dashboard", { replace: true })}
            />
          ) : (
            <ErrorCard message={response?.message} action={submitOnboardData} />
          )}
        </motion.div>
      </ModalWrapper>
    </section>
  );
};

export default OnboardFinal;
