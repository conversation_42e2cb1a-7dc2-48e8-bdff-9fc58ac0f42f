import { LearnosoButton } from "@/components/atoms/Button";
import { useAuth } from "@/hooks";
import { motion } from "framer-motion";
import React from "react";
import { OnboardHeader, SVG1 } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { t } from "i18next";

const OnboardGetStarted: React.FC = () => {
  const { handleNext, updateStudentOnboardData } = useStudentOnboardContext();
  const { user } = useAuth();

  const setUserIdAndGotoNextStep = () => {
    updateStudentOnboardData("user_id", user?.id as number);
    handleNext();
  };
  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <OnboardHeader />

        <div className="flex gap-8 flex-1">
          <motion.div
            className="flex-1 hidden md:flex items-center"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <SVG1 />
          </motion.div>

          <div className="flex-1 flex flex-col gap-8 items-center justify-center">
            <motion.h1
              className="text-2xl text-center font-bold"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {t("student.steps.onboardgetstarted.lets_get_your_preferences")}
              Experience
            </motion.h1>

            <LearnosoButton
              title={t("student.steps.onboardgetstarted.get_started")}
              action={setUserIdAndGotoNextStep}
              icon={null}
            />
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default OnboardGetStarted;
