import { LearnosoButton } from "@/components/atoms/Button";
import { currencies } from "@/lib/util";
import { motion } from "framer-motion";
import React from "react";
import { useForm } from "react-hook-form";
import { OnboardHeader, SVG1 } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { OnboardBudgetFormData } from "../../types";

const OnboardBudget: React.FC = () => {
  const { handleNext, updateStudentOnboardData } = useStudentOnboardContext();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OnboardBudgetFormData>();

  const onSubmit = (data: OnboardBudgetFormData) => {
    console.log(data);
    updateStudentOnboardData("budget", data.amount);
    updateStudentOnboardData("currency", data.currency);
    handleNext();
  };

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial="hidden"
        animate="visible"
        variants={{
          hidden: { opacity: 0 },
          visible: { opacity: 1, transition: { staggerChildren: 0.2 } },
        }}
      >
        <motion.div variants={fadeInUp}>
          <OnboardHeader showSkip={true} handleNext={handleNext} />
        </motion.div>

        <div className="flex flex-1 gap-8">
          <div className="flex-1 hidden md:flex items-center">
            <SVG1 />
          </div>

          <motion.div
            className="flex-1 flex flex-col gap-8 justify-center"
            variants={fadeInUp}
          >
            <motion.h1
              className="text-2xl font-bold"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              What’s your Budget per lesson
            </motion.h1>

            <motion.form
              className="space-y-16"
              onSubmit={handleSubmit(onSubmit)}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col gap-1.5">
                <label htmlFor="budget">Budget:</label>
                <div className="relative max-w-xl">
                  <div className="absolute left-2 text-xs top-1/2 -translate-y-1/2">
                    <select
                      {...register("currency", {
                        required: {
                          value: true,
                          message: "This field is required",
                        },
                      })}
                      id="language"
                      defaultValue=""
                      className="outline-none text-primary"
                    >
                      <option value="" className="capitalize">
                        Currency
                      </option>
                      {currencies.map((item) => (
                        <option value={item} key={item}>
                          {item}
                        </option>
                      ))}
                    </select>
                  </div>
                  <input
                    type="number"
                    placeholder="Amount"
                    id="amount"
                    className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4 pl-20 placeholder:text-gray-500"
                    {...register("amount", {
                      valueAsNumber: true,
                      required: {
                        value: true,
                        message: "This field is required",
                      },
                      validate: {
                        nonNegative: (fieldValue) => {
                          return (
                            fieldValue > 0 || "Field can't be negative or zero"
                          );
                        },
                      },
                    })}
                  />
                </div>
                {errors.amount && (
                  <motion.p
                    className="error"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                  >
                    Currency and Amount required
                  </motion.p>
                )}
              </div>
              <div className="flex justify-end">
                <LearnosoButton
                  action={() => {}}
                  title="Continue"
                  icon={null}
                  type="submit"
                />
              </div>
            </motion.form>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default OnboardBudget;
