import { LearnosoButton } from "@/components/atoms/Button";
import React from "react";
import { useForm } from "react-hook-form";
import { OnboardHeader, SVG1 } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { OnboardLevelFormData } from "../../types";
import { useTranslation } from "react-i18next";

const levels = ["Beginner", "Intermediate", "Advanced"];

const OnboardLevel: React.FC = () => {
  const { handleNext, studentOnboardData } = useStudentOnboardContext();

  const {
    register,
    formState: { errors },
    handleSubmit,
  } = useForm<OnboardLevelFormData>();

  const onSubmit = (data: OnboardLevelFormData) => {
    console.log(data);

    handleNext();
  };
  const { t } = useTranslation();

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <div className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8">
        <OnboardHeader showSkip handleNext={handleNext} />
        <div className="flex flex-1 gap-8">
          <div className="flex-1 hidden md:flex items-center">
            <SVG1 />
          </div>
          <div className="flex-1 flex flex-col gap-8 justify-center">
            <h1 className="text-2xl font-bold">
              {t("student.steps.onboardlevel.whats_your_level")}
              {studentOnboardData.prefered_language}?
            </h1>
            <form className="space-y-8" onSubmit={handleSubmit(onSubmit)}>
              <div className="space-y-2">
                {levels.map((level, index) => (
                  <label
                    className="flex gap-4 items-center text-xl"
                    key={index}
                  >
                    <input
                      type="radio"
                      {...register("level", {
                        required: t(
                          "student.steps.onboardlevel.please_select_your_level",
                        ),
                      })}
                      value={level}
                    />
                    <span>{level}</span>
                  </label>
                ))}
                {errors.level && (
                  <p className="error">{errors.level.message}</p>
                )}
              </div>
              <div className="flex justify-end">
                <LearnosoButton
                  action={() => {}}
                  title={t("student.steps.onboardlevel.continue")}
                  icon={null}
                  type="submit"
                />
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OnboardLevel;
