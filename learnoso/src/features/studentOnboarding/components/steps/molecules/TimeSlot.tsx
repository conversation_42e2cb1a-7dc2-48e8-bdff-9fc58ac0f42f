import { IOSSwitch } from "@/components/atoms/IOSSwitch";
import { OnboardSessionFormData } from "@/features/studentOnboarding/types";
import { FormControlLabel } from "@mui/material";
import React from "react";
import { Control, Controller, UseFormRegister } from "react-hook-form";

const TimeSlot: React.FC<{
  item: "morning" | "afternoon" | "evening";
  control: Control<OnboardSessionFormData, any>;
  register: UseFormRegister<OnboardSessionFormData>;
}> = ({ item, control, register }) => {
  return (
    <div className="flex gap-12 items-center">
      <div className="flex gap-4 items-center">
        <Controller
          name={`availability.${item}.available`}
          control={control}
          render={({ field: { onChange, value } }) => (
            <FormControlLabel
              control={<IOSSwitch checked={value} onChange={onChange} />}
              label=""
            />
          )}
        />
        <span className="capitalize">{item}</span>
      </div>
      <div className="flex gap-2">
        <div className="text-xs w-full outline-none border border-dark/40 rounded-md px-4 flex items-center gap-4 justify-between">
          <label htmlFor="morningFrom">From:</label>
          <input
            type="time"
            id="morningFrom"
            className="text-base py-3 outline-none"
            {...register(`availability.${item}.from`)}
          />
        </div>
        <div className="text-xs w-full outline-none border border-dark/40 rounded-md px-4 flex items-center gap-4 justify-between">
          <label htmlFor="morningTo">To:</label>
          <input
            type="time"
            id="morningTo"
            className="text-base py-3 outline-none"
            {...register(`availability.${item}.to`)}
          />
        </div>
      </div>
    </div>
  );
};

export default TimeSlot;
