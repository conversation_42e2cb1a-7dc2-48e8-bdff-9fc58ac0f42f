import { useFetchCoursesQuery } from "@/app/services/course/course.service";
import { Spinner } from "@/components/atoms";
import { LearnosoButton } from "@/components/atoms/Button";
import { Course } from "@/features/tutor/types";
import { motion } from "framer-motion";
import React from "react";
import { useForm } from "react-hook-form";
import { OnboardHeader, SVG1 } from "..";
import { useStudentOnboardContext } from "../../hooks";
import { OnboardSubjectFormData } from "../../types";
import { t } from "i18next";

const languages = [
  "English",
  "Spanish",
  "Mandarin",
  "Hindi",
  "Arabic",
  "Portuguese",
  "Russian",
  "Japanese",
  "German",
];

const OnboardSubject: React.FC = () => {
  const { handleNext, updateStudentOnboardData } = useStudentOnboardContext();
  const {
    data: courses,
    isLoading: isFetchingCourses,
    isError,
  } = useFetchCoursesQuery("");

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<OnboardSubjectFormData>();

  const onSubmit = (data: OnboardSubjectFormData) => {
    console.log(data);
    updateStudentOnboardData("courses_of_preference", [+data.subject]);
    updateStudentOnboardData("prefered_language", data.language);
    handleNext();
  };

  return (
    <section className="text-dark px-4 bg-[#FAFAFA]">
      <motion.div
        className="max-w-7xl min-h-screen mx-auto flex flex-col gap-8 py-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <OnboardHeader />

        <div className="flex flex-1 gap-8">
          <motion.div
            className="flex-1 hidden md:flex items-center"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <SVG1 />
          </motion.div>

          <motion.div
            className="flex-1 flex flex-col gap-8 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-2xl font-bold">
              {t("student.steps.onboardsubject.what_would_you_like_to_learn")}
            </h1>

            <form className="space-y-5" onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col gap-1.5">
                <label htmlFor="subject">Subject:</label>
                <select
                  {...register("subject", {
                    required: t(
                      "student.steps.onboardsubject.this_field_is_required",
                    ),
                  })}
                  id="subject"
                  defaultValue=""
                  className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                >
                  <option value="" className="capitalize">
                    {t(
                      "student.steps.onboardsubject.select_preferred_learning_subject",
                    )}
                  </option>
                  {isFetchingCourses ? (
                    <Spinner />
                  ) : isError ? (
                    <>
                      {" "}
                      {t("student.steps.onboardsubject.could_not_load_course")}
                    </>
                  ) : (
                    courses?.data?.map((course: Course, index: number) => (
                      <option
                        className="capitalize"
                        value={course.id}
                        key={course.id}
                      >
                        {course.name}
                      </option>
                    ))
                  )}
                </select>
                {errors.subject && (
                  <p className="error">{errors.subject.message}</p>
                )}
              </div>

              <div className="flex flex-col gap-1.5">
                <label htmlFor="language">
                  {" "}
                  {t("student.steps.onboardsubject.language")}
                </label>
                <select
                  {...register("language", {
                    required: t(
                      "student.steps.onboardsubject.this_field_is_required1",
                    ),
                  })}
                  id="language"
                  defaultValue=""
                  className="text-xs w-full py-3 outline-none border border-dark/40 rounded-md px-4"
                >
                  <option value="" className="capitalize">
                    {t(
                      "student.steps.onboardsubject.select_preferred_learning_language",
                    )}
                  </option>
                  {languages.map((language, index) => (
                    <option className="capitalize" value={language} key={index}>
                      {language}
                    </option>
                  ))}
                </select>
                {errors.language && (
                  <p className="error">{errors.language.message}</p>
                )}
              </div>

              <div className="flex justify-end">
                <LearnosoButton
                  title={t("student.steps.onboardsubject.continue")}
                  type="submit"
                />
              </div>
            </form>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default OnboardSubject;
