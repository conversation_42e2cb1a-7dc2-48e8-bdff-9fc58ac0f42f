import { studentApi } from "@/app/services/student/student.service";
import { configureStore, Store } from "@reduxjs/toolkit";
import { adminApi } from "./services/admin";
import { authApi, authReducer } from "./services/auth";
import { courseApi } from "./services/course/course.service";
import { lessonApi } from "./services/lesson/lesson.service";
import { paymentApi } from "./services/payment/payment.service";
import { reviewApi } from "./services/reviews/review.service";
import { tutorApi } from "./services/tutor/tutor.service";
import { utilApi } from "./services/util/util.service";

export const store: Store = configureStore({
  reducer: {
    auth: authReducer,
    [utilApi.reducerPath]: utilApi.reducer,
    [authApi.reducerPath]: authApi.reducer,
    [studentApi.reducerPath]: studentApi.reducer,
    [tutorApi.reducerPath]: tutorApi.reducer,
    [courseApi.reducerPath]: courseApi.reducer,
    [lessonApi.reducerPath]: lessonApi.reducer,
    [paymentApi.reducerPath]: paymentApi.reducer,
    [reviewApi.reducerPath]: reviewApi.reducer,
    [adminApi.reducerPath]: adminApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(utilApi.middleware)
      .concat(studentApi.middleware)
      .concat(tutorApi.middleware)
      .concat(authApi.middleware)
      .concat(courseApi.middleware)
      .concat(lessonApi.middleware)
      .concat(paymentApi.middleware)
      .concat(reviewApi.middleware)
      .concat(adminApi.middleware),
});
