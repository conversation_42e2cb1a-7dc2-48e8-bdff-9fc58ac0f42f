import { preparedHeaders } from "@/app/services/headers";
import { IStudentOnboardData } from "@/features/studentOnboarding/types";
import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const studentApi = createApi({
  reducerPath: "studentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    fetchTutors: builder.query({
      query: (query) => ({ url: `tutors?query=${query}` }),
    }),
    onboardStudent: builder.mutation({
      query: (body: IStudentOnboardData) => ({
        url: "student/onboarding",
        body,
        method: "POST",
      }),
    }),
    getStudents: builder.query<any, any>({
      query: () => ({ url: "/students" }),
    }),
  }),
});

export const {
  useFetchTutorsQuery,
  useOnboardStudentMutation,
  useGetStudentsQuery,
} = studentApi;
