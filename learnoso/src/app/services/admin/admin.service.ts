import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { preparedHeaders } from "../headers";

export const adminApi = createApi({
  reducerPath: "adminApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${BASE_URL}/admin`,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    getDashboardSummary: builder.query({
      query: () => ({ url: "dashboard-stats" }),
    }),
    // users/application-stats
    getTutorApplicationSummary: builder.query({
      query: () => ({ url: "users/application-stats" }),
    }),
    // users
    getUserSummary: builder.query({
      query: () => ({ url: "users/summary" }),
    }),
    getUsers: builder.query({
      query: (query: string) => ({
        url: `users?${query}`,
      }),
    }),

    // finance
    getFinanceSummary: builder.query({
      query: () => ({ url: "financial-summary" }),
    }),
    getAllTransactions: builder.query({
      query: (query: string) => ({
        url: `all-transactions?${query}`,
      }),
    }),

    getDailyTransactions: builder.query({
      query: () => ({
        url: `daily-transactions`,
      }),
    }),

    getMonthlyTransactions: builder.query({
      query: () => ({
        url: `monthly-transactions`,
      }),
    }),

    // tutor
    approveTutor: builder.mutation({
      query: ({ id, reason }: { id: number; reason?: string }) => ({
        url: "tutor-profile/approve",
        method: "POST",
        body: { id },
      }),
    }),
    rejectTutor: builder.mutation({
      query: ({ id, reason }: { id: number; reason?: string }) => ({
        url: "tutor-profile/reject",
        method: "POST",
        body: { id, reason },
      }),
    }),
  }),
});

export const {
  useApproveTutorMutation,
  useGetFinanceSummaryQuery,
  useGetAllTransactionsQuery,
  useGetUserSummaryQuery,
  useGetUsersQuery,
  useRejectTutorMutation,
  useGetDashboardSummaryQuery,
  useGetDailyTransactionsQuery,
  useGetMonthlyTransactionsQuery,
  useGetTutorApplicationSummaryQuery,
} = adminApi;
