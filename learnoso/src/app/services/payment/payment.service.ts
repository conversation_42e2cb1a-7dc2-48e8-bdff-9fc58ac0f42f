import { preparedHeaders } from "@/app/services/headers";
import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const paymentApi = createApi({
  reducerPath: "paymentApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${BASE_URL}`,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    initiatePayment: builder.mutation({
      query: (body: InitPayment) => ({
        url: "payments/initiate",
        body,
        method: "POST",
      }),
    }),
    getWalletBalance: builder.query({
      query: () => ({ url: `wallet` }),
    }),
    getTransactions: builder.query({
      query: () => ({ url: `wallet/transactions` }),
    }),
  }),
});

export const {
  useInitiatePaymentMutation,
  useGetWalletBalanceQuery,
  useGetTransactionsQuery,
} = paymentApi;
