import apiService, { ApiResponse, PaginatedResponse } from '@/lib/api/api.service';

// Payment Types
export interface PaymentMethod {
  id: string;
  type: 'stripe' | 'paypal';
  last_four?: string;
  brand?: string;
  exp_month?: number;
  exp_year?: number;
  is_default: boolean;
}

export interface Transaction {
  id: number;
  user_id: number;
  type: 'topup' | 'withdrawal' | 'lesson_payment' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  payment_method: string;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface WalletBalance {
  balance: number;
  currency: string;
  pending_balance: number;
}

export interface TopupRequest {
  amount: number;
  currency: string;
  payment_method: 'stripe' | 'paypal';
  return_url?: string;
  cancel_url?: string;
}

export interface TopupResponse {
  payment_intent_id?: string;
  client_secret?: string;
  approval_url?: string;
  transaction_id: number;
}

export interface WithdrawalRequest {
  amount: number;
  currency: string;
  withdrawal_method: 'bank_transfer' | 'paypal';
  account_details: Record<string, any>;
}

export interface CurrencyRate {
  from: string;
  to: string;
  rate: number;
  updated_at: string;
}

export interface CommissionSettings {
  tutor_commission_rate: number;
  platform_commission_rate: number;
  minimum_withdrawal_amount: number;
  withdrawal_fee: number;
}

export const enhancedPaymentService = {
  // Wallet Management
  async getWalletBalance(): Promise<ApiResponse<WalletBalance>> {
    return apiService.get<WalletBalance>('/wallet');
  },

  async getTransactions(page = 1, perPage = 15): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    return apiService.get<PaginatedResponse<Transaction>>('/wallet/transactions', {
      params: { page, per_page: perPage }
    });
  },

  // Top-up / Deposits
  async initiateTopup(data: TopupRequest): Promise<ApiResponse<TopupResponse>> {
    return apiService.post<TopupResponse>('/payments/initiate', data);
  },

  async confirmStripePayment(paymentIntentId: string): Promise<ApiResponse> {
    return apiService.post('/payments/stripe/confirm', { payment_intent_id: paymentIntentId });
  },

  async handlePayPalCallback(paymentId: string, payerId: string): Promise<ApiResponse> {
    return apiService.post('/paypal/callback', { payment_id: paymentId, payer_id: payerId });
  },

  // Withdrawals
  async requestWithdrawal(data: WithdrawalRequest): Promise<ApiResponse> {
    return apiService.post('/wallet/withdraw', data);
  },

  async getWithdrawalHistory(page = 1): Promise<ApiResponse<PaginatedResponse<Transaction>>> {
    return apiService.get<PaginatedResponse<Transaction>>('/wallet/withdrawals', {
      params: { page }
    });
  },

  // Currency Management
  async getAllCurrencies(): Promise<ApiResponse<string[]>> {
    return apiService.get<string[]>('/currency/all');
  },

  async convertCurrency(amount: number, from: string, to: string): Promise<ApiResponse<{ converted_amount: number; rate: number }>> {
    return apiService.post('/currency/convert', { amount, from, to });
  },

  async getExchangeRate(from: string, to: string): Promise<ApiResponse<CurrencyRate>> {
    return apiService.post<CurrencyRate>('/currency/rate', { from, to });
  },

  // Commission Settings
  async getCommissionSettings(): Promise<ApiResponse<CommissionSettings>> {
    return apiService.get<CommissionSettings>('/settings/commission');
  },

  // Payment Methods (if implemented)
  async getPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    return apiService.get<PaymentMethod[]>('/payment-methods');
  },

  async addPaymentMethod(data: { type: 'stripe' | 'paypal'; token: string }): Promise<ApiResponse<PaymentMethod>> {
    return apiService.post<PaymentMethod>('/payment-methods', data);
  },

  async removePaymentMethod(methodId: string): Promise<ApiResponse> {
    return apiService.delete(`/payment-methods/${methodId}`);
  },

  async setDefaultPaymentMethod(methodId: string): Promise<ApiResponse> {
    return apiService.post(`/payment-methods/${methodId}/set-default`);
  },

  // Lesson Payments
  async payForLesson(lessonId: number, paymentMethodId?: string): Promise<ApiResponse> {
    return apiService.post('/lessons/pay', { lesson_id: lessonId, payment_method_id: paymentMethodId });
  },

  async refundLesson(lessonId: number, reason: string): Promise<ApiResponse> {
    return apiService.post('/lessons/refund', { lesson_id: lessonId, reason });
  },

  // Subscription Management (if implemented)
  async getSubscriptionPlans(): Promise<ApiResponse<any[]>> {
    return apiService.get('/subscription/plans');
  },

  async subscribe(planId: string, paymentMethodId: string): Promise<ApiResponse> {
    return apiService.post('/subscription/subscribe', { plan_id: planId, payment_method_id: paymentMethodId });
  },

  async cancelSubscription(): Promise<ApiResponse> {
    return apiService.post('/subscription/cancel');
  },

  async getSubscriptionStatus(): Promise<ApiResponse<any>> {
    return apiService.get('/subscription/status');
  },

  // Financial Reports (for tutors)
  async getEarningsReport(startDate: string, endDate: string): Promise<ApiResponse<any>> {
    return apiService.get('/reports/earnings', {
      params: { start_date: startDate, end_date: endDate }
    });
  },

  async getPayoutHistory(page = 1): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiService.get('/reports/payouts', { params: { page } });
  }
};
