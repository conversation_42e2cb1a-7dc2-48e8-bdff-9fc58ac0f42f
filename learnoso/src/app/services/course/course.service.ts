import { preparedHeaders } from "@/app/services/headers";
import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const courseApi = createApi({
  reducerPath: "courseApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    fetchCourses: builder.query({
      query: (query) => ({ url: `courses?query=${query}` }),
    }),
  }),
});

export const { useFetchCoursesQuery } = courseApi;
