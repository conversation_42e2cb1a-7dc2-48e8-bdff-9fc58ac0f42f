import { preparedHeaders } from "@/app/services/headers";
import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { ILesson, ImeetingMetadata } from "./interface";
export const lessonApi = createApi({
  reducerPath: "lessonApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${BASE_URL}/lessons`,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    scheduleLesson: builder.mutation({
      query: (body: ILesson) => ({ url: `schedule`, method: "POST", body }),
    }),
    getStudentLessons: builder.query({
      query: () => ({ url: "student-lessons", method: "GET" }),
    }),
    getTutorLessons: builder.query({
      query: () => ({ url: "tutor-lessons", method: "GET" }),
    }),

    UpdateMeetingMetadata: builder.mutation({
      query: (body: ImeetingMetadata) => ({
        url: `/${body.lessonId}/meeting-metadata`,
        method: "PUT",
        body: body.meetingStats,
      }),
    }),
  }),
});

export const {
  useScheduleLessonMutation,
  useGetStudentLessonsQuery,
  useGetTutorLessonsQuery,
  useUpdateMeetingMetadataMutation,
} = lessonApi;
