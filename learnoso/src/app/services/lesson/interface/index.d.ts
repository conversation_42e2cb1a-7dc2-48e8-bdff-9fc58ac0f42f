interface ILesson {
  tutor_id: number;
  student_id: number;
  title: string;
  description?: string;
  course_id: number;
  starts_at: string;
  ends_at: string;
  timezone: string;
}


interface IUpdateMeeting {
  meeting: {
    endTime: string;
    status: string;
  };
  host: {
    id: string;
    name: string;
    email: string;
  };
  duration: {
    total: number;
    active: number;
    paused: number;
  };
  participants: {
    total: number;
    list: any[];
  };
  activity: {
    pauses: {
      count: number;
      totalDuration: number;
    };
    screenShare: boolean;
    videoMuted: boolean;
    audioMuted: boolean;
  };
}

export interface ImeetingMetadata {
  meetingStats: Partial<IUpdateMeeting>;
  lessonId: string;
}
