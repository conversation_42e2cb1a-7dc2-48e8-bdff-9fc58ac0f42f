import apiService, { ApiResponse, PaginatedResponse } from '@/lib/api/api.service';

// Lesson Types
export interface Lesson {
  id: number;
  tutor_id: number;
  student_id: number;
  course_id: number;
  title: string;
  description: string;
  scheduled_at: string;
  duration: number; // in minutes
  price: number;
  currency: string;
  status: 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  meeting_url?: string;
  agora_channel?: string;
  agora_token?: string;
  created_at: string;
  updated_at: string;
  tutor?: {
    id: number;
    first_name: string;
    last_name: string;
    profile_image?: string;
  };
  student?: {
    id: number;
    first_name: string;
    last_name: string;
    profile_image?: string;
  };
  course?: {
    id: number;
    name: string;
    language: string;
  };
}

export interface ScheduleLessonRequest {
  tutor_id: number;
  course_id: number;
  scheduled_at: string;
  duration: number;
  notes?: string;
}

export interface LessonSession {
  id: string;
  lesson_id: number;
  started_at: string;
  ended_at?: string;
  duration: number;
  status: 'active' | 'paused' | 'completed';
  break_time: number;
  metadata?: Record<string, any>;
}

export interface LessonStatistics {
  total_watch_time: number;
  completion_rate: number;
  average_session_duration: number;
  pause_count: number;
  resume_count: number;
  sessions: LessonSession[];
}

export interface AgoraToken {
  token: string;
  channel: string;
  uid: number;
  expires_at: string;
}

export const enhancedLessonService = {
  // Lesson Management
  async scheduleLesson(data: ScheduleLessonRequest): Promise<ApiResponse<Lesson>> {
    return apiService.post<Lesson>('/lessons/schedule', data);
  },

  async confirmLesson(lessonId: number): Promise<ApiResponse<Lesson>> {
    return apiService.post<Lesson>('/lessons/confirm', { lesson_id: lessonId });
  },

  async cancelLesson(lessonId: number, reason: string): Promise<ApiResponse> {
    return apiService.post('/lessons/cancel', { lesson_id: lessonId, reason });
  },

  async rescheduleLesson(lessonId: number, newDateTime: string): Promise<ApiResponse<Lesson>> {
    return apiService.post<Lesson>('/lessons/reschedule', { 
      lesson_id: lessonId, 
      scheduled_at: newDateTime 
    });
  },

  // Get Lessons
  async getTutorLessons(page = 1, status?: string): Promise<ApiResponse<PaginatedResponse<Lesson>>> {
    return apiService.get<PaginatedResponse<Lesson>>('/lessons/tutor-lessons', {
      params: { page, status }
    });
  },

  async getStudentLessons(page = 1, status?: string): Promise<ApiResponse<PaginatedResponse<Lesson>>> {
    return apiService.get<PaginatedResponse<Lesson>>('/lessons/student-lessons', {
      params: { page, status }
    });
  },

  async getLesson(lessonId: number): Promise<ApiResponse<Lesson>> {
    return apiService.get<Lesson>(`/lessons/${lessonId}`);
  },

  // Lesson Tracking
  async initializeTracker(lessonId: number): Promise<ApiResponse<LessonSession>> {
    return apiService.post<LessonSession>('/lessons/tracker/initialize', { lesson_id: lessonId });
  },

  async startSession(lessonId: number): Promise<ApiResponse<LessonSession>> {
    return apiService.post<LessonSession>('/lessons/tracker/start', { lesson_id: lessonId });
  },

  async endSession(lessonId: number): Promise<ApiResponse<LessonSession>> {
    return apiService.post<LessonSession>('/lessons/tracker/end', { lesson_id: lessonId });
  },

  async startBreak(lessonId: number): Promise<ApiResponse> {
    return apiService.post('/lessons/tracker/break/start', { lesson_id: lessonId });
  },

  async endBreak(lessonId: number): Promise<ApiResponse> {
    return apiService.post('/lessons/tracker/break/end', { lesson_id: lessonId });
  },

  async getSessionStatus(lessonId: number): Promise<ApiResponse<LessonSession>> {
    return apiService.get<LessonSession>(`/lessons/tracker/status/${lessonId}`);
  },

  async getLessonStatistics(lessonId: number): Promise<ApiResponse<LessonStatistics>> {
    return apiService.get<LessonStatistics>(`/lessons/tracker/statistics/${lessonId}`);
  },

  async getSessionHistory(lessonId: number): Promise<ApiResponse<LessonSession[]>> {
    return apiService.get<LessonSession[]>(`/lessons/tracker/history/${lessonId}`);
  },

  async getActiveSessions(): Promise<ApiResponse<LessonSession[]>> {
    return apiService.get<LessonSession[]>('/lessons/tracker/active');
  },

  async updateSessionMetadata(lessonId: number, metadata: Record<string, any>): Promise<ApiResponse> {
    return apiService.post('/lessons/tracker/metadata', { lesson_id: lessonId, metadata });
  },

  // Agora Integration
  async generateAgoraToken(lessonId: number): Promise<ApiResponse<AgoraToken>> {
    return apiService.post<AgoraToken>('/lessons/regenerate-token', { lesson_id: lessonId });
  },

  async generateAgoraTokenByChannel(channel: string): Promise<ApiResponse<AgoraToken>> {
    return apiService.post<AgoraToken>('/agora/token', { channel });
  },

  async getSessionParticipants(lessonId: number): Promise<ApiResponse<any[]>> {
    return apiService.get(`/agora/session/${lessonId}/participants`);
  },

  // Meeting Metadata
  async updateMeetingMetadata(lessonId: number, metadata: Record<string, any>): Promise<ApiResponse> {
    return apiService.put(`/lessons/${lessonId}/meeting-metadata`, metadata);
  },

  // Lesson Reviews and Feedback
  async submitLessonReview(lessonId: number, rating: number, comment: string): Promise<ApiResponse> {
    return apiService.post('/lessons/review', {
      lesson_id: lessonId,
      rating,
      comment
    });
  },

  async getLessonReviews(lessonId: number): Promise<ApiResponse<any[]>> {
    return apiService.get(`/lessons/${lessonId}/reviews`);
  },

  // Lesson Materials
  async uploadLessonMaterial(lessonId: number, file: File, description?: string): Promise<ApiResponse> {
    return apiService.uploadFile(`/lessons/${lessonId}/materials`, file, { description });
  },

  async getLessonMaterials(lessonId: number): Promise<ApiResponse<any[]>> {
    return apiService.get(`/lessons/${lessonId}/materials`);
  },

  async deleteLessonMaterial(lessonId: number, materialId: number): Promise<ApiResponse> {
    return apiService.delete(`/lessons/${lessonId}/materials/${materialId}`);
  }
};
