interface ITutorCourse {
  user_id: number;
  course_ids: number[];
}

interface ITutorDescription {
  user_id: number;
  description: string;
  motivation: string;
  languages: number[];
  primary_language_id: number;
}

interface ITutorEducationCertification {
  subject: string;
  user_id: number;
  description: string;
  institution: string;
  start_date: string;
  end_date: string;
  attachment: File;
  certificate: string;
}

interface ITutorProfileAndVideo {
  profile_picture: File;
  video_url: string;
  user_id: number;
}

interface Availability {
  time: string;
  availability: { from: string; to: string };
  day_of_the_week: string;
}

interface ITutorPriceAvailability {
  user_id: number;
  currency: string;
  price: number;
  availability: Availability[];
}

interface ITutorProfile {
  user_id: number;
}
