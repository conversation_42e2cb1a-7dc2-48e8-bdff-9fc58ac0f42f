import { preparedHeaders } from "@/app/services/headers";
import { BASE_URL } from "@/lib/config";
import { FormWizard } from "@/lib/util";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const tutorApi = createApi({
  reducerPath: "tutorApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${BASE_URL}/tutor`,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    onboardTutorCourse: builder.mutation({
      query: (body: ITutorCourse) => ({
        url: "onboarding/tutor-courses",
        body,
        method: "POST",
      }),
    }),
    onboardTutorDescription: builder.mutation({
      query: (body: ITutorDescription) => ({
        url: "onboarding/description-and-motivation",
        body,
        method: "POST",
      }),
    }),
    onboardTutorEducationCertification: builder.mutation({
      query: (body: FormData) =>
        FormWizard.postFormData("onboarding/education-and-certification", body),
    }),
    onboardTutorProfileAndVideo: builder.mutation({
      query: (body: FormData) =>
        FormWizard.postFormData("onboarding/tutor-profile-url", body),
    }),
    onboardTutorPriceAvailability: builder.mutation({
      query: (body: ITutorPriceAvailability) => ({
        url: "onboarding/price-availability",
        body,
        method: "POST",
      }),
    }),
    getTutorProfile: builder.mutation({
      query: (body: ITutorProfile) => ({
        url: `onboarding/get-all`,
        body,
        method: "POST",
      }),
    }),
    getTutorCourses: builder.query<any, number>({
      query: (id) => ({ url: `${id}/courses` }),
    }),
    getTutorById: builder.query({
      query: (id) => ({ url: `${id}` }),
    }),
    getTutorStats: builder.query({
      query: (id) => ({ url: `${id}/stats` }),
    }),
    getAllTutors: builder.query({
      query: (query) => ({ url: `?${query}` }),
    }),
  }),
});

export const {
  useOnboardTutorCourseMutation,
  useOnboardTutorDescriptionMutation,
  useOnboardTutorEducationCertificationMutation,
  useOnboardTutorProfileAndVideoMutation,
  useOnboardTutorPriceAvailabilityMutation,
  useGetTutorProfileMutation,
  useGetTutorCoursesQuery,
  useGetAllTutorsQuery,
  useGetTutorByIdQuery,
  useGetTutorStatsQuery,
} = tutorApi;
