import apiService, { ApiResponse, PaginatedResponse } from '@/lib/api/api.service';

// Chat Types
export interface Conversation {
  id: number;
  participants: User[];
  last_message?: Message;
  unread_count: number;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  sender_id: number;
  content: string;
  type: 'text' | 'file' | 'image' | 'lesson_link';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  lesson_id?: number;
  is_read: boolean;
  created_at: string;
  sender?: {
    id: number;
    first_name: string;
    last_name: string;
    profile_image?: string;
  };
}

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  profile_image?: string;
  is_online: boolean;
  last_seen?: string;
}

export interface SendMessageRequest {
  conversation_id: number;
  content: string;
  type?: 'text' | 'file' | 'image' | 'lesson_link';
  lesson_id?: number;
}

export interface StartConversationRequest {
  participant_id: number;
  initial_message?: string;
}

// WebSocket Manager for Real-time Chat
export class ChatWebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private messageHandlers: Map<string, Function[]> = new Map();

  constructor(private token: string) {
    this.connect();
  }

  private connect() {
    const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:6001'}/app/chat?token=${this.token}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventListeners();
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.handleReconnect();
    }
  }

  private setupEventListeners() {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('Chat WebSocket connected');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('Chat WebSocket disconnected');
      this.handleReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('Chat WebSocket error:', error);
    };
  }

  private handleMessage(data: any) {
    const { event, payload } = data;
    const handlers = this.messageHandlers.get(event) || [];
    handlers.forEach(handler => handler(payload));
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  public on(event: string, handler: Function) {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, []);
    }
    this.messageHandlers.get(event)!.push(handler);
  }

  public off(event: string, handler: Function) {
    const handlers = this.messageHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  public send(event: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ event, data }));
    }
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.messageHandlers.clear();
  }
}

export const chatService = {
  // Conversation Management
  async getConversations(page = 1): Promise<ApiResponse<PaginatedResponse<Conversation>>> {
    return apiService.get<PaginatedResponse<Conversation>>('/chat/conversations', {
      params: { page }
    });
  },

  async startConversation(data: StartConversationRequest): Promise<ApiResponse<Conversation>> {
    return apiService.post<Conversation>('/chat/conversations/start', data);
  },

  async markAsRead(conversationId: number): Promise<ApiResponse> {
    return apiService.post(`/chat/conversations/${conversationId}/mark-read`);
  },

  // Messaging
  async sendMessage(data: SendMessageRequest): Promise<ApiResponse<Message>> {
    return apiService.post<Message>('/chat/messages/send', data);
  },

  async sendMessageWithFile(conversationId: number, file: File, message?: string): Promise<ApiResponse<Message>> {
    return apiService.uploadFile('/chat/messages/send-file', file, {
      conversation_id: conversationId,
      message
    });
  },

  async sendLessonLink(conversationId: number, lessonId: number, message?: string): Promise<ApiResponse<Message>> {
    return apiService.post<Message>('/chat/messages/send-lesson-link', {
      conversation_id: conversationId,
      lesson_id: lessonId,
      message
    });
  },

  async getMessages(conversationId: number, page = 1): Promise<ApiResponse<PaginatedResponse<Message>>> {
    return apiService.get<PaginatedResponse<Message>>(`/chat/conversations/${conversationId}/messages`, {
      params: { page }
    });
  },

  async searchMessages(conversationId: number, query: string): Promise<ApiResponse<Message[]>> {
    return apiService.get<Message[]>(`/chat/conversations/${conversationId}/search`, {
      params: { q: query }
    });
  },

  async deleteMessage(messageId: number): Promise<ApiResponse> {
    return apiService.delete(`/chat/messages/${messageId}`);
  },

  // Utilities
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    return apiService.get<{ count: number }>('/chat/unread-count');
  },

  async getOnlineUsers(): Promise<ApiResponse<User[]>> {
    return apiService.get<User[]>('/chat/online-users');
  },

  // Real-time features
  createWebSocketManager(token: string): ChatWebSocketManager {
    return new ChatWebSocketManager(token);
  }
};
