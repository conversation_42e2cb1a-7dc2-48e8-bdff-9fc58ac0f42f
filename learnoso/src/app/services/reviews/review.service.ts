import { preparedHeaders } from "@/app/services/headers";
import { BASE_URL } from "@/lib/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

export const reviewApi = createApi({
  reducerPath: "reviewApi",
  baseQuery: fetchBaseQuery({
    baseUrl: `${BASE_URL}/reviews`,
    prepareHeaders: preparedHeaders,
  }),
  tagTypes: ["Reviews"],
  endpoints: (builder) => ({
    postReview: builder.mutation({
      query: (body: IReview) => ({
        url: "/",
        body,
        method: "POST",
      }),
      invalidatesTags: [{ type: "Reviews", id: "LIST" }],
    }),
    getReviews: builder.query({
      query: () => ({
        url: "/",
        method: "GET",
      }),
      providesTags: [{ type: "Reviews", id: "LIST" }],
    }),
    getReviewById: builder.query({
      query: (id: number) => ({
        url: `/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [{ type: "Reviews", id }],
    }),
    getReviewsByTutorId: builder.query({
      query: (data: { tutorId: number; page: number }) => ({
        url: `/tutor/${data.tutorId}?page=${data.page}`,
        method: "GET",
      }),
      providesTags: (result, error, { tutorId }) => [
        { type: "Reviews", id: `TUTOR_${tutorId}` },
      ],
    }),
  }),
});

export const {
  usePostReviewMutation,
  useGetReviewsQuery,
  useGetReviewByIdQuery,
  useGetReviewsByTutorIdQuery,
} = reviewApi;
