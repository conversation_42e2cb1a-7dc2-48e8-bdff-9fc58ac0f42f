import apiService, { ApiResponse } from '@/lib/api/api.service';

// Enhanced types for auth responses
export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  email_verified_at: string | null;
  two_factor_enabled: boolean;
  roles: string[];
  profile_image?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  two_factor_code?: string;
}

export interface RegisterRequest {
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  password: string;
  password_confirmation: string;
}

export interface LoginResponse {
  token: string;
  user: User;
  requires_2fa?: boolean;
  user_id?: number;
}

export interface PasswordStrengthResponse {
  is_valid: boolean;
  requirements: {
    min_length: boolean;
    has_uppercase: boolean;
    has_lowercase: boolean;
    has_numbers: boolean;
    has_symbols: boolean;
  };
}

export interface TwoFactorSetupResponse {
  qr_code_url: string;
  manual_entry_key: string;
  recovery_codes: string[];
}

export interface AuthStatusResponse {
  authenticated: boolean;
  user?: User;
}

export const enhancedAuthService = {
  // Basic Authentication
  async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/login', data);
    if (response.success && response.data?.token) {
      apiService.setToken(response.data.token);
    }
    return response;
  },

  async register(data: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/register', data);
    if (response.success && response.data?.token) {
      apiService.setToken(response.data.token);
    }
    return response;
  },

  async logout(): Promise<ApiResponse> {
    const response = await apiService.post('/logout');
    apiService.setToken(null);
    return response;
  },

  async getAuthStatus(): Promise<ApiResponse<AuthStatusResponse>> {
    return apiService.get<AuthStatusResponse>('/auth-status');
  },

  // Password Management
  async checkPasswordStrength(password: string): Promise<ApiResponse<PasswordStrengthResponse>> {
    return apiService.post<PasswordStrengthResponse>('/password/check-strength', { password });
  },

  async getPasswordRequirements(): Promise<ApiResponse> {
    return apiService.get('/password/requirements');
  },

  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<ApiResponse> {
    return apiService.post('/change-password', {
      current_password: currentPassword,
      password: newPassword,
      password_confirmation: confirmPassword
    });
  },

  // Two-Factor Authentication
  async setup2FA(): Promise<ApiResponse<TwoFactorSetupResponse>> {
    return apiService.post<TwoFactorSetupResponse>('/2fa/setup');
  },

  async confirm2FA(code: string): Promise<ApiResponse> {
    return apiService.post('/2fa/confirm', { code });
  },

  async verify2FA(code: string): Promise<ApiResponse> {
    return apiService.post('/2fa/verify', { code });
  },

  async disable2FA(password: string): Promise<ApiResponse> {
    return apiService.post('/2fa/disable', { password });
  },

  async get2FAStatus(): Promise<ApiResponse<{ enabled: boolean; backup_codes_count: number }>> {
    return apiService.get('/2fa/status');
  },

  async getRecoveryCodes(): Promise<ApiResponse<{ recovery_codes: string[] }>> {
    return apiService.get('/2fa/recovery-codes');
  },

  async regenerateRecoveryCodes(): Promise<ApiResponse<{ recovery_codes: string[] }>> {
    return apiService.post('/2fa/recovery-codes/regenerate');
  },

  // Email Verification
  async resendEmailVerification(): Promise<ApiResponse> {
    return apiService.post('/email/verification/resend');
  },

  // Profile Management
  async getProfile(): Promise<ApiResponse<User>> {
    return apiService.get<User>('/profile');
  },

  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return apiService.put<User>('/profile', data);
  },

  async updateProfileImage(file: File): Promise<ApiResponse<{ profile_image_url: string }>> {
    return apiService.uploadFile('/profile-image', file);
  },

  async switchRole(role: 'student' | 'tutor'): Promise<ApiResponse> {
    return apiService.post('/role/switch', { role });
  }
};
