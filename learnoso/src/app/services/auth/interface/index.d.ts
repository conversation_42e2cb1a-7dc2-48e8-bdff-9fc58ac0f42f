interface ILogin {
  email: string;
  password: string;
}

interface IRegister {
  first_name: string;
  last_name: string;
  email: string;
  country: string;
  password: string;
}

interface IResetPassword {
  email: string;
}

interface IVerifyEmail {
  token: string;
}

interface IUpdatePassword {
  old_password: string;
  new_password: string;
  confirm_password: string;
}

type UserRole = "student" | "tutor" | "admin";
interface IUser {
  id: number;
  first_name: string;
  last_name: string;
  name: string | null;
  email: string;
  email_verified_at: string;
  created_at: string;
  updated_at: string;
  roles: UserRole[];
  bio?: string;
  image?: string;
  current_role: UserRole | null;
  student?: {
    id: number;
    user_id: number;
  };
  tutor?: {
    id: number;
    user_id: number;
  };
}

interface IAuthUser {
  token: string | null;
  user: IUser | null;
  sessionId?: string | null;
  sessionExpireAt?: number | null;
}

interface UpdateStudentProfile {
  first_name: string;
  last_name: string;
  phone: string;
  country: string;
}

interface changePassword {
  old_password: string;
  new_password: string;
  new_password_confirmation: string;
}
