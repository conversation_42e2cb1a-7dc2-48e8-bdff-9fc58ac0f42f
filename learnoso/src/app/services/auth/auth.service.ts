import { BASE_URL } from "@/lib/config";
import { FormWizard, LocalStorage, SESSION_EXPIRE_TIME } from "@/lib/util";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { v4 as uuidv4 } from "uuid";
import { preparedHeaders } from "../headers";

// Initial state
const initialState: IAuthUser = {
  token: LocalStorage.load("learnoso-user-auth-token"),
  user: LocalStorage.load("learnoso-user-info"),
  sessionId: LocalStorage.load("learnoso-user-session-id"),
  sessionExpireAt: LocalStorage.load("learnoso-user-session-expire-at"),
};

const authApi = createApi({
  reducerPath: "authApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: preparedHeaders,
  }),
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (body: ILogin) => ({ url: `login`, method: "POST", body }),
    }),
    register: builder.mutation({
      query: (body: IRegister) => ({ url: `register`, method: "POST", body }),
    }),
    updateProfile: builder.mutation({
      query: (body: UpdateStudentProfile) => ({
        url: "profile",
        method: "PUT",
        body,
      }),
    }),
    updateProfileImage: builder.mutation({
      query: (body: FormData) => FormWizard.postFormData("profile-image", body),
    }),
    changePassword: builder.mutation({
      query: (body: changePassword) => ({
        url: "change-password",
        method: "POST",
        body,
      }),
    }),
  }),
});

// Slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    saveUser: (state, { payload }: PayloadAction<any>) => {
      try {
        const sessionId = uuidv4();
        const sessionExpiry = Date.now() + SESSION_EXPIRE_TIME;

        const token = payload.token as unknown as string;
        const user = {
          ...payload.user,
          tutor: payload.tutor,
          student: payload.student,
        };
        state.token = token;
        state.user = user;
        state.sessionId = sessionId;
        state.sessionExpireAt = sessionExpiry;
        LocalStorage.save("learnoso-user-auth-token", token);
        LocalStorage.save("learnoso-user-info", user);
        LocalStorage.save("learnoso-user-session-id", sessionId);
        LocalStorage.save("learnoso-user-session-expire-at", sessionExpiry);
      } catch (error) {
        console.error("Error saving Learnoso session", error);
      }
    },
    logOutUser: (state) => {
      try {
        state.token = null;
        state.user = null;
        state.sessionId = null;
        state.sessionExpireAt = null;
        LocalStorage.remove("learnoso-user-auth-token");
        LocalStorage.remove("learnoso-user-info");
        LocalStorage.remove("learnoso-user-session-id");
        LocalStorage.remove("learnoso-user-session-expire-at");
        LocalStorage.remove("learnoso-sessionId");
        LocalStorage.remove("learnoso-sessionExpiry");
      } catch (error) {
        console.error("Error removing Learnoso session", error);
      }
    },
  },
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useUpdateProfileMutation,
  useUpdateProfileImageMutation,
  useChangePasswordMutation,
} = authApi;

export const { saveUser, logOutUser } = authSlice.actions;

export const authReducer = authSlice.reducer;

export { authApi };
