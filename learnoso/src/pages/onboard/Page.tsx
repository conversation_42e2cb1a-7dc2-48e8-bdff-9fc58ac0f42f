import AuthGaurd from "@/features/auth/components/AuthGaurd";
import { useAuth } from "@/hooks";
import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import { FaChalkboardTeacher, FaUserGraduate } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

const SelectOnboardType = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const userHasRole = (role: UserRole) => user?.roles.includes(role);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const handleRoleSelect = (role: UserRole) => {
    navigate(`/onboard/${role}`, {
      replace: true,
    });
  };

  const { t } = useTranslation();

  return (
    <AuthGaurd>
      {" "}
      <div className="min-h-screen bg-slate-200 flex items-center justify-center p-4">
        <div className="max-w-4xl w-full">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-primary mb-2">
              {t("onboard.title")}
            </h1>
            <p className="text-dark">{t("onboard.select_platform")}</p>
          </div>

          <motion.div
            className="grid md:grid-cols-2 gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <motion.div
              variants={cardVariants}
              whileHover="hover"
              onClick={() => {
                if (userHasRole("student")) {
                  toast.warn("You are already a student");
                  return;
                } else {
                  handleRoleSelect("student");
                }
              }}
              className="cursor-pointer"
            >
              <div
                className={`bg-white hover:bg-opacity-80 rounded-lg shadow-lg p-6 h-full ${userHasRole("student") ? "cursor-not-allowed" : ""}`}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <FaUserGraduate className="w-8 h-8 text-blue-600" />
                  </div>
                  <h2 className="text-xl font-semibold text-dark mb-2">
                    {t("onboard.student")}
                  </h2>
                  <p className="text-gray-500">{t("onboard.jion_student")}</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              variants={cardVariants}
              whileHover="hover"
              onClick={() => {
                if (userHasRole("tutor")) {
                  toast.warn("You are already a tutor");
                  return;
                } else {
                  handleRoleSelect("tutor");
                }
              }}
              className="cursor-pointer"
            >
              <div
                className={`bg-white hover:bg-opacity-80 rounded-lg shadow-lg p-6 h-full ${userHasRole("tutor") ? "cursor-not-allowed" : ""}`}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-green-500/10 rounded-full flex items-center justify-center mb-4">
                    <FaChalkboardTeacher className="w-8 h-8 text-green-500" />
                  </div>
                  <h2 className="text-xl font-semibold text-dark mb-2">
                    {t("onboard.tutor")}
                  </h2>
                  <p className="text-gray-500">{t("onboard.jion_tutor")}</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </AuthGaurd>
  );
};

export default SelectOnboardType;
