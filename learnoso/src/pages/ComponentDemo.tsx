import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { Star, Heart, Settings } from 'lucide-react';

export const ComponentDemo: React.FC = () => {
  const [progress, setProgress] = useState(65);
  const { success, error, warning, info } = useToast();

  const handleToastDemo = (type: 'success' | 'error' | 'warning' | 'info') => {
    const messages = {
      success: 'Operation completed successfully!',
      error: 'Something went wrong. Please try again.',
      warning: 'Please check your input and try again.',
      info: 'Here\'s some helpful information.',
    };
    
    switch (type) {
      case 'success':
        success(messages.success);
        break;
      case 'error':
        error(messages.error);
        break;
      case 'warning':
        warning(messages.warning);
        break;
      case 'info':
        info(messages.info);
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            LearnOso Modern Components
          </h1>
          <p className="text-xl text-gray-600">
            Testing the new modern UI components
          </p>
        </div>

        {/* Buttons Section */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>Various button styles and states</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="default">Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="success">Success</Button>
              <Button variant="warning">Warning</Button>
              <Button loading>Loading</Button>
              <Button leftIcon={<Star className="h-4 w-4" />}>With Icon</Button>
              <Button rightIcon={<Heart className="h-4 w-4" />}>Right Icon</Button>
              <Button size="sm">Small</Button>
              <Button size="lg">Large</Button>
            </div>
          </CardContent>
        </Card>

        {/* Form Components */}
        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
            <CardDescription>Input fields and form elements</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input 
                  label="Email Address" 
                  placeholder="Enter your email"
                  leftIcon={<Settings className="h-4 w-4" />}
                />
                <Input 
                  label="Password" 
                  type="password"
                  placeholder="Enter your password"
                  helperText="Must be at least 8 characters"
                />
                <Input 
                  label="Error Example" 
                  placeholder="This field has an error"
                  error="This field is required"
                />
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Progress Bar</label>
                  <Progress value={progress} className="mb-2" />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={() => setProgress(Math.max(0, progress - 10))}>
                      Decrease
                    </Button>
                    <Button size="sm" onClick={() => setProgress(Math.min(100, progress + 10))}>
                      Increase
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Badges</label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="default">Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="success">Success</Badge>
                    <Badge variant="warning">Warning</Badge>
                    <Badge variant="destructive">Error</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Toast Demo */}
        <Card>
          <CardHeader>
            <CardTitle>Toast Notifications</CardTitle>
            <CardDescription>Click buttons to test toast notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button onClick={() => handleToastDemo('success')}>
                Success Toast
              </Button>
              <Button onClick={() => handleToastDemo('error')}>
                Error Toast
              </Button>
              <Button onClick={() => handleToastDemo('warning')}>
                Warning Toast
              </Button>
              <Button onClick={() => handleToastDemo('info')}>
                Info Toast
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
