import { motion, useAnimation } from "framer-motion";
import { t } from "i18next";
import React, { useEffect, useState } from "react";
import { FaBook, FaGraduationCap, FaTools, FaUsers } from "react-icons/fa";
import { useInView } from "react-intersection-observer";

interface AnimatedCounterProps {
  from: number;
  to: number;
  duration?: number;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  from,
  to,
  duration = 2,
}) => {
  const [count, setCount] = useState(from);
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  useEffect(() => {
    if (inView) {
      controls.start({ opacity: 1, scale: 1, transition: { duration: 0.5 } });

      let start = from;
      const end = to;
      const totalDuration = duration * 1000; // Convert duration to milliseconds
      const stepTime = 50; // Time per step
      const steps = Math.ceil(totalDuration / stepTime);
      const increment = (end - start) / steps;

      const interval = setInterval(() => {
        start += increment;
        if (start >= end) {
          setCount(end);
          clearInterval(interval);
        } else {
          setCount(Math.floor(start));
        }
      }, stepTime);
    }
  }, [controls, inView, from, to, duration]);

  return (
    <motion.h3
      ref={ref}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={controls}
      className="text-2xl font-bold mb-2"
    >
      <motion.div>
        {count}
        {"+"}
      </motion.div>
    </motion.h3>
  );
};

interface AchievementItem {
  Icon: any;
  number: string;
  label: string;
}

const achievements: AchievementItem[] = [
  { Icon: FaUsers, number: "1000+", label: t("home.achievement.students") },
  {
    Icon: FaGraduationCap,
    number: "500+",
    label: t("home.achievement.tutors"),
  },
  { Icon: FaBook, number: "2000+", label: "Courses" },
  { Icon: FaTools, number: "1000+", label: "Resources" },
];

const Achievements: React.FC = () => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  React.useEffect(() => {
    if (inView) {
      controls.start("visible");
    }
  }, [controls, inView]);

  return (
    <section ref={ref} className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={controls}
          variants={{
            visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
          }}
          className="text-3xl font-bold text-center mb-12"
        >
          {t("home.achievement.cta-text")}
        </motion.h2>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={controls}
          variants={{
            visible: {
              opacity: 1,
              y: 0,
              transition: { duration: 0.5, delay: 0.2 },
            },
          }}
          className="text-center text-black mb-8"
        >
          <p>{t("home.achievement.description")}</p>
          <span className="inline-block w-[12%] h-0.5 bg-orange-500 rounded-md"></span>
        </motion.div>
        <div className="flex justify-evenly flex-wrap gap-8">
          {achievements.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={controls}
              variants={{
                visible: {
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.5, delay: 0.2 + index * 0.1 },
                },
              }}
              className="text-center"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={controls}
                variants={{
                  visible: {
                    scale: 1,
                    transition: { duration: 0.5, delay: 0.5 + index * 0.1 },
                  },
                }}
                className="bg-primary text-white rounded-full p-4 inline-block mb-4"
              >
                <item.Icon size={32} />
              </motion.div>
              <AnimatedCounter from={0} to={parseInt(item.number)} />
              <p className="text-gray-500">{item.label}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Achievements;
