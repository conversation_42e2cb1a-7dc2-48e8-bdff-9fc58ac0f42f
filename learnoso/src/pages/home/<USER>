import { Spinner } from "@/components/atoms";
import React, { Suspense } from "react";

const Hero = React.lazy(() => import("./components/Hero"));
const Achievements = React.lazy(() => import("./components/Achievements"));
const Features = React.lazy(() => import("./components/Features"));
const Testimonials = React.lazy(() => import("./components/Testimonials"));

export const Home = () => {
  return (
    <div className="bg-white">
      <Suspense fallback={<Spinner size="large" />}>
        <Hero />
        <Achievements />
        <Features />
        <Testimonials />
      </Suspense>
    </div>
  );
};

export default Home;
