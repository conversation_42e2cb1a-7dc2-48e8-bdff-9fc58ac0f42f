import { LearnosoButton } from "@/components/atoms/Button";
import { motion } from "framer-motion";
import { t } from "i18next";
import React from "react";
import { Trans } from "react-i18next";
import { useNavigate } from "react-router-dom";

const Hero: React.FC = () => {
  const navigate = useNavigate();

  // Define animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
  };

  return (
    <motion.section
      initial="hidden"
      animate="visible"
      variants={fadeInUp}
      className="relative bg-cover bg-center"
      style={{
        backgroundImage:
          'url("https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1")',
      }}
    >
      <div className="m-0 bg-black bg-opacity-60 backdrop-blur-0 p-28">
        <div className="">
          <motion.h1
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="text-5xl font-bold text-white mb-4 space-y-2"
          >
            <Trans
              i18nKey="home.hero.cta-text"
              components={{
                span1: <span className="text-orange-500" />,
                span2: <span className="text-orange-500 my-4" />,
                br: <br />,
              }}
            />
          </motion.h1>
          <motion.p
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="text-xl text-white mb-8"
            transition={{ delay: 0.2 }}
          >
            {t("home.hero.connect")}
          </motion.p>
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            transition={{ delay: 0.4 }}
          >
            <LearnosoButton
              action={() => navigate("/tutors")}
              title={t("home.hero.cta-button-title")}
              width="w-fit"
            />
          </motion.div>
          <motion.p
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
            className="mt-4 text-sm text-white"
            transition={{ delay: 0.6 }}
          >
            Python, C++, Java, R, Ruby, Flutter, Swift, IELTS, UI/UX, Speaking,
            <br />
            TOEFL, CCNA, Converse, Compta, English, French, German, Music,
            Acting, TestS, SAT
          </motion.p>
        </div>
      </div>
    </motion.section>
  );
};

export default Hero;
