// src/components/Features.tsx
import { motion, useAnimation, Variants } from "framer-motion";
import { t } from "i18next";
// import { t } from "i18next";
import React from "react";
import { useTranslation } from "react-i18next";
import { useInView } from "react-intersection-observer";

const features = [
  {
    number: "01",
    title: t("home.features.tutor"),
    description: t("home.features.description"),
    color: "text-orange-500",
    bg: "bg-orange-500",
  },
  {
    number: "02",
    title: t("home.features.personalized"),
    description: t("home.features.description02"),
    color: "text-primary",
    bg: "bg-primary",
  },
  {
    number: "03",
    title: t("home.features.interactive"),
    description: t("home.features.description03"),
    color: "text-pink-500",
    bg: "bg-pink-500",
  },
  {
    number: "04",
    title: t("home.features.flexible"),
    description: t("home.features.description04"),
    color: "text-pink-500",
    bg: "bg-pink-500",
  },
  {
    number: "05",
    title: t("home.features.affordable"),
    description: t("home.features.description05"),
    color: "text-green-500",
    bg: "bg-green-500",
  },
  {
    number: "06",
    title: t("home.features.process"),
    description: t("home.features.description06"),
    color: "text-orange-500",
    bg: "bg-orange-500",
  },
];

const FeatureCard = ({ feature, index }: { feature: any; index: number }) => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const controls = useAnimation();

  React.useEffect(() => {
    if (inView) {
      controls.start({
        opacity: 1,
        y: 0,
        transition: { duration: 0.5, delay: index * 0.1 },
      });
    }
  }, [controls, inView, index]);

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={controls}
      whileHover={{
        scale: 1.05,
        boxShadow: "0 10px 20px rgba(0,0,0,0.5)",
      }}
      className="bg-white p-6 rounded-lg shadow-md text-center cursor-pointer"
    >
      <div className={`text-2xl font-bold mb-4 ${feature.color}`}>
        {feature.number}
      </div>
      <div className="mb-2">
        <h3 className="text-2xl font-bold">{feature.title}</h3>
        <motion.span
          initial={{ width: 0 }}
          animate={{ width: "30%" }}
          transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
          className={`inline-block h-1 ${feature.bg} rounded-md`}
        ></motion.span>
      </div>
      <p className="text-gray-500">{feature.description}</p>
    </motion.div>
  );
};

const Features: React.FC = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const controls = useAnimation();

  React.useEffect(() => {
    if (inView) {
      controls.start("visible");
    }
  }, [controls, inView]);

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { duration: 0.5, staggerChildren: 0.3 },
    },
  };
  const { t } = useTranslation();

  return (
    <section
      ref={ref}
      className="bg-slate-200 bg-cover bg-center"
      style={{
        backgroundImage:
          'url("https://st.depositphotos.com/1092019/3124/i/600/depositphotos_31249089-stock-photo-benefits-business-concept.jpg")',
      }}
    >
      <div className="py-16 bg-opacity-10 backdrop-blur-0">
        <motion.h2
          initial={{ opacity: 0, y: -20 }}
          animate={controls}
          variants={containerVariants}
          className="text-3xl font-bold text-center mb-4 text-black"
        >
          {t("home.features.cta-text")}
        </motion.h2>
        <motion.p
          initial={{ opacity: 0, y: -20 }}
          animate={controls}
          variants={containerVariants}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-center text-lg mb-12 text-dark"
        >
          {t("home.features.description")}
        </motion.p>
        <motion.div
          initial="hidden"
          animate={controls}
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 p-9"
        >
          {features.map((feature, index) => (
            <FeatureCard key={index} feature={feature} index={index} />
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default Features;
