// src/components/Testimonials.tsx
import { testimonials } from "@/lib/util";
import { motion } from "framer-motion";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import TestimonialCard from "./TestimonialCard";

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % (testimonials.length - 2));
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) =>
      prev === 0 ? testimonials.length - 3 : prev - 1,
    );
  };
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
  };

  const { t } = useTranslation();

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={fadeInUp}
      className="bg-white py-12"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ">
        <h2 className="text-3xl font-extrabold text-black text-center mb-8">
          {t("home.testimonials.cta-text")}
        </h2>
        <div className="text-xl text-dark text-center mb-12">
          <p>{t("home.testimonials.description")}</p>
          <span className="inline-block w-[30%] h-0.5 bg-orange-500 rounded-md"></span>
        </div>
        <div className="relative">
          <div className="flex overflow-hidden">
            {testimonials
              .slice(currentIndex, currentIndex + 3)
              .map((testimonial) => (
                <TestimonialCard
                  testimonial={testimonial}
                  key={testimonial.author}
                />
              ))}
          </div>
          <button
            onClick={prevTestimonial}
            className="absolute top-1/2 -left-2 transform -translate-y-1/2 bg-primary rounded-full p-2 shadow-md"
          >
            <FaChevronLeft className="h-6 w-6 text-white " />
          </button>
          <button
            onClick={nextTestimonial}
            className="absolute top-1/2 -right-2 transform -translate-y-1/2  bg-primary rounded-full p-2 shadow-md"
          >
            <FaChevronRight className="h-6 w-6 text-white" />
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default Testimonials;
