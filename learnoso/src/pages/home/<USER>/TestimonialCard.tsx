import { Testimonial } from "@/types";
import { motion } from "framer-motion"; // Import framer-motion
import React from "react";
import { FaUser } from "react-icons/fa";

function TestimonialCard({ testimonial }: { testimonial: Testimonial }) {
  // Define animations for appearance and disappearance
  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 }, // Start with hidden state
    visible: { opacity: 1, scale: 1 }, // Animate to visible
    exit: { opacity: 0, scale: 0.9 }, // Animate to exit state
  };

  return (
    <motion.div
      className="w-full sm:w-1/3 px-4"
      variants={cardVariants}
      initial="hidden" // Initial animation state
      animate="visible" // Animate when component is mounted
      exit="exit" // Animate when component is unmounted
      transition={{ duration: 0.5, ease: "easeInOut" }} // Smooth transition
    >
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="">
          <svg
            width="44"
            height="44"
            viewBox="0 0 44 44"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M14.832 21.3217H6.23366C6.38033 12.76 8.06699 11.3483 13.3287 8.23165C13.9337 7.86499 14.1353 7.09499 13.7687 6.47165C13.4203 5.86665 12.632 5.66499 12.027 6.03165C5.83033 9.69832 3.66699 11.935 3.66699 22.5867V32.4683C3.66699 35.6033 6.21533 38.1333 9.33199 38.1333H14.832C18.0587 38.1333 20.497 35.695 20.497 32.4683V26.9683C20.497 23.76 18.0587 21.3217 14.832 21.3217Z"
              fill="#21409A"
            />
            <path
              d="M34.6687 21.3217H26.0704C26.217 12.76 27.9037 11.3483 33.1654 8.23165C33.7704 7.86499 33.972 7.09499 33.6054 6.47165C33.2387 5.86665 32.4687 5.66499 31.8454 6.03165C25.6487 9.69832 23.4854 11.935 23.4854 22.605V32.4867C23.4854 35.6217 26.0337 38.1517 29.1503 38.1517H34.6503C37.877 38.1517 40.3153 35.7133 40.3153 32.4867V26.9867C40.3337 23.76 37.8953 21.3217 34.6687 21.3217Z"
              fill="#21409A"
            />
          </svg>
        </div>
        <p className="text-dark mb-4">"{testimonial.quote}"</p>
        <div className="flex items-center">
          <FaUser className="h-10 w-10 rounded-full bg-primary text-white p-2" />
          <div className="ml-4">
            <p className="text-black font-semibold">{testimonial.author}</p>
            <p className="text-gray-500 text-sm">{testimonial.role}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default TestimonialCard;
