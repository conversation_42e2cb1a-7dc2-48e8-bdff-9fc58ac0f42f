import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Modal, ConfirmModal } from '@/components/ui/Modal';
import { useToast } from '@/components/ui/Toast';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select';
import { Spinner, LoadingOverlay, Skeleton, PageLoading } from '@/components/ui/Loading';
import { PaymentForm } from '@/components/payment/PaymentForm';
import { ChatInterface } from '@/components/chat/ChatInterface';
import { 
  Star, 
  Heart, 
  ThumbsUp, 
  MessageSquare, 
  Settings, 
  User,
  CreditCard,
  Wallet,
  Calendar,
  BookOpen
} from 'lucide-react';

export const ModernUIShowcase: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState(65);
  const { success, error, warning, info } = useToast();

  const handleToastDemo = (type: 'success' | 'error' | 'warning' | 'info') => {
    const messages = {
      success: 'Operation completed successfully!',
      error: 'Something went wrong. Please try again.',
      warning: 'Please check your input and try again.',
      info: 'Here\'s some helpful information.',
    };
    
    switch (type) {
      case 'success':
        success(messages.success);
        break;
      case 'error':
        error(messages.error);
        break;
      case 'warning':
        warning(messages.warning);
        break;
      case 'info':
        info(messages.info);
        break;
    }
  };

  const handleLoadingDemo = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">
            LearnOso Modern UI Showcase
          </h1>
          <p className="text-xl text-gray-600">
            Comprehensive frontend implementation with modern components and full backend integration
          </p>
        </div>

        {/* Buttons Section */}
        <Card>
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>Various button styles and states</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button variant="default">Default</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="gradient">Gradient</Button>
              <Button variant="success">Success</Button>
              <Button variant="warning">Warning</Button>
              <Button loading>Loading</Button>
              <Button leftIcon={<Star className="h-4 w-4" />}>With Icon</Button>
              <Button rightIcon={<Heart className="h-4 w-4" />}>Right Icon</Button>
              <Button size="sm">Small</Button>
            </div>
          </CardContent>
        </Card>

        {/* Form Components */}
        <Card>
          <CardHeader>
            <CardTitle>Form Components</CardTitle>
            <CardDescription>Input fields and form elements</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <Input 
                  label="Email Address" 
                  placeholder="Enter your email"
                  leftIcon={<User className="h-4 w-4" />}
                />
                <Input 
                  label="Password" 
                  type="password"
                  placeholder="Enter your password"
                  helperText="Must be at least 8 characters"
                />
                <Input 
                  label="Error Example" 
                  placeholder="This field has an error"
                  error="This field is required"
                />
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="option1">Option 1</SelectItem>
                    <SelectItem value="option2">Option 2</SelectItem>
                    <SelectItem value="option3">Option 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Progress Bar</label>
                  <Progress value={progress} className="mb-2" />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={() => setProgress(Math.max(0, progress - 10))}>
                      Decrease
                    </Button>
                    <Button size="sm" onClick={() => setProgress(Math.min(100, progress + 10))}>
                      Increase
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Badges</label>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="default">Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="success">Success</Badge>
                    <Badge variant="warning">Warning</Badge>
                    <Badge variant="destructive">Error</Badge>
                    <Badge variant="info">Info</Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interactive Components */}
        <Card>
          <CardHeader>
            <CardTitle>Interactive Components</CardTitle>
            <CardDescription>Modals, toasts, and loading states</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button onClick={() => setModalOpen(true)}>
                Open Modal
              </Button>
              <Button onClick={() => setConfirmModalOpen(true)}>
                Confirm Dialog
              </Button>
              <Button onClick={handleLoadingDemo}>
                Loading Demo
              </Button>
              <Button onClick={() => handleToastDemo('success')}>
                Success Toast
              </Button>
              <Button onClick={() => handleToastDemo('error')}>
                Error Toast
              </Button>
              <Button onClick={() => handleToastDemo('warning')}>
                Warning Toast
              </Button>
              <Button onClick={() => handleToastDemo('info')}>
                Info Toast
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Loading States */}
        <Card>
          <CardHeader>
            <CardTitle>Loading States</CardTitle>
            <CardDescription>Various loading indicators and skeletons</CardDescription>
          </CardHeader>
          <CardContent>
            <LoadingOverlay isLoading={loading}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold">Spinners</h3>
                  <div className="flex items-center gap-4">
                    <Spinner size="sm" />
                    <Spinner size="md" />
                    <Spinner size="lg" />
                    <Spinner size="xl" />
                  </div>
                </div>
                <div className="space-y-4">
                  <h3 className="font-semibold">Skeletons</h3>
                  <Skeleton variant="text" />
                  <Skeleton variant="text" width="60%" />
                  <Skeleton variant="circular" width={40} height={40} />
                  <Skeleton variant="rectangular" height={60} />
                </div>
                <div className="space-y-4">
                  <h3 className="font-semibold">Avatar</h3>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src="https://github.com/shadcn.png" />
                      <AvatarFallback>CN</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">John Doe</p>
                      <p className="text-sm text-gray-600">Software Engineer</p>
                    </div>
                  </div>
                </div>
              </div>
            </LoadingOverlay>
          </CardContent>
        </Card>

        {/* Payment Component */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Integration
            </CardTitle>
            <CardDescription>Modern payment form with Stripe and PayPal support</CardDescription>
          </CardHeader>
          <CardContent>
            <PaymentForm 
              defaultAmount={100}
              onSuccess={(transactionId) => success(`Payment successful! Transaction ID: ${transactionId}`)}
              onError={(error) => error(`Payment failed: ${error}`)}
            />
          </CardContent>
        </Card>

        {/* Feature Summary */}
        <Card>
          <CardHeader>
            <CardTitle>✨ Implementation Summary</CardTitle>
            <CardDescription>What we've built for your modern LearnOso frontend</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Core Infrastructure
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Enhanced API service layer</li>
                  <li>• Modern UI component library</li>
                  <li>• Type-safe service interfaces</li>
                  <li>• Error handling & rate limiting</li>
                  <li>• Toast notification system</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Wallet className="h-4 w-4" />
                  Payment Features
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Stripe & PayPal integration</li>
                  <li>• Wallet management</li>
                  <li>• Transaction history</li>
                  <li>• Currency conversion</li>
                  <li>• Withdrawal system</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Real-time Features
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• WebSocket chat system</li>
                  <li>• Typing indicators</li>
                  <li>• File sharing</li>
                  <li>• Online status</li>
                  <li>• Message history</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Lesson Management
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Lesson scheduling</li>
                  <li>• Time tracking</li>
                  <li>• Video conferencing</li>
                  <li>• Progress analytics</li>
                  <li>• Session management</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Authentication
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Enhanced login/register</li>
                  <li>• Two-factor authentication</li>
                  <li>• Password strength validation</li>
                  <li>• Profile management</li>
                  <li>• Role switching</li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Dashboard Features
                </h3>
                <ul className="text-sm space-y-1 text-gray-600">
                  <li>• Modern dashboard layouts</li>
                  <li>• Student/Tutor/Admin views</li>
                  <li>• Analytics & statistics</li>
                  <li>• Progress tracking</li>
                  <li>• Quick actions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Modals */}
        <Modal
          open={modalOpen}
          onOpenChange={setModalOpen}
          title="Example Modal"
          description="This is a demonstration of the modal component"
        >
          <div className="space-y-4">
            <p>This modal showcases the modern design and smooth animations.</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setModalOpen(false)}>
                Confirm
              </Button>
            </div>
          </div>
        </Modal>

        <ConfirmModal
          open={confirmModalOpen}
          onOpenChange={setConfirmModalOpen}
          title="Confirm Action"
          description="Are you sure you want to perform this action? This cannot be undone."
          onConfirm={() => success('Action confirmed!')}
          confirmText="Yes, Continue"
          variant="destructive"
        />
      </div>
    </div>
  );
};
