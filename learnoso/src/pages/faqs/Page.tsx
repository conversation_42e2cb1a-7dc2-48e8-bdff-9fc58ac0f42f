import { t } from "i18next";
import React, { useState } from "react";
import {
  FiChevronDown,
  FiChevronUp,
  FiMail,
  FiMessageCircle,
  FiPhone,
  FiSearch,
} from "react-icons/fi";

interface FAQItem {
  question: string;
  answer: string;
}

// const faqs: FAQItem[] = [
//   {
//     question: "How do I reset my password?",
//     answer:
//       "To reset your password, click on the 'Forgot Password' link on the login page. Enter your email address, and we'll send you instructions to reset your password.",
//   },
//   {
//     question: "How can I update my profile information?",
//     answer:
//       "Log in to your account, go to the 'Settings' page, and click on 'Edit Profile'. Make your changes and click 'Save' to update your information.",
//   },
//   {
//     question: "What payment methods do you accept?",
//     answer:
//       "We accept various payment methods including credit/debit cards (Visa, MasterCard, American Express), PayPal, and bank transfers.",
//   },
//   {
//     question: "How do I cancel my subscription?",
//     answer:
//       "To cancel your subscription, go to 'Account Settings', click on 'Subscription', and then select 'Cancel Subscription'. Follow the prompts to complete the cancellation process.",
//   },
// ];

const faqs: FAQItem[] = t("faqs.items", {
  returnObjects: true,
}) as Array<{
  question: string;
  answer: string;
}>;

const HelpAndSupport: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Implement search functionality here
    console.log("Searching for:", searchQuery);
  };

  const toggleFAQ = (index: number) => {
    setExpandedFAQ(expandedFAQ === index ? null : index);
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8 text-center">Help & Support</h1>

      {/* Search Bar */}
      <form onSubmit={handleSearch} className="mb-8">
        <div className="relative">
          <input
            type="text"
            placeholder="Search for help..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full p-4 pr-12 rounded-lg border border-slate-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
          >
            <FiSearch size={24} />
          </button>
        </div>
      </form>

      {/* FAQs */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">
          {t("faqs.frequently_asked_questions")}
        </h2>
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="border border-slate-200 rounded-lg">
              <button
                className="flex justify-between items-center w-full p-4 text-left"
                onClick={() => toggleFAQ(index)}
              >
                <span className="font-medium">{faq.question}</span>
                {expandedFAQ === index ? <FiChevronUp /> : <FiChevronDown />}
              </button>
              {expandedFAQ === index && (
                <div className="p-4 bg-slate-50">
                  <p>{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* Contact Information */}
      <section className="mb-12">
        <h2 className="text-2xl font-semibold mb-4"> {t("faqs.contact_us")}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-center">
            <FiMail size={24} className="text-blue-500 mr-3" />
            <div>
              <h3 className="font-medium">{t("common.email")}</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-blue-500 hover:underline"
              >
                {t("faqs.support_email")}
              </a>
            </div>
          </div>
          <div className="flex items-center">
            <FiPhone size={24} className="text-blue-500 mr-3" />
            <div>
              <h3 className="font-medium">{t("faqs.phone")}</h3>
              <a
                href="tel:+1234567890"
                className="text-blue-500 hover:underline"
              >
                +1 (234) 567-890
              </a>
            </div>
          </div>
          <div className="flex items-center">
            <FiMessageCircle size={24} className="text-blue-500 mr-3" />
            <div>
              <h3 className="font-medium">{t("faqs.live_chat")}</h3>
              <button className="text-blue-500 hover:underline">
                {t("faqs.start_chat")}
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Resources */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">
          {" "}
          {t("faqs.additional_resources")}
        </h2>
        <ul className="list-disc list-inside space-y-2">
          <li>
            <a href="#" className="text-blue-500 hover:underline">
              {t("faqs.user_guide")}
            </a>
          </li>
          <li>
            <a href="#" className="text-blue-500 hover:underline">
              {t("faqs.video")}
            </a>
          </li>
          <li>
            <a href="#" className="text-blue-500 hover:underline">
              {t("faqs.community")}
            </a>
          </li>
          <li>
            <a href="#" className="text-blue-500 hover:underline">
              {t("faqs.api_documentation")}
            </a>
          </li>
        </ul>
      </section>
    </div>
  );
};

export default HelpAndSupport;
