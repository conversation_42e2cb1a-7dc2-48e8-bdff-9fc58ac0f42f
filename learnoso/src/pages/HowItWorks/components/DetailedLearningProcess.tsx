import { motion, useAnimation } from "framer-motion";
import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  FaBook,
  FaCalendarAlt,
  FaCertificate,
  FaChartLine,
  FaClipboardCheck,
  FaSearch,
  FaUserPlus,
  FaVideo,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";

const steps = [
  {
    icon: <FaUserPlus className="text-3xl" />,
    title: "1. Sign Up",
    description:
      "Create your Learnoso account as a student or tutor. Fill in your profile with your interests, goals, and experience.",
  },
  {
    icon: <FaSearch className="text-3xl" />,
    title: "2. Explore Courses or Tutors",
    description:
      "Browse our wide range of courses or search for tutors specializing in your areas of interest.",
  },
  {
    icon: <FaCalendarAlt className="text-3xl" />,
    title: "3. Schedule Sessions",
    description:
      "Book one-on-one sessions with tutors or enroll in group classes that fit your schedule.",
  },
  {
    icon: <FaVideo className="text-3xl" />,
    title: "4. Attend Live Sessions",
    description:
      "Join interactive video sessions with your tutor or class, ask questions, and participate in real-time discussions.",
  },
  {
    icon: <FaBook className="text-3xl" />,
    title: "5. Access Learning Materials",
    description:
      "Get personalized study materials, assignments, and resources shared by your tutors.",
  },
  {
    icon: <FaClipboardCheck className="text-3xl" />,
    title: "6. Complete Assignments",
    description:
      "Work on projects and assignments to apply what you've learned and get feedback from your tutors.",
  },
  {
    icon: <FaChartLine className="text-3xl" />,
    title: "7. Track Your Progress",
    description:
      "Monitor your improvement through quizzes, progress reports, and performance analytics.",
  },
  {
    icon: <FaCertificate className="text-3xl" />,
    title: "8. Earn Certificates",
    description:
      "Complete courses and earn certificates to showcase your newly acquired skills.",
  },
];

const StepComponent = ({ step, index }: { step: any; index: number }) => {
  const controls = useAnimation();
  const ref = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          controls.start("visible");
        }
      },
      { threshold: 0.1 },
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [controls]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        visible: { opacity: 1, x: 0 },
        hidden: { opacity: 0, x: -50 },
      }}
      transition={{ duration: 0.5, delay: index * 0.2 }}
      className={`flex items-start mb-16 ${
        index % 2 === 0 ? "flex-row" : "flex-row-reverse"
      } relative z-10`}
    >
      <div
        className={`w-1/2 ${
          index % 2 === 0 ? "pr-8 text-right" : "pl-8 text-left"
        }`}
      >
        <h3 className="text-2xl font-bold text-black mb-2">{step.title}</h3>
        <p className="text-gray-500">{step.description}</p>
      </div>
      <div className="flex items-center justify-center w-16 h-16 rounded-full bg-gray text-white">
        {step.icon}
      </div>
      <div className={`w-1/2 ${index % 2 === 0 ? "pl-8" : "pr-8"}`}></div>
    </motion.div>
  );
};

const DetailedLearningProcess = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  return (
    <section className="bg-slate-100 py-16">
      <div className="container mx-auto px-4">
        <h2 className="text-4xl font-bold text-center text-gray-500-800 mb-12">
          {t("howitworks.detailedlearningprocess.title")}
        </h2>
        <div className="relative">
          <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-dark"></div>
          <div className="max-w-4xl mx-auto ">
            {steps.map((step, index) => (
              <StepComponent key={index} step={step} index={index} />
            ))}
          </div>
        </div>
        <div className="text-center mt-12 flex justify-center">
          <motion.button
            onClick={(_) => navigate("/register")}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex jutify-center items-center bg-blue-500 hover:bg-blue-600 text-white py-3 px-8 rounded-full text-lg font-semibold transition duration-300 ease-in-out"
          >
            {t("howitworks.detailedlearningprocess.startLearningButton")}
          </motion.button>
        </div>
      </div>
    </section>
  );
};

export default DetailedLearningProcess;
