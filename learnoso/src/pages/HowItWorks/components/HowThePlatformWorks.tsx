import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import {
  Fa<PERSON>rrow<PERSON><PERSON>,
  FaBook<PERSON><PERSON>,
  FaChalkboard<PERSON>eacher,
  FaRegChartBar,
} from "react-icons/fa";
import { useNavigate } from "react-router-dom";

function HowThePlatformWorks() {
  const navigate = useNavigate();
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
  };
  const { t } = useTranslation();
  return (
    <>
      <motion.section
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
        className="bg-white py-10"
      >
        <div className="container mx-auto">
          <h2 className="text-4xl font-bold text-center text-gray-500-800 mb-8">
            {t("howitworks.howtheplatformworks.title")}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="flex flex-col items-center text-center">
              <FaBookOpen className="text-4xl text-blue-500 mb-4" />
              <h3 className="text-2xl font-semibold text-black">
                {t("howitworks.howtheplatformworks.signup")}
              </h3>
              <p className="mt-2 text-gray-500">
                {t("howitworks.howtheplatformworks.create")}
              </p>
            </div>

            {/* Step 2 */}
            <div className="flex flex-col items-center text-center">
              <FaChalkboardTeacher className="text-4xl text-green-500 mb-4" />
              <h3 className="text-2xl font-semibold text-black">
                {t("howitworks.howtheplatformworks.jion")}
              </h3>
              <p className="mt-2 text-gray-500">
                {t("howitworks.howtheplatformworks.browse")}
              </p>
            </div>

            {/* Step 3 */}
            <div className="flex flex-col items-center text-center">
              <FaRegChartBar className="text-4xl text-orange-500 mb-4" />
              <h3 className="text-2xl font-semibold text-black">
                {t("howitworks.howtheplatformworks.track")}
              </h3>
              <p className="mt-2 text-gray-500">
                {t("howitworks.howtheplatformworks.monitor")}
              </p>
            </div>
          </div>

          <div className="text-center mt-10 flex justify-center">
            <motion.button
              onClick={(_) => navigate("/register")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex justify-between items-center bg-blue-500 hover:bg-blue-600 text-white py-3 px-8 rounded-full text-lg font-semibold transition duration-300 ease-in-out"
            >
              {t("howitworks.howtheplatformworks.get_started")}{" "}
              <FaArrowRight className="ml-2" />
            </motion.button>
          </div>
        </div>
      </motion.section>
    </>
  );
}

export default HowThePlatformWorks;
