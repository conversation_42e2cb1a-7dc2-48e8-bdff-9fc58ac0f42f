import { motion } from "framer-motion";
import React from "react";
import { useTranslation } from "react-i18next";
import "../styles/style.css";

export const Banner = ({}) => {
  // Define animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.8 } },
  };

  const { t } = useTranslation();
  return (
    <motion.section
      initial="hidden"
      animate="visible"
      variants={fadeInUp}
      style={{
        backgroundImage:
          "url('https://st2.depositphotos.com/1000423/7837/i/600/depositphotos_78379364-stock-photo-thats-how-it-works.jpg')",
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
      className="bg-gradient-to-r from-blue-200 via-blue-300 to-blue-500 "
    >
      <div className="mx-auto flex flex-col lg:flex-row items-center justify-between  m-0 bg-black bg-opacity-60 backdrop-blur-sm p-28 text-white">
        {/* Left side text */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="text-left lg:w-1/2 p-6"
        >
          <h1 className="text-5xl font-bold text-orange-500">
            {t("howitworks.banner.title")}
          </h1>
          <p className="mt-4 text-lg text-gray-500-700">
            {t("howitworks.banner.discover")}
          </p>
          <p className="mt-4 text-lg text-gray-500-700">
            {t("howitworks.banner.description")}
          </p>
        </motion.div>

        {/* Right side animated SVG */}
        {/* <div className="lg:w-1/2 p-6 hidden lg:block">

          <div className="animated-svg-container">
        
            <div className="circle-container">
              <div className="center-text">

              </div>

              <div className="icon concrete-experience">
                <FaBrain className="text-pink-500" />
              </div>

              <div className="icon reflective-observation">
                <FaEye className="text-primary" />
              </div>

              <div className="icon abstract-conceptualization">
                <FaPen className="text-blue-500" />
              </div>

              <div className="icon active-experimentation">
                <FaTools className="text-red-500" />
              </div>

            </div>
          </div>
        </div> */}
      </div>
    </motion.section>
  );
};
