import Tutors from "@/features/student/components/organs/Tutors";
import { motion } from "framer-motion";
import React from "react";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

function TutorsPage() {
  return (
    <div className="col-span-2 p-28 py-10">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <Tutors />
      </motion.div>
    </div>
  );
}

export default TutorsPage;
