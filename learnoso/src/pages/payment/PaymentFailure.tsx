import { SuccessCard } from "@/components/molecules";
import Modal from "@/components/molecules/ModalWrapper";
import { RoleGuard } from "@/features/auth/components/RoleGuard";
import { usePreviousLocation } from "@/hooks";
import React, { useId } from "react";
import { useNavigate } from "react-router-dom";

function PaymentFailure() {
  const id = useId();
  const navigate = useNavigate();
  const previousRoute = usePreviousLocation();
  return (
    <RoleGuard allowedRoles={["student"]}>
      <Modal onClose={() => {}} isOpen>
        <SuccessCard
          action={() => navigate(previousRoute ?? "/student")}
          message="Payment Failed."
          key={"payment" + id}
        />
      </Modal>
    </RoleGuard>
  );
}

export default PaymentFailure;
