import { SuccessCard } from "@/components/molecules";
import Modal from "@/components/molecules/ModalWrapper";
import { RoleGuard } from "@/features/auth/components/RoleGuard";
import React, { useId } from "react";
import { useNavigate } from "react-router-dom";

function PaymentSuccess() {
  const id = useId();
  const navigate = useNavigate();
  return (
    <RoleGuard allowedRoles={["student"]}>
      <Modal onClose={() => {}} isOpen opacity="bg-opacity-5">
        <SuccessCard
          action={() => navigate("/student")}
          message="Payment Successfull."
          key={"payment" + id}
        />
      </Modal>
    </RoleGuard>
  );
}

export default PaymentSuccess;
