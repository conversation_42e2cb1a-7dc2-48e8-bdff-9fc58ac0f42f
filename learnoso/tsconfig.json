{"compilerOptions": {"jsx": "react", "composite": true, "noEmit": true, "target": "ES6", "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "esModuleInterop": true, "allowImportingTsExtensions": true, "forceConsistentCasingInFileNames": true, "types": ["@testing-library/jest-dom"], "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/features/*": ["features/*"], "@/hooks/*": ["hooks/*"], "@/data/*": ["data/*"], "@/pages/*": ["pages/*"], "@/router/*": ["router/*"], "@/assets/*": ["assets/*"], "@/lib/*": ["lib/*"], "@/app/*": ["app/*"], "@/providers/*": ["providers/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx"], "exclude": ["node_modules", "dist"]}