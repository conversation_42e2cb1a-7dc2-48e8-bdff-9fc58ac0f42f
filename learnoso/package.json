{"name": "learnoso", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "prettier:format-check": "prettier --check .", "prettier:write": "prettier --write .", "husky:prepare": "husky install", "vite:preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@devexpress/dx-react-core": "^4.0.8", "@devexpress/dx-react-scheduler": "^4.0.8", "@devexpress/dx-react-scheduler-material-ui": "^4.0.8", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": ">=5.0.0", "@mui/lab": "^5.0.0-alpha.117", "@mui/material": ">=5.0.0", "@mui/x-date-pickers": "^5.0.15", "@mui/x-date-pickers-v6": "npm:@mui/x-date-pickers@^6.18.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@reduxjs/toolkit": "^1.9.7", "agora-rtc-sdk-ng": "^4.23.2", "agora-rtm-sdk": "^2.2.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "framer-motion": "^11.5.4", "i18next": "^24.0.5", "i18next-http-backend": "^3.0.1", "lucide-react": "^0.477.0", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^15.1.3", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-redux": "^8.1.3", "react-router-dom": "^6.11.2", "react-toastify": "^10.0.5", "recharts": "^2.15.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/crypto-js": "^4.2.2", "@types/node": "^22.5.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^2.1.1", "autoprefixer": "^10.4.19", "eslint": "^9.9.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^8.0.0", "jsdom": "^25.0.0", "lint-staged": "^15.2.7", "postcss": "^8.4.38", "prettier": "^3.3.2", "tailwindcss": "^3.4.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^2.1.1"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run format-check"}}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}