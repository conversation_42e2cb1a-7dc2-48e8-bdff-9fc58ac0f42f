<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         executionOrder="random"
         resolveDependencies="true"
         stopOnFailure="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Security">
            <directory suffix="Test.php">./tests/Security</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory>./app/Console/Kernel.php</directory>
            <directory>./app/Exceptions/Handler.php</directory>
            <directory>./app/Http/Middleware</directory>
        </exclude>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:zMl/Ps5Lwc5SvPxQUB97ulWJUOv+HES2wa5v2+cyuhM="/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>

        <!-- Testing specific configurations -->
        <env name="PUSHER_ENABLED" value="false"/>
        <env name="BROADCAST_DRIVER" value="null"/>
        <env name="AUDIT_ENABLED" value="false"/>
        <env name="RATE_LIMITING_ENABLED" value="false"/>
    </php>
</phpunit>
