<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();

            // Relationship to conversation
            $table->foreignId('conversation_id')->constrained('conversations')->onDelete('cascade');

            // Message sender
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');

            // Message content
            $table->text('content');
            $table->enum('type', ['text', 'image', 'file', 'system', 'lesson_link'])->default('text');

            // File attachments (for images/files)
            $table->string('file_path')->nullable();
            $table->string('file_name')->nullable();
            $table->string('file_type')->nullable(); // mime type
            $table->integer('file_size')->nullable(); // in bytes

            // Message metadata
            $table->json('metadata')->nullable(); // For additional data like lesson links, etc.

            // Message status
            $table->enum('status', ['sent', 'delivered', 'read', 'deleted'])->default('sent');
            $table->timestamp('read_at')->nullable();
            $table->timestamp('delivered_at')->nullable();

            // Reply/thread functionality
            $table->foreignId('reply_to_message_id')->nullable()->constrained('messages')->onDelete('set null');

            // Soft deletes for message history
            $table->softDeletes();
            $table->timestamps();

            // Indexes for performance
            $table->index(['conversation_id', 'created_at']);
            $table->index(['sender_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['type']);
            $table->index(['reply_to_message_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
