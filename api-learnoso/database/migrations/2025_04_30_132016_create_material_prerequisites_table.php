<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_prerequisites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('material_id')->constrained('course_materials')->onDelete('cascade');
            $table->json('concepts');
            $table->json('tools');
            $table->json('resources');
            $table->timestamps();
            
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_prerequisites');
    }
};
