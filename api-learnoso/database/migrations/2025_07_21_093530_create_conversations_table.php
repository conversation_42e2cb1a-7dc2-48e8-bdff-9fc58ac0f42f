<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conversations', function (Blueprint $table) {
            $table->id();

            // Participants
            $table->foreignId('student_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('tutor_id')->constrained('users')->onDelete('cascade');

            // Optional lesson association (for lesson-specific chats)
            $table->foreignId('lesson_id')->nullable()->constrained('lessons')->onDelete('set null');

            // Conversation metadata
            $table->string('title')->nullable(); // Optional conversation title
            $table->enum('type', ['general', 'lesson_specific', 'support'])->default('general');
            $table->enum('status', ['active', 'archived', 'blocked'])->default('active');

            // Last activity tracking
            $table->timestamp('last_message_at')->nullable();
            $table->foreignId('last_message_by')->nullable()->constrained('users')->onDelete('set null');

            // Read status tracking
            $table->timestamp('student_last_read_at')->nullable();
            $table->timestamp('tutor_last_read_at')->nullable();

            // Message counts for quick access
            $table->integer('total_messages')->default(0);
            $table->integer('student_unread_count')->default(0);
            $table->integer('tutor_unread_count')->default(0);

            $table->timestamps();

            // Indexes for performance
            $table->index(['student_id', 'tutor_id']);
            $table->index(['lesson_id']);
            $table->index(['last_message_at']);
            $table->index(['status', 'last_message_at']);

            // Ensure unique conversation per student-tutor pair (general conversations)
            $table->unique(['student_id', 'tutor_id', 'type'], 'unique_general_conversation');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
