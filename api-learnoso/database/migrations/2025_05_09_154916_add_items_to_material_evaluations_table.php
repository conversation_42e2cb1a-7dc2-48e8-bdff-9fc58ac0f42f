<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('material_evaluations', function (Blueprint $table) {
            $table->string('title')->after('material_id')->nullable();
            $table->enum('type', ['question', 'assignment', 'both', 'none' ])->after('title')->default('both');
            $table->dateTime('due_date')->after('type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('material_evaluations', function (Blueprint $table) {
            //
        });
    }
};
