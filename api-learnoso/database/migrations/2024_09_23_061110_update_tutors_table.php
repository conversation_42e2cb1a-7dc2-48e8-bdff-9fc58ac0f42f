<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tutors', function (Blueprint $table) {

            //drop 2 columns 'introduction_and_interest', 'teaching_experience_and_certs'
            $table->dropColumn('introduction_and_interest');
            $table->dropColumn('teaching_experience_and_certs');

            // add video_url
            $table->string('video_url')->nullable();
            });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tutors', function (Blueprint $table) {

            //add 2 columns 'introduction_and_interest', 'teaching_experience_and_certs'
            $table->string('introduction_and_interest')->nullable();
            $table->string('teaching_experience_and_certs')->nullable();

            // drop video_url
            $table->dropColumn('video_url');
            }
        );

    }
};
