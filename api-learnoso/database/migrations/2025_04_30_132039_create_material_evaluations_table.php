<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_evaluations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('material_id')->constrained('course_materials')->onDelete('cascade');
            $table->json('questions');
            $table->json('assignments');
            $table->json('assignees')->nullable(); // Store student IDs who are assigned to this evaluation
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_evaluations');
    }


};
