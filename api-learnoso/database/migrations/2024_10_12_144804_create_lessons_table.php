<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tutor_id')->constrained('users');
            $table->foreignId('student_id')->constrained('users');
            $table->foreignId('course_id')->constrained('courses');
            $table->string('title');
            $table->text('description')->nullable();
            $table->dateTime('starts_at');
            $table->dateTime('ends_at');
            $table->boolean('attended')->default(false);
            $table->json("meeting_meta_data")->nullable();
            $table->integer('duration')->nullable();
            $table->string('agora_token')->nullable();
            $table->string('channel_name')->nullable();
            $table->string("timezone")->nullable();
            $table->boolean('confirmed')->default(false);
            $table->string('meeting_link')->nullable();
            $table->string("cancellation_reason")->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('lessons');
    }
};
