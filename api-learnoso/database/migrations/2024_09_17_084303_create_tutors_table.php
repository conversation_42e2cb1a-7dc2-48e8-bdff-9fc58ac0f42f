<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tutors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('profile_picture')->nullable();
            $table->text('bio')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('city')->nullable();
            $table->string('native_language')->nullable();
            $table->float('rating')->nullable();
            $table->float('price')->nullable();
            $table->json('availability')->nullable();
            $table->string(column: 'currency')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('short_description')->nullable();
            $table->text('introduction_and_interest')->nullable();
            $table->text('teaching_experience_and_certs')->nullable();
            $table->text('motivation_to_students')->nullable();
            $table->timestamps();
        });

    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutors');
    }
};
