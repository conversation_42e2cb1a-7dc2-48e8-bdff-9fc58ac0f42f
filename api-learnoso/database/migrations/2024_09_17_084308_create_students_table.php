<?php

use App\Enums\SupportCurrency;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->float('budget')->nullable();
            $table->json('courses_of_preference')->nullable();
            $table->json('reasons_for_learning')->nullable();
            $table->string('timezone')->nullable();
            $table->enum("currency", SupportCurrency::getValues());
            $table->string('prefered_language')->nullable();
            $table->json('availability_times')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
