<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->integer('failed_login_attempts')->default(0)->after('notification_settings');
            $table->timestamp('last_failed_login_at')->nullable()->after('failed_login_attempts');
            $table->timestamp('account_locked_until')->nullable()->after('last_failed_login_at');
            $table->text('lockout_reason')->nullable()->after('account_locked_until');
            $table->timestamp('last_successful_login_at')->nullable()->after('lockout_reason');
            $table->string('last_login_ip')->nullable()->after('last_successful_login_at');

            // Index for performance
            $table->index(['failed_login_attempts', 'account_locked_until']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['failed_login_attempts', 'account_locked_until']);
            $table->dropColumn([
                'failed_login_attempts',
                'last_failed_login_at',
                'account_locked_until',
                'lockout_reason',
                'last_successful_login_at',
                'last_login_ip'
            ]);
        });
    }
};
