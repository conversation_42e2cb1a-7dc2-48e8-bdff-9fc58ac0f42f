<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_curriculums', function (Blueprint $table) {
            $table->id();
            $table->foreignId('material_id')->constrained('course_materials')->onDelete('cascade');
            $table->string('level'); // Beginner, Intermediate, Advanced
            $table->json('modules'); // Array of {title, content, order}
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_curricula');
    }
};
