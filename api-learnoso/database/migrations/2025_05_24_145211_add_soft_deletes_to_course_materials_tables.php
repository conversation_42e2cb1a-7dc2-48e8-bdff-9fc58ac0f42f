<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('course_materials', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('material_prerequisites', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('material_curriculums', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('material_evaluations', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('material_submissions', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('material_reviews', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        
    }
};
