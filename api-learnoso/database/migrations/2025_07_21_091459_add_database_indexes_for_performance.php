<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Indexes for lessons table
        Schema::table('lessons', function (Blueprint $table) {
            $table->index(['student_id', 'status'], 'lessons_student_status_idx');
            $table->index(['tutor_id', 'status'], 'lessons_tutor_status_idx');
            $table->index(['course_id', 'status'], 'lessons_course_status_idx');
            $table->index(['starts_at', 'status'], 'lessons_starts_status_idx');
            $table->index(['created_at'], 'lessons_created_at_idx');
            $table->index(['confirmed', 'attended'], 'lessons_confirmed_attended_idx');
        });

        // Indexes for users table (if not already present)
        Schema::table('users', function (Blueprint $table) {
            $table->index(['email'], 'users_email_idx');
            $table->index(['created_at'], 'users_created_at_idx');
            $table->index(['updated_at'], 'users_updated_at_idx');
        });

        // Indexes for tutors table
        Schema::table('tutors', function (Blueprint $table) {
            $table->index(['user_id'], 'tutors_user_id_idx');
            $table->index(['price'], 'tutors_price_idx');
            $table->index(['profile_status'], 'tutors_profile_status_idx');
            $table->index(['created_at'], 'tutors_created_at_idx');
        });

        // Indexes for students table
        Schema::table('students', function (Blueprint $table) {
            $table->index(['user_id'], 'students_user_id_idx');
            $table->index(['created_at'], 'students_created_at_idx');
        });

        // Indexes for courses table
        Schema::table('courses', function (Blueprint $table) {
            $table->index(['name'], 'courses_name_idx');
            $table->index(['created_at'], 'courses_created_at_idx');
        });

        // Indexes for languages table
        Schema::table('languages', function (Blueprint $table) {
            $table->index(['name'], 'languages_name_idx');
            $table->index(['code'], 'languages_code_idx');
        });

        // Indexes for tutor_reviews table
        Schema::table('tutor_reviews', function (Blueprint $table) {
            $table->index(['tutor_id', 'rating'], 'tutor_reviews_tutor_rating_idx');
            $table->index(['student_id'], 'tutor_reviews_student_idx');
            $table->index(['created_at'], 'tutor_reviews_created_at_idx');
        });

        // Indexes for course_materials table
        Schema::table('course_materials', function (Blueprint $table) {
            $table->index(['course_id', 'type'], 'course_materials_course_type_idx');
            $table->index(['tutor_id'], 'course_materials_tutor_idx');
            $table->index(['difficulty_level'], 'course_materials_difficulty_idx');
            $table->index(['created_at'], 'course_materials_created_at_idx');
        });

        // Indexes for transactions table (if exists)
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                $table->index(['payable_type', 'payable_id'], 'transactions_payable_idx');
                $table->index(['type', 'created_at'], 'transactions_type_created_idx');
                $table->index(['confirmed'], 'transactions_confirmed_idx');
            });
        }

        // Indexes for wallets table (if exists)
        if (Schema::hasTable('wallets')) {
            Schema::table('wallets', function (Blueprint $table) {
                $table->index(['holder_type', 'holder_id'], 'wallets_holder_idx');
                $table->index(['name'], 'wallets_name_idx');
            });
        }

        // Indexes for system_settings table
        if (Schema::hasTable('system_settings')) {
            Schema::table('system_settings', function (Blueprint $table) {
                $table->index(['key'], 'system_settings_key_idx');
            });
        }

        // Indexes for personal_access_tokens table
        if (Schema::hasTable('personal_access_tokens')) {
            Schema::table('personal_access_tokens', function (Blueprint $table) {
                $table->index(['tokenable_type', 'tokenable_id'], 'personal_access_tokens_tokenable_idx');
                $table->index(['expires_at'], 'personal_access_tokens_expires_idx');
            });
        }

        // Indexes for failed_jobs table
        if (Schema::hasTable('failed_jobs')) {
            Schema::table('failed_jobs', function (Blueprint $table) {
                $table->index(['failed_at'], 'failed_jobs_failed_at_idx');
                $table->index(['queue'], 'failed_jobs_queue_idx');
            });
        }

        // Indexes for jobs table
        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->index(['queue', 'reserved_at'], 'jobs_queue_reserved_idx');
                $table->index(['available_at'], 'jobs_available_at_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop indexes for lessons table
        Schema::table('lessons', function (Blueprint $table) {
            $table->dropIndex('lessons_student_status_idx');
            $table->dropIndex('lessons_tutor_status_idx');
            $table->dropIndex('lessons_course_status_idx');
            $table->dropIndex('lessons_starts_status_idx');
            $table->dropIndex('lessons_created_at_idx');
            $table->dropIndex('lessons_confirmed_attended_idx');
        });

        // Drop indexes for users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_email_idx');
            $table->dropIndex('users_created_at_idx');
            $table->dropIndex('users_updated_at_idx');
        });

        // Drop indexes for tutors table
        Schema::table('tutors', function (Blueprint $table) {
            $table->dropIndex('tutors_user_id_idx');
            $table->dropIndex('tutors_price_idx');
            $table->dropIndex('tutors_profile_status_idx');
            $table->dropIndex('tutors_created_at_idx');
        });

        // Drop indexes for students table
        Schema::table('students', function (Blueprint $table) {
            $table->dropIndex('students_user_id_idx');
            $table->dropIndex('students_created_at_idx');
        });

        // Drop indexes for courses table
        Schema::table('courses', function (Blueprint $table) {
            $table->dropIndex('courses_name_idx');
            $table->dropIndex('courses_created_at_idx');
        });

        // Drop indexes for languages table
        Schema::table('languages', function (Blueprint $table) {
            $table->dropIndex('languages_name_idx');
            $table->dropIndex('languages_code_idx');
        });

        // Drop indexes for tutor_reviews table
        Schema::table('tutor_reviews', function (Blueprint $table) {
            $table->dropIndex('tutor_reviews_tutor_rating_idx');
            $table->dropIndex('tutor_reviews_student_idx');
            $table->dropIndex('tutor_reviews_created_at_idx');
        });

        // Drop indexes for course_materials table
        Schema::table('course_materials', function (Blueprint $table) {
            $table->dropIndex('course_materials_course_type_idx');
            $table->dropIndex('course_materials_tutor_idx');
            $table->dropIndex('course_materials_difficulty_idx');
            $table->dropIndex('course_materials_created_at_idx');
        });

        // Drop indexes for other tables if they exist
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                $table->dropIndex('transactions_payable_idx');
                $table->dropIndex('transactions_type_created_idx');
                $table->dropIndex('transactions_confirmed_idx');
            });
        }

        if (Schema::hasTable('wallets')) {
            Schema::table('wallets', function (Blueprint $table) {
                $table->dropIndex('wallets_holder_idx');
                $table->dropIndex('wallets_name_idx');
            });
        }

        if (Schema::hasTable('system_settings')) {
            Schema::table('system_settings', function (Blueprint $table) {
                $table->dropIndex('system_settings_key_idx');
            });
        }

        if (Schema::hasTable('personal_access_tokens')) {
            Schema::table('personal_access_tokens', function (Blueprint $table) {
                $table->dropIndex('personal_access_tokens_tokenable_idx');
                $table->dropIndex('personal_access_tokens_expires_idx');
            });
        }

        if (Schema::hasTable('failed_jobs')) {
            Schema::table('failed_jobs', function (Blueprint $table) {
                $table->dropIndex('failed_jobs_failed_at_idx');
                $table->dropIndex('failed_jobs_queue_idx');
            });
        }

        if (Schema::hasTable('jobs')) {
            Schema::table('jobs', function (Blueprint $table) {
                $table->dropIndex('jobs_queue_reserved_idx');
                $table->dropIndex('jobs_available_at_idx');
            });
        }
    }
};
