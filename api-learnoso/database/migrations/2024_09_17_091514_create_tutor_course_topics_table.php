<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tutor_course_topic', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tutor_id')->constrained('users', 'id');
            $table->foreignId('course_id')->constrained();
            $table->foreignId('topic_id')->constrained();
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tutor_course_topics');
    }
};
