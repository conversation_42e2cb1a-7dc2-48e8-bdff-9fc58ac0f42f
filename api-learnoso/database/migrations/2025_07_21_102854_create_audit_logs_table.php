<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();

            // Event details
            $table->string('event_type'); // login, logout, password_change, 2fa_enable, etc.
            $table->string('action'); // created, updated, deleted, accessed, failed
            $table->text('description'); // Human readable description

            // User and actor details
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('actor_id')->nullable()->constrained('users')->onDelete('set null'); // Who performed the action
            $table->string('actor_type')->nullable(); // user, system, admin, api

            // Subject details (what was affected)
            $table->string('subject_type')->nullable(); // Model class name
            $table->unsignedBigInteger('subject_id')->nullable(); // Model ID

            // Context and metadata
            $table->json('old_values')->nullable(); // Before state
            $table->json('new_values')->nullable(); // After state
            $table->json('metadata')->nullable(); // Additional context

            // Request details
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('request_method', 10)->nullable();
            $table->text('request_url')->nullable();
            $table->json('request_data')->nullable(); // Sanitized request data

            // Security and risk
            $table->enum('risk_level', ['low', 'medium', 'high', 'critical'])->default('low');
            $table->boolean('is_suspicious')->default(false);
            $table->text('suspicious_reason')->nullable();

            // Session and authentication
            $table->string('session_id')->nullable();
            $table->string('auth_method')->nullable(); // password, 2fa, api_token, etc.

            // API and system context
            $table->string('api_version')->nullable();
            $table->string('source')->default('web'); // web, mobile, api, system

            // Tags for categorization
            $table->json('tags')->nullable(); // ['security', 'payment', 'admin']

            // Status and flags
            $table->enum('status', ['success', 'failed', 'pending', 'cancelled'])->default('success');
            $table->boolean('requires_review')->default(false);
            $table->timestamp('reviewed_at')->nullable();
            $table->foreignId('reviewed_by')->nullable()->constrained('users')->onDelete('set null');

            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'created_at']);
            $table->index(['actor_id', 'created_at']);
            $table->index(['event_type', 'created_at']);
            $table->index(['risk_level', 'created_at']);
            $table->index(['is_suspicious', 'created_at']);
            $table->index(['subject_type', 'subject_id']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['session_id']);
            $table->index(['requires_review']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
