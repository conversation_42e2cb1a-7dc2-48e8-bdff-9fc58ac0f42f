<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Get the current settings
        $settings = DB::table('system_settings')->first();

        if ($settings) {
            $currentSettings = json_decode($settings->settings, true) ?? [];

            // Add commission settings if they don't exist
            $currentSettings['commission'] = [
                'lesson_commission_rate' => 0.10, // 10%
                'minimum_commission_amount' => 1.00,
                'maximum_commission_amount' => 50.00,
                'withdrawal_fee' => 0.02, // 2%
                'minimum_withdrawal_amount' => 10.00
            ];

            // Add notification settings
            $currentSettings['notifications'] = [
                'email_notifications_enabled' => true,
                'push_notifications_enabled' => true,
                'notification_types' => [
                    'lesson_scheduled' => true,
                    'lesson_cancelled' => true,
                    'lesson_reminder' => true,
                    'payment_received' => true,
                    'withdrawal_processed' => true,
                    'system_updates' => true
                ]
            ];

            // Update the settings
            DB::table('system_settings')
                ->where('id', $settings->id)
                ->update(['settings' => json_encode($currentSettings)]);
        }
    }

    public function down()
    {
        // Get the current settings
        $settings = DB::table('system_settings')->first();

        if ($settings) {
            $currentSettings = json_decode($settings->settings, true) ?? [];

            // Remove commission and notification settings
            unset($currentSettings['commission']);
            unset($currentSettings['notifications']);

            // Update the settings
            DB::table('system_settings')
                ->where('id', $settings->id)
                ->update(['settings' => json_encode($currentSettings)]);
        }
    }
};
