<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            $table->enum('profile_status', ['pending', 'approved', 'rejected'])->default('pending');
            $table->timestamp('profile_approved_at')->nullable();
            $table->timestamp('profile_rejected_at')->nullable();
            $table->text('profile_rejection_reason')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tutors', function (Blueprint $table) {
            $table->dropColumn('profile_status');
            $table->dropColumn('profile_approved_at');
            $table->dropColumn('profile_rejected_at');
            $table->dropColumn('profile_rejection_reason');
        });
    }
};
