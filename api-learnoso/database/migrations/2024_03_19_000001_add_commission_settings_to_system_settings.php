<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // Get the current settings
        $settings = DB::table('system_settings')->first();

        if ($settings) {
            $currentSettings = json_decode($settings->settings, true) ?? [];

            // Add commission settings if they don't exist
            $updatedSettings = array_merge($currentSettings, [
                'lesson_commission_rate' => 0.10,
                'minimum_commission_amount' => 1.00,
                'maximum_commission_amount' => 50.00
            ]);

            // Update the settings
            DB::table('system_settings')
                ->where('id', $settings->id)
                ->update([
                    'settings' => json_encode($updatedSettings),
                    'updated_at' => now()
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Get the current settings
        $settings = DB::table('system_settings')->first();

        if ($settings) {
            $currentSettings = json_decode($settings->settings, true) ?? [];

            // Remove commission settings
            unset($currentSettings['lesson_commission_rate']);
            unset($currentSettings['minimum_commission_amount']);
            unset($currentSettings['maximum_commission_amount']);

            // Update the settings
            DB::table('system_settings')
                ->where('id', $settings->id)
                ->update([
                    'settings' => json_encode($currentSettings),
                    'updated_at' => now()
                ]);
        }
    }
};
