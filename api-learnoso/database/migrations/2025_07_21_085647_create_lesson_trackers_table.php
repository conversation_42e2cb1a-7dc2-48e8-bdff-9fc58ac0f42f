<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lesson_trackers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lesson_id')->constrained('lessons')->onDelete('cascade');
            $table->foreignId('student_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('tutor_id')->constrained('users')->onDelete('cascade');

            // Session timing
            $table->timestamp('session_started_at')->nullable();
            $table->timestamp('session_ended_at')->nullable();
            $table->integer('total_duration_seconds')->default(0); // Total session time in seconds
            $table->integer('active_duration_seconds')->default(0); // Active teaching time (excluding breaks)
            $table->integer('break_duration_seconds')->default(0); // Total break time

            // Session status
            $table->enum('status', ['not_started', 'in_progress', 'paused', 'completed', 'cancelled'])->default('not_started');
            $table->boolean('is_active')->default(false); // Currently active/running

            // Break tracking
            $table->integer('break_count')->default(0);
            $table->timestamp('last_break_started_at')->nullable();
            $table->timestamp('last_break_ended_at')->nullable();

            // Video session tracking
            $table->string('agora_channel')->nullable();
            $table->json('session_metadata')->nullable(); // Store additional session data

            // Billing related
            $table->boolean('is_billable')->default(true);
            $table->decimal('billable_amount', 10, 2)->nullable();
            $table->integer('minimum_session_seconds')->default(900); // 15 minutes minimum

            $table->timestamps();

            // Indexes for performance
            $table->index(['lesson_id', 'status']);
            $table->index(['student_id', 'created_at']);
            $table->index(['tutor_id', 'created_at']);
            $table->index(['is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lesson_trackers');
    }
};
