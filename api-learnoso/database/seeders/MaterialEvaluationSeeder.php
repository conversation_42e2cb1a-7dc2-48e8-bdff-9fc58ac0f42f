<?php

namespace Database\Seeders;

use App\Models\CourseMaterial;
use App\Models\MaterialEvaluation;
use App\Models\MaterialSubmission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MaterialEvaluationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Questions
        $questions = [];
        $set = ['a','b','c','d'];

        for($i=0; $i<2; $i++){
            for($j=0; $j<2; $j++){
                $questions[$i][] = [
                    "question" => fake()->paragraph(),
                    "responses" => [
                        "a" => rand(5, 11),
                        "b" => rand(5, 11),
                        "c" => rand(5, 11),
                        "d" => rand(5, 11),
                    ],
                    "answer" => [$set[rand(0,3)]],
                ];
            }
        }

        // Assignments
        $assignments = [];

        for($i=0; $i<1; $i++){
            for($j=0; $j<3; $j++){
                $assignments[$i][] = [
                    "question" => fake()->paragraph(),
                    "graded_on" => rand(5, 25)
                ];
            }
        }

        // Assignees
        $assignees = [];
        for($i=0; $i<4; $i++){
            $assignees[] = rand(1, 20);
        }

        // Type
        $type = ["none", "both", "question", "assignment"];
        
        // Feedback
        $feedback = ["Bad", "Poor", "Average", "Very Good", "Excellent"];
        
        // IDs
        $ids = CourseMaterial::pluck("id")->toArray();
        $end = count($ids) - 1;
        
        for($i=0; $i<11; $i++){
            MaterialEvaluation::create([
                "material_id" => $ids[rand(0, $end)],
                "type" => $type[rand(0,3)],
                "due_date" => fake()->date(),
                "questions" => $questions,
                "assignments" => $assignments,
                "assignees" => $assignees,
            ]);
        }

        // IDs
        $ids = MaterialEvaluation::pluck("id")->toArray();
        $end = count($ids) - 1;

        // file paths
        $file_paths = [];
        $value = rand(2, 5);

        for($i=0; $i<$value; $i++){
            $file_paths[] = fake()->filePath();
        }

        for($i=0; $i<7; $i++){
            MaterialSubmission::create([
                "evaluation_id" => $ids[rand(0, $end)],
                "student_id" => fake()->numberBetween(3, 30),
                "type" => $type[rand(0,3)],
                "content" => fake()->paragraph(),
                "file_paths" => $file_paths,
                "is_graded" => fake()->boolean(),
                "score" => fake()->numberBetween(5, 17),
                "feedback" => $feedback[rand(0,4)],
            ]);
        }
    }
}
