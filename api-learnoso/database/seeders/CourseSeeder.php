<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Course;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $courses = [
            ['name' => 'Python Programming', 'description' => 'Learn Python programming for web development, data analysis, and machine learning.'],
            ['name' => 'Java Development', 'description' => 'Build scalable applications with Java for enterprise, Android, and web development.'],
            ['name' => 'Web Development with PHP', 'description' => 'Create dynamic websites using PHP and its popular frameworks.'],
            ['name' => 'JavaScript for Beginners', 'description' => 'Learn the basics of JavaScript for web interactivity and development.'],
            ['name' => 'C Programming Basics', 'description' => 'Understand the fundamentals of the C language for systems programming.'],
            ['name' => 'C++ for Game Development', 'description' => 'Use C++ to develop high-performance games and real-time applications.'],
            ['name' => 'TypeScript Essentials', 'description' => 'Learn TypeScript to write safer and scalable JavaScript applications.'],
            ['name' => 'Swift for iOS Development', 'description' => 'Develop apps for iPhone and iPad using Swift programming.'],
            ['name' => 'Kotlin for Android Development', 'description' => 'Build modern Android apps using Kotlin.'],
            ['name' => 'Ruby on Rails', 'description' => 'Learn Ruby programming and build web applications with Rails.'],
            ['name' => 'Go (Golang) Programming', 'description' => 'Explore Go for building efficient and scalable cloud applications.'],
            ['name' => 'R for Data Science', 'description' => 'Learn R programming for statistical computing and data visualization.'],
            ['name' => 'SQL for Data Management', 'description' => 'Master SQL queries to manage and retrieve data from relational databases.'],
            ['name' => 'NoSQL Databases', 'description' => 'Learn NoSQL concepts and work with MongoDB for non-relational data.'],
            ['name' => 'React.js Development', 'description' => 'Create responsive and dynamic user interfaces using React.js.'],
            ['name' => 'Node.js for Backend Development', 'description' => 'Develop scalable server-side applications with Node.js.'],
            ['name' => 'Angular Framework', 'description' => 'Build modern web applications using the Angular framework.'],
            ['name' => 'Vue.js Essentials', 'description' => 'Learn Vue.js to create lightweight and interactive front-end apps.'],
            ['name' => 'Django for Web Development', 'description' => 'Use Python and Django to build robust web applications.'],
            ['name' => 'Laravel Framework', 'description' => 'Master Laravel for building modern PHP web applications.'],
            ['name' => 'Spring Framework', 'description' => 'Build robust Java applications using the Spring framework.'],
            ['name' => 'Machine Learning with Python', 'description' => 'Understand machine learning algorithms and implement them in Python.'],
            ['name' => 'Artificial Intelligence Basics', 'description' => 'Learn the fundamentals of AI and its practical applications.'],
            ['name' => 'Blockchain Development', 'description' => 'Develop blockchain-based applications and smart contracts.'],
            ['name' => 'Cybersecurity Basics', 'description' => 'Understand the fundamentals of securing systems and data.'],
            ['name' => 'DevOps Tools and Practices', 'description' => 'Learn CI/CD, Docker, Kubernetes, and other DevOps tools.'],
            ['name' => 'Cloud Computing with AWS', 'description' => 'Build cloud-native applications using Amazon Web Services.'],
            ['name' => 'UI/UX Design Principles', 'description' => 'Learn the essentials of designing intuitive user interfaces.'],
            ['name' => 'Game Development with Unity', 'description' => 'Build interactive 2D and 3D games using Unity.'],

            ['name' => 'English for Professionals', 'description' => 'Improve your English skills for professional communication.'],
            ['name' => 'Spanish for Beginners', 'description' => 'Learn basic Spanish grammar, vocabulary, and conversation.'],
            ['name' => 'French for Travelers', 'description' => 'Master essential French phrases and vocabulary for travel.'],
            ['name' => 'German Language Basics', 'description' => 'Learn German grammar and conversational skills for beginners.'],
            ['name' => 'Mandarin Chinese for Beginners', 'description' => 'Start learning Mandarin with essential phrases and pronunciation.'],
            ['name' => 'Japanese Language Essentials', 'description' => 'Learn basic Japanese grammar and conversation skills.'],
            ['name' => 'Portuguese for Beginners', 'description' => 'Learn Portuguese for everyday communication and travel.'],
            ['name' => 'Russian Language Basics', 'description' => 'Start learning Russian with basic grammar and vocabulary.'],
            ['name' => 'Korean for Beginners', 'description' => 'Learn the basics of the Korean language and culture.'],
            ['name' => 'Italian for Beginners', 'description' => 'Discover the basics of Italian grammar and common phrases.'],
        ];

        foreach ($courses as $course) {
            Course::create($course);
        }
    }
}
