<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Student;
use App\Models\Course;
use Illuminate\Support\Facades\Hash;
use App\Enums\SupportCurrency;

class StudentSeeder extends Seeder
{
    public function run()
    {
        $courseIds = Course::pluck('id')->toArray();

        if (empty($courseIds)) {
            $this->command->warn('No courses found in the database. Please add courses before running this seeder.');
            return;
        }

        $timezones = \DateTimeZone::listIdentifiers();
        $currencies = SupportCurrency::getValidCurrencies();

        for ($i = 1; $i <= 50; $i++) {
            $user = User::create([
                'first_name' => 'Student' . $i,
                'last_name' => 'Lastname' . $i,
                'email' => 'student' . $i . '@example.com',
                'country' => 'Country' . $i,
                'password' => Hash::make('password123'),
            ]);

            $user->assignRole('student');

            $randomCourseIds = collect($courseIds)->random(rand(1, min(4, count($courseIds))))->toArray();
            $randomTimezone = $timezones[array_rand($timezones)];
            $randomCurrency = $currencies[array_rand($currencies)];

            Student::create([
                'user_id' => $user->id,
                'budget' => random_int(100, 1000),
                'courses_of_preference' => $randomCourseIds,
                'reasons_for_learning' => ['Career growth', 'Personal interest'],
                'availability_times' => ['Morning', 'Evening'],
                'timezone' => $randomTimezone,
                'prefered_language' => 'English',
                'currency' => $randomCurrency,
            ]);
        }
    }
}
