<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\OnboardingProgress;
use App\Models\CurrentRole;
use Spatie\Permission\Models\Role;

class SetOnboardingStatusSeeder extends Seeder
{
    public function run()
    {
        $students = User::role('student')->get();

        foreach ($students as $student) {
            if ($student->roles->count() === 1) {
                OnboardingProgress::updateOrCreate(
                    ['user_id' => $student->id],
                    [
                        'current_step' => 5,
                        'onboarding_type' => 'student'
                    ]
                );

                $role = Role::where('name', 'student')->firstOrFail();

                $currentRole = CurrentRole::firstOrCreate(
                    ['user_id' => $student->id],
                    [
                        'role_id' => $role->id,
                        'lastly_switched_on' => now()
                    ]
                );

                if (!$currentRole->wasRecentlyCreated) {
                    $currentRole->update([
                        'role_id' => $role->id,
                        'lastly_switched_on' => now()
                    ]);
                }

                $student->assignRole('guest');
                $student->save();
            }
        }
    }
}
