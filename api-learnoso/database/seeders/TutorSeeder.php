<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Tutor;
use App\Models\Course;
use App\Models\Education;
use Illuminate\Support\Facades\Hash;
use App\Enums\SupportCurrency;
use App\Enums\SupportTimezone;

class TutorSeeder extends Seeder
{
    public function run()
    {
        $courseIds = Course::pluck('id')->toArray();
        $currencies = SupportCurrency::getValidCurrencies();
                     $timezones = \DateTimeZone::listIdentifiers();


        if (empty($courseIds)) {
            $this->command->warn('No courses found in the database. Please add courses before running this seeder.');
            return;
        }

        for ($i = 1; $i <= 30; $i++) {
            $user = User::create([
                'first_name' => 'Tutor' . $i,
                'last_name' => 'Lastname' . $i,
                'email' => 'tutor' . $i . '@example.com',
                'country' => 'Country' . $i,
                'password' => Hash::make('password123'),
            ]);

            $user->assignRole('tutor');

            $randomCourseIds = collect($courseIds)->random(rand(1, min(4, count($courseIds))))->toArray();
            $randomCurrency = collect($currencies)->random();
            $randomTimezone = collect($timezones)->random();

            $availability = [
                [
                    'time' => 'morning',
                    'availability' => [
                        'from' => '08:00',
                        'to' => '12:00',
                    ],
                    'day_of_the_week' => 'monday',
                ],
                [
                    'time' => 'afternoon',
                    'availability' => [
                        'from' => '13:00',
                        'to' => '17:00',
                    ],
                    'day_of_the_week' => 'tuesday',
                ],
                [
                    'time' => 'evening',
                    'availability' => [
                        'from' => '18:00',
                        'to' => '22:00',
                    ],
                    'day_of_the_week' => 'friday',
                ],
            ];

            $tutor = Tutor::create([
                'user_id' => $user->id,
                'profile_picture' => 'https://via.placeholder.com/150',
                'bio' => 'Experienced tutor in various subjects.',
                'phone_number' => '************',
                'city' => 'City' . $i,
                'native_language' => 'English',
                'rating' => rand(1, 5),
                'price' => rand(10, 100),
                'currency' => $randomCurrency,
                'is_active' => true,
                'short_description' => 'Dedicated tutor helping students succeed.',
                'motivation_to_students' => 'Let\'s work together to achieve your goals!',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'timezone' => $randomTimezone,
                'availability' => json_encode($availability),  // Storing availability as JSON
            ]);

            $user->courses()->attach($randomCourseIds);

            $educationData = [
                [
                    'subject' => 'Mathematics',
                    'certificate' => 'Bachelor of Science in Mathematics',
                    'institution' => 'University of Example',
                    'start_date' => now()->subYears(rand(1, 5)),
                    'end_date' => now()->subYears(rand(1, 4)),
                    'description' => 'Completed undergraduate degree in Mathematics.',
                    'url' => 'https://www.universityofexample.edu'
                ],
                [
                    'subject' => 'Physics',
                    'certificate' => 'Master of Science in Physics',
                    'institution' => 'Institute of Physics',
                    'start_date' => now()->subYears(rand(1, 6)),
                    'end_date' => now()->subYears(rand(1, 5)),
                    'description' => 'Completed post-graduate studies in Physics.',
                    'url' => 'https://www.instituteofphysics.edu'
                ],
            ];

            foreach ($educationData as $education) {
                Education::create(array_merge($education, [
                    'user_id' => $user->id
                ]));
            }
        }
    }
}
