<?php

namespace Database\Seeders;

use App\Models\Language;
use Illuminate\Database\Seeder;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            'English',
            'Spanish',
            'French',
            'German',
            'Mandarin Chinese',
            'Arabic',
            'Russian',
            'Portuguese',
            'Hindi',
            'Bengali',
            'Urdu',
            'Japanese',
            'Swahili',
            'Turkish',
            'Korean',
            'Vietnamese',
            'Thai',
            'Italian',
            'Dutch',
            'Greek',
            'Farsi (Persian)',
            'Tagalog',
            'Indonesian',
            'Malay',
            'Hebrew',
            'Polish',
            'Swedish',
            'Norwegian',
            'Danish',
            'Finnish',
            'Czech',
            'Hungarian',
            'Romanian',
            'Bulgarian',
            'Slovak',
            'Croatian',
            'Serbian',
            'Slovenian',
            'Macedonian',
            'Albanian',
            'Bosnian',
            'Montenegrin',
            'Catalan',
            'Galician',
            'Portuguese (Brazil)',
            'Portuguese (Portugal)',
            'Basque',
            'Lithuanian',
            'Latvian',
            'Estonian',
            'Georgian',
            'Armenian',
            'Azerbaijani',
            'Kazakh',
            'Kyrgyz',
            'Turkmen',
            'Uzbek',
            'Tajik',
            'Ukrainian',
            'Belarusian',
            'Tibetan',
            'Nepali',
            'Sinhalese',
            'Burmese',
            'Khmer',
            'Lao',
            'Hmong',
            'Malayalam',
            'Kannada',
            'Telugu',
            'Tamil',
            'Punjabi',
            'Marathi',
            'Gujarati',
            'Oriya',
            'Assamese',
            'Maithili',
            'Konkani',
            'Sindhi',
            'Sanskrit',
            'Pashto',
            'Balochi',
            'Saraiki',
            'Uyghur',
            'Mongolian',
        ];

        foreach ($languages as $language) {
            Language::create(['name' => $language]);
        }
    }
}
