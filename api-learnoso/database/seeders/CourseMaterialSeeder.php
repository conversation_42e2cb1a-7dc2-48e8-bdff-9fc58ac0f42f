<?php

namespace Database\Seeders;

use App\Models\Comments;
use App\Models\CourseMaterial;
use App\Models\MaterialCurriculum;
use App\Models\MaterialEvaluation;
use App\Models\MaterialReview;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CourseMaterialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        for($i=0; $i<5; $i++){
            CourseMaterial::create([
                "course_id" => fake()->numberBetween(1, 4),
                "tutor_id" => fake()->unique()->numberBetween(10, 14),
                "title" => "CourseTitle" . $i,
                "slug" => fake()->slug(),
                "description" => fake()->paragraph(),
                "skill_level" => "Intermediate",
                "last_updated" => now(),
            ]);
        }


        for($i=0; $i<7; $i++){
            MaterialReview::create(
                [
                    'material_id'=> fake()->numberBetween(1,3),
                    'student_id'=> fake()->numberBetween(1,20),
                    'content'=> fake()->paragraph(),
                    'votes'=> fake()->numberBetween(1,100),
                    'rating'=> rand(5,0),
                ]
            );
        }

        $j = 1;
        for($i=0; $i<15; $i++){
            if($i % 4 === 0) $j++;
            Comments::create(
                [
                'review_id'=> $j,
                'student_id'=> fake()->numberBetween(1,4),
                'content'=>fake()->paragraph(),
                'votes'=> fake()->numberBetween(1,100),
                'parent_id'=>  $i,
                ]
            );
        }

        $levels = ['beginner', 'intermediate', 'advanced'];
        $modules = [];
        
        for($i= 0; $i< 3; $i++){
            for($k= 0; $k< rand(3,5); $k++){
                $modules[] = ['name' => fake()->slug(), "number" => $k+1];
            }
            for($j= 0; $j< 3; $j++){
                MaterialCurriculum::create([
                    'material_id' => $i+1,
                    'level' => $levels[$j],
                    'modules' => $modules
                ]);
            }
            $modules = [];
        }
    }
}