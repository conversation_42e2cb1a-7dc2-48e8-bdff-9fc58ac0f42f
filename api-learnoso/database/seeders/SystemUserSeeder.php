<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class SystemUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure the 'admin' role exists
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'student']);
        Role::firstOrCreate(['name' => 'guest']);
        Role::firstOrCreate(['name' => 'tutor']);

        // Create the system user
        $systemUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'System',
                'last_name' => 'User',
                'country' => 'Global',
                'password' => Hash::make('password'), // Set a secure password
                'email_verified_at' => now(),
            ]
        );

        // Assign the 'admin' role to the system user
        $systemUser->assignRole($adminRole);
    }
}
