<?php

use App\Http\Middleware\Cors;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Illuminate\Auth\AuthenticationException; // Ensure this is imported
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Illuminate\Support\Facades\Route;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\RateLimiter;


return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        using: function () {
            Route::middleware('api')
                ->prefix('')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->prefix('web')
                ->group(base_path('routes/web.php'));
        },
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Configure rate limiting here where it belongs
        RateLimiter::for('auth', function (Request $request) {
            return app()->environment('testing')
                ? Limit::none()
                : Limit::perMinute(5)->by($request->ip());
        });

        RateLimiter::for('payment', function (Request $request) {
            return app()->environment('testing')
                ? Limit::none()
                : Limit::perMinute(3)->by($request->user()?->id ?: $request->ip());
        });

        RateLimiter::for('chat', function (Request $request) {
            return app()->environment('testing')
                ? Limit::none()
                : Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        RateLimiter::for('api', function (Request $request) {
            return app()->environment('testing')
                ? Limit::none()
                : Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });

        $middleware->alias([
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'throttle' => \App\Http\Middleware\ApiRateLimit::class,
            'audit' => \App\Http\Middleware\AuditLogMiddleware::class,
            'csrf' => \App\Http\Middleware\VerifyCsrfToken::class,
            'sanitize' => \App\Http\Middleware\SanitizeInput::class,
        ]);
    })
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            env('APP_URL') . '/*'
        ]);
        $middleware->append(Cors::class);
        $middleware->append(\App\Http\Middleware\SanitizeInput::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {

        // Handle Authentication Exception
        $exceptions->render(function (AuthenticationException $e, Request $request) {
            return response()->json(['message' => 'Unauthenticated.'], 401);
        });

        // Handle Validation Exception
        $exceptions->render(function (ValidationException $e, Request $request) {
            return response()->json([
                'message' => 'Validation Error',
                'errors' => $e->errors(),
            ], 422);
        });

        // Handle Route Nof Found
        $exceptions->render(function (RouteNotFoundException $e, Request $request) {

            // check if $e has any thing related to login
            if (str_contains($e->getMessage(), 'Route [login] not defined')) {

                return response()->json(
                    [
                        'success' => false,
                        'message' => 'Unauthenticated, You are not logged in'
                    ],
                    401
                );
            }

            return response()->json(
                [
                    'success' => false,
                    'message' => 'Endpoint not found.'
                ],
                404);
        });

        // Handle Model Not Found Exception
        $exceptions->render(function (ModelNotFoundException $e, Request $request) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Resource not found.'
                ],
                404);
        });

        // Handle Not Found HTTP Exception
        $exceptions->render(function (NotFoundHttpException $e, Request $request) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Endpoint not found.'
                ],
                404);
        });

        // Handle Method Not Allowed HTTP Exception
        $exceptions->render(function (MethodNotAllowedHttpException $e, Request $request) {
            return response()->json([
                    'success' => false,
                    'message' => 'Method not allowed.'
                ], 405);
        });

        // Handle Too Many Requests Exception (Rate Limiting)
        $exceptions->render(function (TooManyRequestsHttpException $e, Request $request) {
            return response()->json([
                    'success' => false,
                    'message' => 'Too many attempts, please try again later.'], 429);
        });


        // Handle Generic HTTP Exception
        $exceptions->render(function (HttpException $e, Request $request) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()], $e->getStatusCode());
        });

        // // Handle Generic Exception
        // $exceptions->render(function (Throwable $e, Request $request) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'Server error. Please try again later.'], 500);
        // });

        // Send all generic exceptions to Sentry
        $exceptions->render(function (Throwable $e) {
            \Sentry\captureException($e);
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()], 500);
        });



        // Decide if JSON should be returned for all requests (since all are API)
        $exceptions->shouldRenderJsonWhen(function (Request $request, Throwable $e) {
            return true; // Always return JSON
        });

    })->create();
