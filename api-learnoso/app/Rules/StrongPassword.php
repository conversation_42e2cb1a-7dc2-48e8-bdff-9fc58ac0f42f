<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPassword implements ValidationRule
{
    protected $minLength;
    protected $requireUppercase;
    protected $requireLowercase;
    protected $requireNumbers;
    protected $requireSpecialChars;
    protected $maxLength;
    protected $disallowCommonPasswords;

    public function __construct(
        int $minLength = 8,
        bool $requireUppercase = true,
        bool $requireLowercase = true,
        bool $requireNumbers = true,
        bool $requireSpecialChars = true,
        int $maxLength = 128,
        bool $disallowCommonPasswords = true
    ) {
        $this->minLength = $minLength;
        $this->requireUppercase = $requireUppercase;
        $this->requireLowercase = $requireLowercase;
        $this->requireNumbers = $requireNumbers;
        $this->requireSpecialChars = $requireSpecialChars;
        $this->maxLength = $maxLength;
        $this->disallowCommonPasswords = $disallowCommonPasswords;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        $errors = [];

        // Check minimum length
        if (strlen($value) < $this->minLength) {
            $errors[] = "at least {$this->minLength} characters";
        }

        // Check maximum length
        if (strlen($value) > $this->maxLength) {
            $errors[] = "no more than {$this->maxLength} characters";
        }

        // Check for uppercase letters
        if ($this->requireUppercase && !preg_match('/[A-Z]/', $value)) {
            $errors[] = "at least one uppercase letter";
        }

        // Check for lowercase letters
        if ($this->requireLowercase && !preg_match('/[a-z]/', $value)) {
            $errors[] = "at least one lowercase letter";
        }

        // Check for numbers
        if ($this->requireNumbers && !preg_match('/[0-9]/', $value)) {
            $errors[] = "at least one number";
        }

        // Check for special characters
        if ($this->requireSpecialChars && !preg_match('/[^A-Za-z0-9]/', $value)) {
            $errors[] = "at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)";
        }

        // Check against common passwords
        if ($this->disallowCommonPasswords && $this->isCommonPassword($value)) {
            $errors[] = "not be a commonly used password";
        }

        // Check for repeated characters (more than 3 in a row)
        if (preg_match('/(.)\1{3,}/', $value)) {
            $errors[] = "not contain more than 3 repeated characters in a row";
        }

        // Check for sequential characters
        if ($this->hasSequentialChars($value)) {
            $errors[] = "not contain obvious sequential patterns (abc, 123, qwe)";
        }

        if (!empty($errors)) {
            $message = 'The :attribute must contain ' . $this->formatErrorList($errors) . '.';
            $fail($message);
        }
    }

    /**
     * Check if password contains sequential characters
     */
    private function hasSequentialChars(string $password): bool
    {
        $sequences = [
            // Alphabetical sequences
            'abcdefghijklmnopqrstuvwxyz',
            'zyxwvutsrqponmlkjihgfedcba',
            // Number sequences
            '01234567890',
            '09876543210',
            // Keyboard sequences
            'qwertyuiop',
            'asdfghjkl',
            'zxcvbnm',
            'poiuytrewq',
            'lkjhgfdsa',
            'mnbvcxz'
        ];

        $password = strtolower($password);

        foreach ($sequences as $sequence) {
            for ($i = 0; $i <= strlen($sequence) - 3; $i++) {
                $substr = substr($sequence, $i, 3);
                if (strpos($password, $substr) !== false) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Check if password is commonly used
     */
    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', 'password123', '123456', '123456789', 'qwerty',
            'abc123', 'password1', 'admin', 'letmein', 'welcome',
            'monkey', '1234567890', 'iloveyou', 'princess', 'rockyou',
            '12345678', 'abc123', 'nicole', 'daniel', 'babygirl',
            'michael', 'ashley', 'qwerty123', 'welcome123', 'admin123',
            'root', 'toor', 'pass', 'test', 'guest', 'user',
            'dragon', 'sunshine', 'master', 'shadow', 'jesus',
            'football', 'baseball', 'superman', 'hello', 'freedom'
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Format error list into readable text
     */
    private function formatErrorList(array $errors): string
    {
        if (count($errors) === 1) {
            return $errors[0];
        }

        if (count($errors) === 2) {
            return $errors[0] . ' and ' . $errors[1];
        }

        $lastError = array_pop($errors);
        return implode(', ', $errors) . ', and ' . $lastError;
    }

    /**
     * Get password strength score (0-100)
     */
    public static function getStrengthScore(string $password): array
    {
        $score = 0;
        $feedback = [];

        // Length scoring
        if (strlen($password) >= 8) {
            $score += 20;
        } else {
            $feedback[] = 'Use at least 8 characters';
        }

        if (strlen($password) >= 12) {
            $score += 10;
        }

        // Character variety scoring
        if (preg_match('/[a-z]/', $password)) {
            $score += 10;
        } else {
            $feedback[] = 'Add lowercase letters';
        }

        if (preg_match('/[A-Z]/', $password)) {
            $score += 15;
        } else {
            $feedback[] = 'Add uppercase letters';
        }

        if (preg_match('/[0-9]/', $password)) {
            $score += 15;
        } else {
            $feedback[] = 'Add numbers';
        }

        if (preg_match('/[^A-Za-z0-9]/', $password)) {
            $score += 20;
        } else {
            $feedback[] = 'Add special characters';
        }

        // Bonus points for variety
        $uniqueChars = strlen(count_chars($password, 3));
        if ($uniqueChars >= 8) {
            $score += 10;
        }

        // Penalties
        if (preg_match('/(.)\1{2,}/', $password)) {
            $score -= 10;
            $feedback[] = 'Avoid repeating characters';
        }

        $rule = new self();
        if ($rule->hasSequentialChars($password)) {
            $score -= 15;
            $feedback[] = 'Avoid sequential patterns';
        }

        if ($rule->isCommonPassword($password)) {
            $score -= 25;
            $feedback[] = 'Avoid common passwords';
        }

        $score = max(0, min(100, $score));

        $strength = 'Very Weak';
        if ($score >= 80) $strength = 'Very Strong';
        elseif ($score >= 60) $strength = 'Strong';
        elseif ($score >= 40) $strength = 'Medium';
        elseif ($score >= 20) $strength = 'Weak';

        return [
            'score' => $score,
            'strength' => $strength,
            'feedback' => $feedback
        ];
    }
}
