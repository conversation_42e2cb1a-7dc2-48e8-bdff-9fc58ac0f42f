<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class SupportCurrency extends Enum
{
    const NGN = "ngn";
    const ZAR = "zar";
    const MAD = "mad";
    const EGP = "egp";
    const GHS = "ghs";
    const KES = "kes";
    const TND = "tnd";
    const XOF = "xof";
    const XAF = "xaf";

    const USD = "usd";
    const EUR = "eur";
    const GBP = "gbp";
    const CNY = "cny";
    const JPY = "jpy";

    public static function getValidCurrencies(): array
    {
        return static::getValues();
    }

    public static function getKeyValuePairs(): array
    {
        return array_map(
            fn($value) => [$value => strtoupper(str_replace('_', ' ', $value))],
            static::getValues()
        );
    }
}
