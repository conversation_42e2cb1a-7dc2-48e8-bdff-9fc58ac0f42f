<?php

namespace App\Listeners;

use App\Services\RealTimeChatService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleUserOnlineStatusEvent implements ShouldQueue
{
    use InteractsWithQueue;

    protected RealTimeChatService $realTimeChatService;

    /**
     * Create the event listener.
     */
    public function __construct(RealTimeChatService $realTimeChatService)
    {
        $this->realTimeChatService = $realTimeChatService;
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            // Extract data from WebSocket event
            $userId = $event->userId ?? null;
            $isOnline = $event->isOnline ?? true;

            if (!$userId) {
                Log::warning('Invalid online status event data', [
                    'user_id' => $userId
                ]);
                return;
            }

            // Handle the online status event through the service
            $this->realTimeChatService->updateUserOnlineStatus($userId, $isOnline);

            Log::info('Online status event handled', [
                'user_id' => $userId,
                'is_online' => $isOnline
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle online status event', [
                'error' => $e->getMessage(),
                'event' => $event
            ]);
        }
    }
}
