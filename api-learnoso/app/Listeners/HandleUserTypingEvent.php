<?php

namespace App\Listeners;

use App\Services\RealTimeChatService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class HandleUserTypingEvent implements ShouldQueue
{
    use InteractsWithQueue;

    protected RealTimeChatService $realTimeChatService;

    /**
     * Create the event listener.
     */
    public function __construct(RealTimeChatService $realTimeChatService)
    {
        $this->realTimeChatService = $realTimeChatService;
    }

    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        try {
            // Extract data from WebSocket event
            $userId = $event->userId ?? null;
            $conversationId = $event->conversationId ?? null;
            $isTyping = $event->isTyping ?? true;

            if (!$userId || !$conversationId) {
                Log::warning('Invalid typing event data', [
                    'user_id' => $userId,
                    'conversation_id' => $conversationId
                ]);
                return;
            }

            // Handle the typing event through the service
            $this->realTimeChatService->handleUserTyping($userId, $conversationId, $isTyping);

            Log::info('Typing event handled', [
                'user_id' => $userId,
                'conversation_id' => $conversationId,
                'is_typing' => $isTyping
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to handle typing event', [
                'error' => $e->getMessage(),
                'event' => $event
            ]);
        }
    }
}
