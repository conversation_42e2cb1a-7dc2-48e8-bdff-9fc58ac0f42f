<?php

namespace App\Repositories\Eloquent;

use App\Http\Requests\MaterialEvaluationRequest;
use App\Http\Requests\MaterialReviewsRequest;
use App\Models\Comments;
use App\Models\CourseMaterial;
use App\Models\MaterialEvaluation;
use App\Models\MaterialReview;
use App\Models\MaterialSubmission;
use App\Models\Tutor;
use App\Models\User;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Dom\Comment;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Helpers\CommentHelper;
use Illuminate\Support\Facades\Auth;

class CourseMaterialRepository implements CourseMaterialRepositoryInterface
{

    public function all(array $filters = []): LengthAwarePaginator
    {
        $query = CourseMaterial::query();

        if (isset($filters['course_id'])) {
            $query->where('course_id', $filters['course_id']);
        }

        if (isset($filters['tutor_id'])) {
            $query->where('tutor_id', $filters['tutor_id']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_published'])) {
            $query->where('is_published', $filters['is_published']);
        }

        // load course, tutor, curriculums, prerequisite, evaluation, reviews
        $query->with([
            'course',
            'tutor',
            'curriculums', // fillable property in model doesn't align with what is defined in migration
            'prerequisite',
            'evaluation',
            'reviews',
        ]);

        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $query->orderBy($sortBy, $sortDirection);

        return $query->paginate($filters['per_page'] ?? 10);
    }
    public function find(int|string $id)
    {
        return CourseMaterial::with([
            'course',
            'tutor',
            'curriculums',  // fillable property in model doesn't align with what is defined in migration
            'prerequisite',
            'evaluation',
            'reviews'
            ])->findOrFail($id);
    }

    public function create(array $data)
    {
        // Check if tutor exists and has the correct role
        $tutor = Tutor::where('id', $data['tutor_id'])->first();

        if (!$tutor) {
            throw new Exception('Invalid tutor ID or tutor not found');
        }

        // Ensure all required fields are present
        $requiredFields = [
            'course_id',
            'tutor_id',
            'title',
            'slug',
            'description',
            'language',
            'skill_level',
            'last_updated',
            'currency',
            'price_per_hour',
            // 'total_hours'               // total_hours not defined in course_materials table
        ];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }

        // Create the course material
        return CourseMaterial::create($data);
    }

    public function update(int $id, array $data)
    {
        $material = $this->find($id);
        $material->update($data);
        return $material;
    }

    public function delete(int $id)
    {
        $material = $this->find($id);
        return $material->delete();
    }

    public function findByCourseId(int $course_id)
    {
        return CourseMaterial::where('course_id', $course_id)->get();
    }

    public function findByTutorId(int $tutor_id)
    {
        return CourseMaterial::where('tutor_id', $tutor_id)->get();
    }

    public function createCurriculum(array $data)
    {
        $curriculums =CourseMaterial::find($data['material_id'])->curriculums();

        foreach( $curriculums->get() as $curriculum ) {
            if($curriculum->level == $data['level']) {
                return false;
            }
        }
        
        return $curriculums->create($data);
    }
    public function getCurriculum(int $id, int $material_id)
    {
        return CourseMaterial::find($material_id)->curriculums()->find($id);
    }

    public function getPrerequisite(int $id, int $material_id)
    {
        return CourseMaterial::find($material_id)->prerequisite()->find($id);
    }

        public function createPrerequisite($request)
    {
        $courseMaterial = CourseMaterial::find($request->material_id);

        $data = [
            'material_id' => $request->material_id,
            'concepts' => $request->concepts,
            'tools' => $request->tools,
            'resources' => []
        ];

        foreach ($request->resources as $resource) {
            $imagePath = $resource['image']->store('images/resources', 'public');

            $data['resources'][] = [
                'title' => $resource['title'],
                'resource_link' => $resource['resource_link'],
                'image' => $imagePath,
            ];
        }

        return $courseMaterial->prerequisite()->create($data);
    }

    public function updateCurriculum(array $data)
    {
        $curriculum = CourseMaterial::find($data['material_id'])->curriculums()->find($data['id']);
        $curriculum->update($data);
        return $curriculum;
    }

    public function updatePrerequisite(array $data)
    {
        $prerequisite = CourseMaterial::find($data['material_id'])->prerequisite()->find($data['id']);
        $prerequisite->update($data);
        return $prerequisite;
    }
    
    public function allEvaluation(int $material_id)
    {
        return MaterialEvaluation::where("material_id", operator: $material_id)->get();
    }

    public function getEvaluation(int $id)
    {
        return MaterialEvaluation::with(["material"])->find($id);
    }

    public function createEvaluation($request): array
    {
        try {
            $evaluation = new MaterialEvaluation();
            $evaluation->material_id = $request->material_id;
            if(isset($request->title)) {
                $evaluation->title = $request->title;
            }
            if(isset($request->due_date)) {
                $evaluation->due_date = $request->due_date;
            }
            if(isset($request->type)) {
                if($request->type === "question") {
                    $evaluation->questions = $request->questions;
                } elseif ($request->type === "assignment") {
                    $evaluation->assignments = $request->assignments;
                } elseif ($request->type === "both") {
                    $evaluation->questions = $request->questions;
                    $evaluation->assignments = $request->assignments;
                }
            }
            if(isset($request->assignees)) {
                $evaluation->assignees = $request->assignees;
            }

            $evaluation->save();

            return [
                "status" => true,
                "id" => $evaluation->id
            ];
        } catch(Exception $e) {
            return [
                "status" => false,
                "message" => $e->getMessage()
            ];
        }
    }

    public function updateEvaluation($request, $id)
    {
        try {
            $evaluation = MaterialEvaluation::find($id);
            if(isset($request->questions)) {
                $evaluation->questions = $request->questions;
            }
            if(isset($request->assignments)) {
                $evaluation->assignments = $request->assignments;
            }
            if(isset($request->assignees)) {
                $evaluation->assignees = $request->assignees;
            }
            if(isset($request->title)) {
                $evaluation->title = $request->title;
            }
            if(isset($request->type)) {
                $evaluation->type = $request->type;
            }
            if(isset($request->due_date)) {
                $evaluation->due_date = $request->due_date;
            }
            $evaluation->save();

            return [
                "status" => true,
                "id" => $evaluation->id
            ];
        } catch(Exception $e) {
            return [
                "status" => false,
                "message" => $e->getMessage()
            ];
        }
    }

    public function allSubmissions($evaluation_id)
    {
        try
        {
            if(isset($evaluation_id)){
                return MaterialSubmission::where("evaluation_id", $evaluation_id)->get();
            } else {
                return MaterialSubmission::get();
            }
        } catch(Exception $e){
            return [];
        }
    }

    public function getSubmission($id)
    {
        try
        {
            return MaterialSubmission::find($id);
        } catch(Exception $e){
            return [];
        }
    }

    public function createSubmission($request)
    {
        try {
            $submission = new Materialsubmission();
            $submission->evaluation_id = $request->evaluation_id;
            $submission->student_id = $request->student_id;
            $submission->type = $request->type;
            $submission->content = $request->content;
            $submission->is_graded = $request->is_graded;
            $submission->score = $request->score;
            if($request->file_path) {
                $submission->file_path = $request->file_path;
            }
            if($request->feedback) {
                $submission->feedback = $request->feedback;
            }

            $submission->save();

            return [
                "status" => true,
                "id" => $submission->id
            ];
        } catch(Exception $e) {
            return [
                "status" => false,
                "message" => $e->getMessage()
            ];
        }
    
    }

    public function updateSubmission($request, int|string $id)
    {
        $submission = MaterialSubmission::where("id", $id)
            ->update($request->only([
                "type",
                "content",
                "score",
                "is_graded",
                "feedback",
            ]));
        return $submission;
    }

    public function getReview($materialId)
{
    try {
        // Fetch reviews for the given material ID
        $reviews = MaterialReview::where('material_id', $materialId)
            ->with(['comments'])
            ->get();

        // Check if any reviews were found
        if ($reviews->isEmpty()) {
            return ['status' => false, "message"=> "No Review Found"];
        }

        // Rearranges comments
        foreach($reviews as $review){
            $review->comments = CommentHelper::buildCommentTree($review->comments);
        }

        return [ "status" => true ,"data" => $reviews ];
    } catch (Exception $e) {
        return ['status' => false, $e->getMessage()];
    }
}






    public function createReview($request): array
    {
        try {
            $review = new MaterialReview();
            $review->material_id = $request->material_id;
            $review->student_id = $request->student_id;
            if(isset($request->content)) {
                $review->content = $request->content;
            }
            if(isset($request->rating)) {
                $review->rating = $request->rating;
            }

            $review->save();

            return [
                "status" => true,
                "data" => [$review]
            ];
        } catch(Exception $e) {
            return [
                "status" => false,
                "message" => $e->getMessage()
            ];
        }
    }


    public function updateReview($request){
        $review = MaterialReview::where("id", $request->id)->first();
        try{
            if(isset($request->content)){
                $review->content = $request->content;
            }
            if(isset($request->rating)) {
                $review->rating = $request->rating;
            }
            $review->save();
            return [
                "status"=> true,
                "data"=> $review
                ];

        }
        catch(Exception $e){
            return [
                "status"=> false,
                "message"=> $e->getMessage()
            ];
        }
    }
    public function deleteReview(int $review_id){
        $review = MaterialReview::where("id", $review_id)->first();
        $review->delete();
        return [
            "status"=> true,
        ];
    }

    
      public function createComment($request)
    {
        
        try {
            $comment = new Comments();
        
            $comment->review_id = $request->review_id;
            $comment->student_id = $request->student_id;
            $comment->content = $request->content;
            $comment->parent_id = $request->parent_id;
            $comment->save();

            return [
                "status" => true,
                "data" => $comment
            ];
        } catch(Exception $e) {
            return [
                "status" => false,
                "message" => $e->getMessage()
            ];
        }
    }


   public function updateComment($request){
    $comment = Comments::where("id", $request->id)->first();
    try{
        if(isset($request->content)){
        $comment->content =  $request->content;
    }
    $comment->save();
       return [
            "status"=> true,
            "data"=> $comment
        ];
    }
    catch(Exception $e) {
        return [
            "status"=> false,
            "message"=> $e->getMessage()
        ];
    }
   }
   public function deleteComment(int $comment_id){
        $review = Comments::where("id", $comment_id)->first();
        $review->delete();
        return [
            "status"=> true,
        ];
    }
 }

