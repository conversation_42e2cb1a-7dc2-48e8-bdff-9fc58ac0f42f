<?php

namespace App\Repositories\Eloquent;

use App\Models\Lesson;
use App\Repositories\Contracts\LessonRepositoryInterface;
use Illuminate\Support\Facades\DB;

class LessonRepository implements LessonRepositoryInterface
{
    public function createLesson(array $lessonData)
    {

        return Lesson::create($lessonData);
    }

    public function updateLesson(Lesson $lesson, array $data)
    {
        $lesson->update($data);
        return $lesson;
    }

    public function deleteLesson(Lesson $lesson)
    {
        return $lesson->delete();
    }

    public function find($id)
    {
        return Lesson::with('tutor', 'student', 'course')->find($id);
    }


    public function getLessonsByTutorId($tutorId)
    {
        return Lesson::with('tutor', 'student', 'course')->where('tutor_id', $tutorId)->get();
    }

    public function getLessonsByStudentId($studentId)
    {
        return Lesson::with('tutor', 'student', 'course')->where('student_id', $studentId)->get();
    }

    public function transaction(callable $callback)
    {
        return DB::transaction($callback);
    }
}
