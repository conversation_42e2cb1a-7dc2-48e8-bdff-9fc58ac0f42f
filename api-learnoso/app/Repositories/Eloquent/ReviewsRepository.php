<?php

namespace App\Repositories\Eloquent;

use App\Models\TutorReview;
use App\Repositories\Contracts\ReviewsRepositoryInterface;
use Illuminate\Http\Request;

class ReviewsRepository implements ReviewsRepositoryInterface
{
    public function getReviewsByTutorId($tutorId)
    {
        return TutorReview::with('tutor.user', 'student.user')->where('tutor_id', $tutorId)->paginate(10);
    }

    public function getReviewsByStudentId($studentId)
    {
        return TutorReview::with('tutor.user', 'student.user')->where('student_id', $studentId)->get();
    }

    public function hasStudentReviewedTutor($studentId, $tutorId)
    {
        return TutorReview::where('student_id', $studentId)
            ->where('tutor_id', $tutorId)
            ->exists();
    }

    public function createReview(array $reviewData)
    {
        return TutorReview::create($reviewData);
    }

    public function updateReview($reviewId, array $data)
    {
        return TutorReview::find($reviewId)->update($data);
    }

    public function deleteReview($reviewId)
    {
        return TutorReview::find($reviewId)->delete();
    }

    public function find($id)
    {
        return TutorReview::with('tutor.user', 'student.user')->find($id);
    }

    public function all(Request $request)
    {

        return TutorReview::with('tutor.user', 'student.user')->paginate(
            $request->has('per_page') ? $request->per_page : 10
        );
    }

}
