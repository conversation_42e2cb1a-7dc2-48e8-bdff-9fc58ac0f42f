<?php

namespace App\Repositories\Eloquent;
use App\Models\Course;
use App\Repositories\Contracts\CourseRepositoryInterface;

class CourseRepository implements CourseRepositoryInterface
{

    public function create(array $data)
    {
        return Course::create($data);
    }


    public function update($id, array $data)
    {
        $course = $this->find($id);
        $course->update($data);
        return $course;
    }

    public function delete($id)
    {
        $course = $this->find($id);
        return $course->delete();
    }
    
    public function find($id)
    {
        return Course::find($id);
    }

    public function all(){
        return Course::with('tutors', 'topics')->get();
    }
}
