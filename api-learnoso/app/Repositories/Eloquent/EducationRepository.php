<?php

namespace App\Repositories\Eloquent;

use App\Models\Education;
use App\Repositories\Contracts\EducationRepositoryInterface;

class EducationRepository implements EducationRepositoryInterface
{
    public function create(array $data)
    {
        return Education::create($data);
    }

    public function update(array $data, $id)
    {
        $education = Education::findOrFail($id);
        $education->update($data);
        return $education;
    }

    public function delete($id)
    {
        $education = Education::findOrFail($id);
        $education->delete();
        return true;
    }
}
