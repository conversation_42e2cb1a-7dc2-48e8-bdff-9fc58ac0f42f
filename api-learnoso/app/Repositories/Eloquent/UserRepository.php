<?php

namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;

class UserRepository implements UserRepositoryInterface
{
    protected $model;

    public function __construct(User $user)
    {
        $this->model = $user;
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function find($id)
    {
        return $this->model->findOrFail($id);
    }

    public function findByEmail($email)
    {
        return User::where('email', $email)->first();
    }

    public function all($filters = [])
    {
    // query with relations;
        $query = $this->model->with(['roles', 'tutor', 'student','courses']);

        if (isset($filters['role'])) {
            $query->whereHas('roles', function($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        if (isset($filters['search'])) {
            $query->where('first_name', 'like', '%' . $filters['search'] . '%')
            ->orWhere('last_name', 'like', '%' . $filters['search'] . '%')
                ->orWhere('email', 'like', '%' . $filters['search'] . '%');
        }

        if(isset($filters['course_id'])) {
            $query->whereHas('courses', function($q) use ($filters) {
                $q->where('course_id', $filters['course_id']);
            });
        }

        if(isset($filters['country'])) {
            $query->where('country', $filters['country']);
        }

        // check if it has to do with ratings, query the user.tutor table
        if(isset($filters['rating'])) {
            $query->whereHas('tutor', function($q) use ($filters) {
                $q->where('rating', '>=', $filters['rating']);
            });

        }

        // check pagination page_size
        $pageSize = $filters['page_size'] ?? 10;

        return $query->paginate($pageSize);
    }

    public function getUserSummary()
    {

        $tutorCount = $this->model->whereHas('roles', function($q) {
            $q->where('name', 'tutor');
        })->count();

        $studentCount = $this->model->whereHas('roles', function($q) {
            $q->where('name', 'student');
        })->count();

        $adminCount = $this->model->whereHas('roles', function($q) {
            $q->where('name', 'admin');
        })->count();

        $guestCounts = $this->model->whereHas('roles', function($q) {
            $q->where('name', 'guest');
        })->count();

        $totalUsers = $this->model->count();

        return compact('tutorCount', 'studentCount', 'adminCount', 'guestCounts', 'totalUsers');
    }
}
