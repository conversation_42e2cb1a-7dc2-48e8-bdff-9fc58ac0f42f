<?php

namespace App\Repositories\Eloquent;

use App\Models\Lesson;
use App\Models\Tutor;
use App\Models\User;
use App\Repositories\Contracts\TutorRepositoryInterface;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class TutorRepository implements TutorRepositoryInterface
{

    public function search(array $filters)
    {
        try {
            $query = User::role('tutor')
                ->with(['courses', 'tutor.students.user', 'languages']);

            $this->applyFilters($filters, $query);

            // Apply search query
            if (isset($filters['query'])) {
                $query->where(function ($subQuery) use ($filters) {
                    $subQuery->where('name', 'like', '%' . $filters['query'] . '%');
                    $subQuery->orWhereHas('courses', function ($courseQuery) use ($filters) {
                        $courseQuery->where('name', 'like', '%' . $filters['query'] . '%');
                    });
                    $subQuery->orWhereHas('tutor', function ($profileQuery) use ($filters) {
                        $profileQuery->where('name', 'like', '%' . $filters['query'] . '%');
                    });
                    $subQuery->orWhereHas('tutor', function ($profileQuery) use ($filters) {
                        $profileQuery->where('price', '>=', $filters['min_price'] ?? 0);
                        $profileQuery->where('price', '<=', $filters['max_price'] ?? PHP_FLOAT_MAX);
                    });
                    $subQuery->orWhereHas('languages', function ($languageQuery) use ($filters) {
                        $languageQuery->where('name', 'like', '%' . $filters['query'] . '%');
                    });
                });
            }

            return $query;
        } catch (\Exception $e) {
            throw $e;
        }
    }


    public function getTutorApplicationStats(): array|null
    {
        $totalTutors = User::role('tutor')->count();
        $pendingTutors = User::role('tutor')->whereHas('tutor', function ($query) {
            $query->where('profile_status', 'pending');
        })->count();

        $verifiedTutors = User::role('tutor')->whereHas('tutor', function ($query) {
            $query->where('profile_status', 'approved');
        })->count();

        $rejectedTutors = User::role('tutor')->whereHas('tutor', function ($query) {
            $query->where('profile_status', 'rejected');
        })->count();

        return [
            'totalTutors' => $totalTutors,
            'pendingTutors' => $pendingTutors,
            'verifiedTutors' => $verifiedTutors,
            'rejectedTutors' => $rejectedTutors,
        ];
    }


    public function findByUserId($userId)
    {
        return User::with(
            'tutor.students.user',
            'courses',
            'educations',
            'languages',
        )->where('id', $userId)->first();
    }
    public function getTutor($id)
    {
        //
    }

    public function getTutors($filters): array
    {
        $query = $this->search($filters);

        $query->whereHas('roles', function ($roleQuery) {
            $roleQuery->where('name', 'tutor');
        })->whereNotNull('email_verified_at');

        $query->with(['tutor.students.user', 'courses', 'educations', 'languages']);

        // Sort the tutors and re-index
        $tutors = $query->get()
            ->sortByDesc(function ($tutor) {
                return $tutor->is_verified_tutor;
            })
            ->values(); // Re-index the collection to ensure numeric keys

        $perPage = $filters['per_page'] ?? 10;
        $currentPage = isset($filters['page']) ? (int) $filters['page'] : 1;

        $paginatedTutors = new LengthAwarePaginator(
            $tutors->forPage($currentPage, $perPage)->values(), // Re-index here explicitly
            $tutors->count(),
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );

        return $paginatedTutors->toArray();
    }


    public function getTutorApplications($filters): array
    {
        $query = User::query();

        // apply filters
        if(isset($filters['status'])){
            $query->whereHas('tutor', function($q) use ($filters){
                $q->where('profile_status', $filters['status']);
            });
        }

        if(isset($filters['search'])){
            $query->where('first_name', 'like', '%'.$filters['search'].'%')
                ->orWhere('last_name', 'like', '%'.$filters['search'].'%')
                ->orWhere('email', 'like', '%'.$filters['search'].'%');
        }

        if(isset($filters['country'])){
            $query->where('country', $filters['country']);
        }

        if(isset($filters['timezone'])){
            $query->whereHas('tutor', function($q) use ($filters){
                $q->where('timezone', $filters['timezone']);
            });
        }

        $query->whereHas('roles', function ($roleQuery) {
            $roleQuery->where('name', 'tutor');
        });

        $query->with(['tutor', 'courses', 'languages']);

        // Sort the tutors and re-index
        $tutors = $query->get()
            ->sortByDesc(function ($tutor) {
                return $tutor->is_verified_tutor;
            })
            ->values();

        $perPage = $filters['per_page'] ?? 10;
        $currentPage = isset($filters['page']) ? (int) $filters['page'] : 1;

        $paginatedTutors = new LengthAwarePaginator(
            $tutors->forPage($currentPage, $perPage)->values(), // Re-index here explicitly
            $tutors->count(),
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );

        return $paginatedTutors->toArray();
    }



    public function create(array $data)
    {
        $user = User::find($data['user_id']);
        $user->assignRole('tutor');
        return Tutor::create($data);
    }

    public function update(array $data, $id)
    {

    }

    public function delete($id)
    {
        //
    }

    public function onboardTutorCourses($data)
    {
        //
    }

    public function onboardTutorCertification($data)
    {
        //
    }

    public function onboardTutorAvailability($data)
    {
        //
    }

    public function onboardTutorLanguage($data)
    {
        //
    }


    public function onboardTutorProfileInfo($data)
    {
        //
    }

    private function applyFilters(array $filters, Builder $query)
    {
        try {
            // Filter by price range
            if (isset($filters['min_price']) || isset($filters['max_price'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->when(isset($filters['min_price']), function ($q) use ($filters) {
                        $q->where('price', '>=', $filters['min_price']);
                    })->when(isset($filters['max_price']), function ($q) use ($filters) {
                        $q->where('price', '<=', $filters['max_price']);
                    });
                });
            }

            // Filter by language
            if (isset($filters['language'])) {
                $query->whereHas('languages', function ($languageQuery) use ($filters) {
                    $languageQuery->where('name', 'like', '%' . $filters['language'] . '%');
                });
            }

            // Filter by availability (on the tutor relation)
            if (isset($filters['availability'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('availability', $filters['availability']);
                });
            }

            // Filter by rating (on the tutor relation)
            if (isset($filters['min_rating'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('rating', '>=', $filters['min_rating']);
                });
            }

            // Filter by active status (on the tutor relation)
            if (isset($filters['is_active'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('is_active', $filters['is_active']);
                });
            }

            // Filter by specific country (on the user or tutor relation)
            if (isset($filters['country'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('country', $filters['country']);
                });
            }

            // Filter by timezone (on the tutor relation)
            if (isset($filters['timezone'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('timezone', $filters['timezone']);
                });
            }

            // Filter by tutor verification (on the user or tutor relation)
            if (isset($filters['is_verified_tutor'])) {
                $query->whereHas('tutor', function ($tutorQuery) use ($filters) {
                    $tutorQuery->where('is_verified_tutor', $filters['is_verified_tutor']);
                });
            }

        } catch (\Exception $e) {
            Log::error('Error applying filters: ' . $e->getMessage());
            throw $e;
        }
    }

    public function getTutorDashboardStats($tutorId): ?array
    {
        $tutor = Tutor::find($tutorId);

        if (!$tutor) {
            return null;
        }

        $userId = $tutor->user->id;

        Log::info('Tutor User ID: ' . $userId);

        $totalIncome = Lesson::where('tutor_id', $userId)
            ->where(function ($query) {
                $query->where('status', 'completed')
                    ->orWhere('confirmed', true);
            })
            ->get()
            ->sum(function ($lesson) use ($tutor) {
                return $lesson->duration ? ($lesson->duration / 60) * $tutor->price : $tutor->price;
            });

        $activeStudents = $tutor->students()
            ->wherePivot('status', 'active')
            ->count();

        $totalLessons = Lesson::where('tutor_id', $userId)->count();

        $totalHours = Lesson::where('tutor_id', $userId)
            ->where(function ($query) {
                $query->whereNotNull('duration')
                    ->where('duration', '>', 0)
                    ->orWhere(function ($query) {
                        $query->where('confirmed', true)
                            ->whereNotNull('starts_at')
                            ->whereNotNull('ends_at')
                            ->whereRaw('ends_at > starts_at');
                    });
            })
            ->get()
            ->reduce(function ($carry, $lesson) {
                if ($lesson->duration && $lesson->duration > 0) {
                    Log::info("Duration from lesson ID {$lesson->id}: {$lesson->duration} minutes");
                    return $carry + $lesson->duration;
                }
                if ($lesson->ends_at && $lesson->starts_at && $lesson->ends_at > $lesson->starts_at) {
                    $calculatedDuration = $lesson->starts_at->diffInMinutes($lesson->ends_at);
                    Log::info("Calculated duration from lesson ID {$lesson->id}: {$calculatedDuration} minutes");
                    return $carry + $calculatedDuration;
                }
                return $carry;
            }, 0) / 60;


        $totalSessions = Lesson::where('tutor_id', $userId)->count();

        $averageLessonPrice = $tutor->price;

        $upcomingLessons = Lesson::where('tutor_id', $userId)
            ->where('status', 'scheduled')
            ->orWhere('starts_at', '>', now())
            ->count();

        $canceledLessons = Lesson::where('tutor_id', $userId)
            ->where('status', 'canceled')
            ->count();

        $pastLessons = Lesson::where('tutor_id', $userId)
            ->where('ends_at', '<', now())
            ->count();

        $completedLessons = Lesson::where('tutor_id', $userId)
            ->where('status', 'completed')
            ->orWhere('confirmed', true)
            ->count();

        return [
            'totalIncome' => $totalIncome,
            'activeStudents' => $activeStudents,
            'totalLessons' => $totalLessons,
            'totalHours' => $totalHours,
            'totalSessions' => $totalSessions,
            'averageLessonPrice' => $averageLessonPrice,
            'upcomingLessons' => $upcomingLessons,
            'canceledLessons' => $canceledLessons,
            'completedLessons' => $completedLessons,
            'pastLessons' => $pastLessons,
        ];
    }







}
