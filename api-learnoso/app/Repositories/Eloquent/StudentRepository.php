<?php

namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Contracts\StudentRepositoryInterface;

class StudentRepository implements StudentRepositoryInterface
{
    protected $model;

    public function __construct(\App\Models\Student $student)
    {
        $this->model = $student;
    }

    public function onboardStudent(array $data)
    {

        return $this->model->updateOrCreate(
            ['user_id' => $data['user_id']], $data);
    }

    public function getStudent($id){
        return User::with('student')->find($id);
    }

    public function find($id){
        return $this->model->find($id);
    }

    public function findByEmail($email){
        return User::with('student')->where('email', $email)->first();
    }

    public function update(array $data, $id){
        return $this->model->find($id)->update($data);
    }

    public function delete($id){
        return $this->model->find($id)->delete();
    }

    public function getStudents(){
        return $this->model->with('user')->get();
    }

}
