<?php

namespace App\Repositories\Eloquent;

use App\Models\User;
use App\Repositories\Contracts\WalletRepositoryInterface;
use Bavix\Wallet\Models\Transaction;

class WalletRepository implements WalletRepositoryInterface
{
    public function getWallet(User $user)
    {
        return $user->load('wallet', 'student', 'tutor');
    }

    public function getTransactions(User $user, array $data)
    {

        $query = Transaction::where('payable_id', $user->id)
            ->where('payable_type', get_class($user));

        if (isset($data['type'])) {
            $query->where('type', $data['type']);
        }

        if (isset($data['start_date']) && isset($data['end_date'])) {
            $query->whereBetween('created_at', [$data['start_date'], $data['end_date']]);
        }

        // also check for a search query
        if (isset($data['search'])) {
            $query->where('uuid', 'like', '%' . $data['search'] . '%')
            ->orWhere('amount', 'like', '%' . $data['search'] . '%')
            ->orWhere('type', 'like', '%' . $data['search'] . '%');
        }
        return $query->orderBy('created_at', 'desc')
            ->get();
    }

    public function getAllTransactions($filters){
        $query = Transaction::query();

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        // also check for a search query
        if (isset($filters['search'])) {
            $query->where('uuid', 'like', '%' . $filters['search'] . '%')
            ->orWhere('amount', 'like', '%' . $filters['search'] . '%')
            ->orWhere('type', 'like', '%' . $filters['search'] . '%');
        }

        // paginate the results


        return $query->orderBy('created_at', 'desc')
            ->paginate($filters['per_page'] ?? 10);
    }
}
