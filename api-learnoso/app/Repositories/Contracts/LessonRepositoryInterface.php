<?php

namespace App\Repositories\Contracts;

use App\Models\Lesson;

interface LessonRepositoryInterface
{
    public function createLesson(array $lessonData);
    public function updateLesson(Lesson $lesson, array $data);
    public function deleteLesson(Lesson $lesson);
    public function find($lessonId);

    public function getLessonsByTutorId($tutorId);

    public function getLessonsByStudentId($studentId);


}
