<?php

namespace App\Repositories\Contracts;

use App\Http\Requests\CommentsRequest;
use App\Http\Requests\MaterialEvaluationRequest;
use App\Http\Requests\MaterialPrerequisiteRequest;
use App\Http\Requests\MaterialSubmissionRequest;
use App\Http\Requests\MaterialReviewsRequest;
use App\Http\Requests\UpdateCommentsRequest;
use Illuminate\Pagination\LengthAwarePaginator;

interface CourseMaterialRepositoryInterface
{
    public function all(array $filters = []): LengthAwarePaginator;
    public function find(int $id);
    public function create(array $data);
    public function update(int $id, array $data);
    public function delete(int $id);
    public function findByCourseId(int $course_id);
    public function findByTutorId(int $tutor_id);

    public function getPrerequisite(int $id, int $material_id);
    public function createPrerequisite(MaterialPrerequisiteRequest $request);
    public function updatePrerequisite(array $data);

    public function getCurriculum(int $id, int $material_id);
    public function createCurriculum(array $data);
    public function updateCurriculum(array $data);

    public function allEvaluation(int $material_id);
    public function getEvaluation(int $id);
    public function createEvaluation(MaterialEvaluationRequest $request);
    public function updateEvaluation(MaterialEvaluationRequest $request, $id);

    public function allSubmissions($data);
    public function getSubmission($data);
    public function createSubmission(MaterialSubmissionRequest $request);
    public function updateSubmission(MaterialSubmissionRequest $request, int $id);

    public function createReview(MaterialReviewsRequest $request);
    public function getReview(int $material_id);
    public function updateReview(MaterialReviewsRequest $request);
    public function deleteReview(int $id);
    public function createComment(CommentsRequest $request);
    public function updateComment(CommentsRequest $request);
    public function deleteComment(int $comment_id);
}

