<?php

namespace App\Repositories\Contracts;

use Illuminate\Http\Request;

interface ReviewsRepositoryInterface
{
    public function getReviewsByTutorId($tutorId);
    public function getReviewsByStudentId($studentId);
    public function createReview(array $reviewData);
    public function updateReview($reviewId, array $data);
    public function deleteReview($reviewId);
    public function find($id);
    public function all(Request $request);

    public function hasStudentReviewedTutor($studentId, $tutorId);
}
