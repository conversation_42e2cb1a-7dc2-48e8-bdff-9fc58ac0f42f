<?php

namespace App\Repositories\Contracts;
use Illuminate\Pagination\LengthAwarePaginator;


interface TutorRepositoryInterface
{

    public function getTutors($filters): array;

    public function getTutor($id);

    public function findByUserId($id);

    public function create(array $data);

    public function update(array $data, $id);

    public function delete($id);

    public function onboardTutorCourses($data);

    public function onboardTutorCertification($data);

    public function onboardTutorAvailability($data);

    public function onboardTutorLanguage($data);

    public function onboardTutorProfileInfo($data);

    public function getTutorDashboardStats($tutorId): array|null;

    public function getTutorApplicationStats(): array|null;

    public function getTutorApplications($filters): array;
}
