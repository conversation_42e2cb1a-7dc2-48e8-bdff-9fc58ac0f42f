<?php

namespace App\Observers;

use App\Services\AuditLogService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuditObserver
{
    protected AuditLogService $auditService;

    // Models that should be audited
    protected array $auditableModels = [
        'User', 'Lesson', 'Payment', 'Withdrawal',
        'StripeConnectedAccount', 'TutorReview', 'Course'
    ];

    // Fields to exclude from audit logs (sensitive data)
    protected array $excludedFields = [
        'password', 'remember_token', 'two_factor_secret',
        'api_token', 'stripe_secret', 'updated_at'
    ];

    public function __construct(AuditLogService $auditService)
    {
        $this->auditService = $auditService;
    }

    /**
     * Handle the model "created" event.
     */
    public function created(Model $model): void
    {
        if ($this->shouldAudit($model)) {
            try {
                $this->auditService->logDataAccess(
                    'created',
                    $model,
                    Auth::user(),
                    [],
                    $this->getCleanAttributes($model),
                    ['model_class' => get_class($model)]
                );
            } catch (\Exception $e) {
                Log::error('Audit observer error on created', [
                    'model' => get_class($model),
                    'id' => $model->getKey(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle the model "updated" event.
     */
    public function updated(Model $model): void
    {
        if ($this->shouldAudit($model)) {
            try {
                $oldValues = $this->getCleanAttributes($model, $model->getOriginal());
                $newValues = $this->getCleanAttributes($model, $model->getDirty());

                // Only log if there are actual changes
                if (!empty($newValues)) {
                    $this->auditService->logDataAccess(
                        'updated',
                        $model,
                        Auth::user(),
                        $oldValues,
                        $newValues,
                        [
                            'model_class' => get_class($model),
                            'changed_fields' => array_keys($newValues)
                        ]
                    );
                }
            } catch (\Exception $e) {
                Log::error('Audit observer error on updated', [
                    'model' => get_class($model),
                    'id' => $model->getKey(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle the model "deleted" event.
     */
    public function deleted(Model $model): void
    {
        if ($this->shouldAudit($model)) {
            try {
                $this->auditService->logDataAccess(
                    'deleted',
                    $model,
                    Auth::user(),
                    $this->getCleanAttributes($model),
                    [],
                    [
                        'model_class' => get_class($model),
                        'soft_delete' => method_exists($model, 'trashed') && !$model->isForceDeleting()
                    ]
                );
            } catch (\Exception $e) {
                Log::error('Audit observer error on deleted', [
                    'model' => get_class($model),
                    'id' => $model->getKey(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle the model "restored" event.
     */
    public function restored(Model $model): void
    {
        if ($this->shouldAudit($model)) {
            try {
                $this->auditService->logDataAccess(
                    'restored',
                    $model,
                    Auth::user(),
                    [],
                    $this->getCleanAttributes($model),
                    ['model_class' => get_class($model)]
                );
            } catch (\Exception $e) {
                Log::error('Audit observer error on restored', [
                    'model' => get_class($model),
                    'id' => $model->getKey(),
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Check if the model should be audited
     */
    protected function shouldAudit(Model $model): bool
    {
        $className = class_basename($model);
        return in_array($className, $this->auditableModels);
    }

    /**
     * Get clean attributes (excluding sensitive fields)
     */
    protected function getCleanAttributes(Model $model, array $attributes = null): array
    {
        $attributes = $attributes ?? $model->getAttributes();

        // Remove excluded fields
        foreach ($this->excludedFields as $field) {
            unset($attributes[$field]);
        }

        // Remove null values
        return array_filter($attributes, function ($value) {
            return $value !== null;
        });
    }
}
