<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register()
    {
        $this->app->bind(
            \App\Repositories\Contracts\UserRepositoryInterface::class,
            \App\Repositories\Eloquent\UserRepository::class,
        );

        $this->app->bind(
            \App\Repositories\Contracts\StudentRepositoryInterface::class,
            \App\Repositories\Eloquent\StudentRepository::class,
        );


        $this->app->bind(
            \App\Repositories\Contracts\CourseRepositoryInterface::class,
            \App\Repositories\Eloquent\CourseRepository::class,
        );


        $this->app->bind(
            \App\Repositories\Contracts\CourseMaterialRepositoryInterface::class,
            \App\Repositories\Eloquent\CourseMaterialRepository::class,
        );


        $this->app->bind(
            \App\Repositories\Contracts\TutorRepositoryInterface::class,
            \App\Repositories\Eloquent\TutorRepository::class,
        );


        $this->app->bind(
            \App\Repositories\Contracts\EducationRepositoryInterface::class,
            \App\Repositories\Eloquent\EducationRepository::class,
        );


        $this->app->bind(
            \App\Repositories\Contracts\WalletRepositoryInterface::class,
            \App\Repositories\Eloquent\WalletRepository::class,
        );

        $this->app->bind(
            \App\Repositories\Contracts\LessonRepositoryInterface::class,
            \App\Repositories\Eloquent\LessonRepository::class
        );

        $this->app->bind(
            \App\Repositories\Contracts\ReviewsRepositoryInterface::class,
            \App\Repositories\Eloquent\ReviewsRepository::class
        );
    }


    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register audit observer for sensitive models
        $auditableModels = [
            \App\Models\User::class,
            \App\Models\Lesson::class,
            \App\Models\StripeConnectedAccount::class,
            \App\Models\TutorReview::class,
            \App\Models\Course::class,
            
            // Add other models as needed when they exist
        ];

        foreach ($auditableModels as $model) {
            if (class_exists($model)) {
                $model::observe(\App\Observers\AuditObserver::class);
            }
        }
    }
}
