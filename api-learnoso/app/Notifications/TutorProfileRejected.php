<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class TutorProfileRejected extends Notification implements ShouldQueue
{
    use Queueable;

    protected $tutor;
    protected $reason;

    /**
     * Create a new notification instance.
     */
    public function __construct($tutor, $reason)
    {
        $this->tutor = $tutor;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('❌ Your Tutor Profile Was Rejected')
            ->view('emails.profile_rejected', [
                'tutor' => $this->tutor,
                'reason' => $this->reason,
                'profileUpdateUrl' => 'https://www.learnoso.com/tutor/profile',
            ]);
    }
}
