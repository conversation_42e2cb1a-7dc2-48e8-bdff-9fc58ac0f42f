<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class TutorProfileApproved extends Notification implements ShouldQueue
{
    use Queueable;

    protected $tutor;

    /**
     * Create a new notification instance.
     */
    public function __construct($tutor)
    {
        $this->tutor = $tutor;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('🎉 Your Tutor Profile is Approved!')
            ->view('emails.profile_approved', [
                'tutor' => $this->tutor,
                'dashboardUrl' => 'https://www.learnoso.com/tutor/dashboard',
                ]);
    }
}
