<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $tier = 'default'): Response
    {
        $user = Auth::user();
        $key = $this->getRateLimitKey($request, $user, $tier);

        $limits = $this->getRateLimits($tier, $user);

        // Check rate limit
        $executed = RateLimiter::attempt(
            $key,
            $limits['attempts'],
            function () use ($next, $request) {
                return $next($request);
            },
            $limits['decay']
        );

        if (!$executed) {
            $retryAfter = RateLimiter::availableIn($key);

            // Log rate limit hit
            Log::warning('Rate limit exceeded', [
                'user_id' => $user?->id,
                'ip' => $request->ip(),
                'endpoint' => $request->path(),
                'tier' => $tier,
                'retry_after' => $retryAfter
            ]);

            return response()->json([
                'error' => 'Too Many Requests',
                'message' => 'Rate limit exceeded. Please try again later.',
                'retry_after' => $retryAfter,
                'limit' => $limits['attempts'],
                'window' => $limits['decay']
            ], 429)->header('Retry-After', $retryAfter);
        }

        // Add rate limit headers to response
        $response = $executed;
        $remaining = RateLimiter::remaining($key, $limits['attempts']);

        return $response->withHeaders([
            'X-RateLimit-Limit' => $limits['attempts'],
            'X-RateLimit-Remaining' => $remaining,
            'X-RateLimit-Reset' => now()->addSeconds($limits['decay'])->timestamp,
        ]);
    }

    /**
     * Generate rate limit key
     */
    private function getRateLimitKey(Request $request, $user, string $tier): string
    {
        if ($user) {
            return "api_rate_limit:{$tier}:user:{$user->id}";
        }

        return "api_rate_limit:{$tier}:ip:{$request->ip()}";
    }

    /**
     * Get rate limits based on tier and user role
     */
    private function getRateLimits(string $tier, $user): array
    {
        $userRole = $user?->getRoleNames()->first() ?? 'guest';

        $baseLimits = [
            'auth' => [
                'attempts' => 5,
                'decay' => 300 // 5 minutes
            ],
            'chat' => [
                'attempts' => 100,
                'decay' => 60 // 1 minute
            ],
            'api' => [
                'attempts' => 60,
                'decay' => 60 // 1 minute
            ],
            'upload' => [
                'attempts' => 10,
                'decay' => 300 // 5 minutes
            ],
            'payment' => [
                'attempts' => 3,
                'decay' => 600 // 10 minutes
            ],
            'default' => [
                'attempts' => 60,
                'decay' => 60 // 1 minute
            ]
        ];

        $limits = $baseLimits[$tier] ?? $baseLimits['default'];

        // Adjust limits based on user role
        if ($user) {
            $multipliers = [
                'admin' => 5.0,
                'tutor' => 2.0,
                'student' => 1.5,
                'guest' => 1.0
            ];

            $multiplier = $multipliers[$userRole] ?? 1.0;
            $limits['attempts'] = (int) ($limits['attempts'] * $multiplier);
        }

        return $limits;
    }
}
