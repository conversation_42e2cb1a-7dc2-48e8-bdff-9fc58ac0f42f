<?php

namespace App\Http\Middleware;

use App\Services\RealTimeChatService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class UpdateUserPresence
{
    protected RealTimeChatService $realTimeChatService;

    public function __construct(RealTimeChatService $realTimeChatService)
    {
        $this->realTimeChatService = $realTimeChatService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Update user presence if authenticated
        if (Auth::check()) {
            $user = Auth::user();

            // Update online status (this will extend the TTL)
            $this->realTimeChatService->updateUserOnlineStatus($user->id, true);
        }

        return $response;
    }
}
