<?php

namespace App\Http\Middleware;

use App\Services\AuditLogService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    protected AuditLogService $auditService;

    // Sensitive endpoints that should be logged
    protected array $sensitiveEndpoints = [
        // Authentication
        'POST:login', 'POST:logout', 'POST:register',

        // 2FA
        'POST:2fa/setup', 'POST:2fa/confirm', 'POST:2fa/disable',

        // Password changes
        'POST:change-password', 'POST:password/reset',

        // Payment operations
        'POST:payments/initiate', 'POST:wallet/withdraw',

        // Admin actions
        'POST:admin/*', 'PUT:admin/*', 'DELETE:admin/*',

        // User data modifications
        'PUT:profile', 'POST:profile-image',

        // Role switching
        'POST:role/switch',

        // Lesson management
        'POST:lessons/schedule', 'POST:lessons/confirm',

        // Data exports
        'GET:admin/all-transactions', 'GET:admin/users',
    ];

    // High-risk actions that require special attention
    protected array $highRiskActions = [
        'DELETE', 'PUT:admin/*', 'POST:admin/*',
        'POST:wallet/withdraw', 'POST:2fa/disable'
    ];

    public function __construct(AuditLogService $auditService)
    {
        $this->auditService = $auditService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log after the request is processed
        $this->logRequest($request, $response);

        return $response;
    }

    /**
     * Log the request if it matches sensitive criteria
     */
    protected function logRequest(Request $request, Response $response): void
    {
        try {
            $method = $request->method();
            $path = $request->path();
            $endpoint = "{$method}:{$path}";
            $statusCode = $response->getStatusCode();

            // Check if this endpoint should be logged
            if ($this->shouldLog($endpoint, $method, $path)) {
                $user = Auth::user();

                // Determine risk level
                $riskLevel = $this->determineRiskLevel($endpoint, $method, $statusCode);

                // Create audit log
                $this->auditService->logApiAccess(
                    $path,
                    $method,
                    $user,
                    $statusCode,
                    [
                        'route_name' => $request->route()?->getName(),
                        'controller_action' => $this->getControllerAction($request),
                        'response_size' => strlen($response->getContent()),
                        'processing_time' => $this->getProcessingTime($request)
                    ]
                );

                // Log specific actions based on endpoint
                $this->logSpecificAction($request, $response, $user);
            }
        } catch (\Exception $e) {
            // Don't let audit logging break the application
            Log::error('Audit middleware error', [
                'error' => $e->getMessage(),
                'request' => $request->path()
            ]);
        }
    }

    /**
     * Determine if this request should be logged
     */
    protected function shouldLog(string $endpoint, string $method, string $path): bool
    {
        // Log all sensitive endpoints
        foreach ($this->sensitiveEndpoints as $pattern) {
            if ($this->matchesPattern($endpoint, $pattern)) {
                return true;
            }
        }

        // Log all admin routes
        if (str_starts_with($path, 'admin/')) {
            return true;
        }

        // Log all DELETE operations
        if ($method === 'DELETE') {
            return true;
        }

        // Log failed requests (4xx, 5xx)
        return false;
    }

    /**
     * Check if endpoint matches pattern
     */
    protected function matchesPattern(string $endpoint, string $pattern): bool
    {
        // Convert pattern to regex
        $regex = str_replace('*', '.*', preg_quote($pattern, '/'));
        return preg_match("/^{$regex}$/", $endpoint) === 1;
    }

    /**
     * Determine risk level based on endpoint and response
     */
    protected function determineRiskLevel(string $endpoint, string $method, int $statusCode): string
    {
        // Failed requests are higher risk
        if ($statusCode >= 400) {
            return $statusCode >= 500 ? 'high' : 'medium';
        }

        // Check high-risk actions
        foreach ($this->highRiskActions as $pattern) {
            if ($this->matchesPattern($endpoint, $pattern)) {
                return 'high';
            }
        }

        // Admin actions are medium risk
        if (str_contains($endpoint, 'admin/')) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Log specific actions based on the endpoint
     */
    protected function logSpecificAction(Request $request, Response $response, $user): void
    {
        $path = $request->path();
        $method = $request->method();
        $statusCode = $response->getStatusCode();
        $status = $statusCode >= 200 && $statusCode < 300 ? 'success' : 'failed';

        // Authentication actions
        if ($path === 'login' && $method === 'POST') {
            $metadata = [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'failed_attempts' => $user ? $user->failed_login_attempts : 0
            ];

            $this->auditService->logAuth('login', $user, $status, $metadata);
        }

        // 2FA actions
        if (str_starts_with($path, '2fa/')) {
            $action = match(true) {
                str_contains($path, 'setup') => '2fa_setup',
                str_contains($path, 'confirm') => '2fa_enabled',
                str_contains($path, 'disable') => '2fa_disabled',
                default => '2fa_action'
            };

            $this->auditService->logAuth($action, $user, $status);
        }

        // Payment actions
        if (str_starts_with($path, 'payments/') || str_starts_with($path, 'wallet/')) {
            $amount = $request->input('amount', 0);
            $currency = $request->input('currency', 'USD');

            if ($amount > 0) {
                $action = str_contains($path, 'withdraw') ? 'withdrawal' : 'payment';
                $this->auditService->logPayment($action, $user, $amount, $currency, $status);
            }
        }

        // Admin actions
        if (str_starts_with($path, 'admin/') && $user && $user->hasRole(['admin', 'super-admin'])) {
            $this->auditService->logAdminAction(
                $method,
                "Admin {$method} action on {$path}",
                $user,
                null,
                ['endpoint' => $path, 'status_code' => $statusCode]
            );
        }

        // Role switching
        if ($path === 'role/switch' && $method === 'POST') {
            $newRole = $request->input('role');
            $this->auditService->logSecurityEvent(
                'role_changed',
                'updated',
                "User switched role to: {$newRole}",
                $user,
                'medium',
                ['new_role' => $newRole, 'previous_role' => $user->currentRole?->role?->name]
            );
        }
    }

    /**
     * Get controller action from request
     */
    protected function getControllerAction(Request $request): ?string
    {
        $route = $request->route();
        if (!$route) {
            return null;
        }

        $action = $route->getActionName();
        return $action !== 'Closure' ? $action : null;
    }

    /**
     * Calculate processing time
     */
    protected function getProcessingTime(Request $request): ?float
    {
        if (defined('LARAVEL_START')) {
            return round((microtime(true) - LARAVEL_START) * 1000, 2);
        }

        return null;
    }
}
