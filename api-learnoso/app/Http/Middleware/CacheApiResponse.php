<?php

namespace App\Http\Middleware;

use App\Services\CacheService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class CacheApiResponse
{
    protected CacheService $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, int $ttl = 1800): BaseResponse
    {
        // Only cache GET requests
        if ($request->method() !== 'GET') {
            return $next($request);
        }

        // Skip caching for authenticated requests (except specified endpoints)
        if ($request->user() && !$this->shouldCacheAuthenticatedRequest($request)) {
            return $next($request);
        }

        // Generate cache key
        $cacheKey = $this->generateCacheKey($request);

        // Try to get cached response
        $cachedResponse = $this->cacheService->getApiResponse($cacheKey);

        if ($cachedResponse !== null) {
            return response()->json($cachedResponse['data'], $cachedResponse['status'])
                ->withHeaders($cachedResponse['headers'] ?? [])
                ->header('X-Cache', 'HIT');
        }

        // Execute request
        $response = $next($request);

        // Only cache successful responses
        if ($this->shouldCacheResponse($response)) {
            $responseData = [
                'data' => json_decode($response->getContent(), true),
                'status' => $response->getStatusCode(),
                'headers' => $this->getCacheableHeaders($response)
            ];

            $this->cacheService->cacheApiResponse($cacheKey, [], $responseData, $ttl);
        }

        $response->headers->set('X-Cache', 'MISS');
        return $response;
    }

    /**
     * Generate a unique cache key for the request
     */
    private function generateCacheKey(Request $request): string
    {
        $key = $request->getPathInfo();

        // Include query parameters
        if ($request->getQueryString()) {
            $key .= '?' . $request->getQueryString();
        }

        return md5($key);
    }

    /**
     * Determine if authenticated request should be cached
     */
    private function shouldCacheAuthenticatedRequest(Request $request): bool
    {
        $cacheableAuthEndpoints = [
            '/api/courses',
            '/api/languages',
            '/api/tutor',
            '/api/settings/commission'
        ];

        foreach ($cacheableAuthEndpoints as $endpoint) {
            if (str_starts_with($request->getPathInfo(), $endpoint)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine if response should be cached
     */
    private function shouldCacheResponse($response): bool
    {
        // Only cache successful responses
        if ($response->getStatusCode() !== 200) {
            return false;
        }

        // Don't cache if response contains errors
        $content = json_decode($response->getContent(), true);
        if (isset($content['error']) || isset($content['errors'])) {
            return false;
        }

        return true;
    }

    /**
     * Get headers that should be cached
     */
    private function getCacheableHeaders($response): array
    {
        $cacheableHeaders = [
            'Content-Type',
            'Content-Language'
        ];

        $headers = [];
        foreach ($cacheableHeaders as $header) {
            if ($response->headers->has($header)) {
                $headers[$header] = $response->headers->get($header);
            }
        }

        return $headers;
    }
}
