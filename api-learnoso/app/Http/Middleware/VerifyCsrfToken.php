<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;
use Symfony\Component\HttpFoundation\Response;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     */
    protected $except = [
        // Webhook endpoints that receive external requests
        'agora/webhook/*',
        'paypal/callback',
        'stripe/callback',

        // API routes are already protected by Sanctum
        'api/*',

        // Health check and monitoring endpoints
        'up',
        'health',

        // Development and testing endpoints
        'telescope/*',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle($request, Closure $next)
    {
        // Skip CSRF for API requests (they use Sanctum tokens)
        if ($request->is('api/*')) {
            return $next($request);
        }

        // Skip CSRF for webhook endpoints
        if ($this->shouldExclude($request)) {
            return $next($request);
        }

        // Apply CSRF protection for web routes
        return parent::handle($request, $next);
    }

    /**
     * Determine if the request should be excluded from CSRF verification
     */
    protected function shouldExclude($request): bool
    {
        foreach ($this->except as $except) {
            if ($request->is($except)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get the CSRF token from the request
     */
    protected function getTokenFromRequest($request)
    {
        $token = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');

        if (!$token && $header = $request->header('X-XSRF-TOKEN')) {
            $token = $this->encrypter->decrypt($header, static::serialized());
        }

        return $token;
    }

    /**
     * Add the CSRF token to the response
     */
    public function addCookieToResponse($request, $response)
    {
        $config = config('session');

        if ($response instanceof \Illuminate\Http\Response) {
            $response->withCookie(
                cookie(
                    'XSRF-TOKEN',
                    $request->session()->token(),
                    $config['lifetime'],
                    $config['path'],
                    $config['domain'],
                    $config['secure'],
                    false, // Make accessible to JavaScript
                    false,
                    $config['same_site'] ?? null
                )
            );
        }

        return $response;
    }
}
