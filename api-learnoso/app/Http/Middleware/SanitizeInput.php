<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SanitizeInput
{
    /**
     * Fields that should not be sanitized (preserve original content)
     */
    protected array $skipSanitization = [
        'password',
        'password_confirmation',
        'current_password',
        'new_password',
        '_token',
        'api_key',
        'secret',
    ];

    /**
     * Fields that allow HTML content (limited sanitization)
     */
    protected array $allowHtmlFields = [
        'description',
        'content',
        'bio',
        'about',
        'message_content',
        'review_content',
        'course_description',
        'lesson_notes',
    ];

    /**
     * Dangerous patterns that indicate potential XSS attacks
     */
    protected array $xssPatterns = [
        '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi',
        '/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi',
        '/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi',
        '/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi',
        '/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload\s*=/i',
        '/onerror\s*=/i',
        '/onclick\s*=/i',
        '/onmouseover\s*=/i',
        '/onsubmit\s*=/i',
        '/onfocus\s*=/i',
        '/onblur\s*=/i',
        '/eval\s*\(/i',
        '/expression\s*\(/i',
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only sanitize for web requests and specific API endpoints
        if ($this->shouldSanitize($request)) {
            $this->sanitizeRequest($request);
        }

        $response = $next($request);

        // Add security headers to prevent XSS
        return $this->addSecurityHeaders($response);
    }

    /**
     * Determine if the request should be sanitized
     */
    protected function shouldSanitize(Request $request): bool
    {
        // Always sanitize web requests
        if (!$request->is('api/*')) {
            return true;
        }

        // Sanitize API endpoints that handle user content
        $sanitizeEndpoints = [
            'api/profile*',
            'api/lessons*',
            'api/courses*',
            'api/reviews*',
            'api/chat*',
            'api/course-material*',
            'api/admin*',
        ];

        foreach ($sanitizeEndpoints as $endpoint) {
            if ($request->is($endpoint)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Sanitize the request inputs
     */
    protected function sanitizeRequest(Request $request): void
    {
        try {
            $input = $request->all();
            $sanitized = $this->sanitizeArray($input);
            $request->replace($sanitized);
        } catch (\Exception $e) {
            Log::error('Input sanitization error', [
                'error' => $e->getMessage(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip()
            ]);
        }
    }

    /**
     * Recursively sanitize an array of data
     */
    protected function sanitizeArray(array $data): array
    {
        $sanitized = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitizeArray($value);
            } elseif (is_string($value)) {
                $sanitized[$key] = $this->sanitizeValue($key, $value);
            } else {
                $sanitized[$key] = $value;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize a single value based on field type
     */
    protected function sanitizeValue(string $key, string $value): string
    {
        // Skip sanitization for specified fields
        if (in_array($key, $this->skipSanitization)) {
            return $value;
        }

        // Check for potential XSS attacks
        if ($this->containsXss($value)) {
            Log::warning('Potential XSS attempt detected', [
                'field' => $key,
                'value' => substr($value, 0, 200), // Log first 200 chars
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent()
            ]);
        }

        // Different sanitization based on field type
        if (in_array($key, $this->allowHtmlFields)) {
            return $this->sanitizeHtml($value);
        }

        // Default: strip all HTML and sanitize
        return $this->sanitizeText($value);
    }

    /**
     * Check if value contains potential XSS
     */
    protected function containsXss(string $value): bool
    {
        foreach ($this->xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Sanitize HTML content (preserve safe HTML tags)
     */
    protected function sanitizeHtml(string $value): string
    {
        // Remove dangerous elements completely
        foreach ($this->xssPatterns as $pattern) {
            $value = preg_replace($pattern, '', $value);
        }

        // Allow only safe HTML tags
        $allowedTags = '<p><br><strong><b><em><i><u><ul><ol><li><a><h1><h2><h3><h4><h5><h6><blockquote>';
        $value = strip_tags($value, $allowedTags);

        // Sanitize attributes in remaining tags
        $value = preg_replace('/(<[^>]+)\s+(on\w+|javascript:|vbscript:)[^>]*>/i', '$1>', $value);

        // Remove any remaining script-like content
        $value = preg_replace('/javascript:/i', '', $value);
        $value = preg_replace('/vbscript:/i', '', $value);

        return trim($value);
    }

    /**
     * Sanitize plain text (remove all HTML)
     */
    protected function sanitizeText(string $value): string
    {
        // Strip all HTML tags
        $value = strip_tags($value);

        // Remove null bytes
        $value = str_replace("\0", '', $value);

        // Decode HTML entities
        $value = html_entity_decode($value, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Re-encode special characters
        $value = htmlspecialchars($value, ENT_QUOTES | ENT_HTML5, 'UTF-8', false);

        // Trim whitespace
        return trim($value);
    }

    /**
     * Add security headers to response
     */
    protected function addSecurityHeaders(Response $response): Response
    {
        $headers = [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => $this->getContentSecurityPolicy(),
        ];

        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }

        return $response;
    }

    /**
     * Get Content Security Policy header value
     */
    protected function getContentSecurityPolicy(): string
    {
        $directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.pusher.com https://cdn.jsdelivr.net",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net",
            "font-src 'self' https://fonts.gstatic.com",
            "img-src 'self' data: https:",
            "connect-src 'self' https://ws.pusherapp.com wss://ws.pusherapp.com",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'",
        ];

        return implode('; ', $directives);
    }

    /**
     * Log suspicious activity
     */
    protected function logSuspiciousActivity(string $field, string $value, string $reason): void
    {
        Log::warning('Suspicious input detected', [
            'field' => $field,
            'reason' => $reason,
            'value_preview' => substr($value, 0, 100),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'user_id' => Auth::id(),
        ]);
    }
}
