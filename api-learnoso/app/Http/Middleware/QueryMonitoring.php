<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class QueryMonitoring
{
    private $queryCount = 0;
    private $totalQueryTime = 0;
    private $slowQueries = [];
    private $duplicateQueries = [];
    private $queryLog = [];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, int $slowThreshold = 100): Response
    {
        // Only monitor in non-production environments or when explicitly enabled
        if (!$this->shouldMonitor()) {
            return $next($request);
        }

        // Start monitoring
        $this->startMonitoring($slowThreshold);

        $response = $next($request);

        // Stop monitoring and log results
        $this->stopMonitoringAndLog($request, $response);

        return $response;
    }

    /**
     * Determine if query monitoring should be enabled
     */
    private function shouldMonitor(): bool
    {
        return config('app.debug') ||
               config('app.env') !== 'production' ||
               config('database.query_monitoring', false);
    }

    /**
     * Start database query monitoring
     */
    private function startMonitoring(int $slowThreshold): void
    {
        $this->queryCount = 0;
        $this->totalQueryTime = 0;
        $this->slowQueries = [];
        $this->duplicateQueries = [];
        $this->queryLog = [];

        DB::listen(function ($query) use ($slowThreshold) {
            $this->queryCount++;
            $this->totalQueryTime += $query->time;

            $normalizedSql = $this->normalizeQuery($query->sql);

            // Track query details
            $queryData = [
                'sql' => $query->sql,
                'bindings' => $query->bindings,
                'time' => $query->time,
                'normalized' => $normalizedSql
            ];

            $this->queryLog[] = $queryData;

            // Check for slow queries
            if ($query->time > $slowThreshold) {
                $this->slowQueries[] = $queryData;
            }

            // Check for duplicate queries
            $this->trackDuplicateQueries($normalizedSql, $queryData);
        });
    }

    /**
     * Stop monitoring and log the results
     */
    private function stopMonitoringAndLog(Request $request, Response $response): void
    {
        DB::flushQueryLog();

        $endpoint = $request->getPathInfo();
        $method = $request->getMethod();
        $statusCode = $response->getStatusCode();

        // Prepare monitoring data
        $monitoringData = [
            'endpoint' => "{$method} {$endpoint}",
            'status_code' => $statusCode,
            'query_count' => $this->queryCount,
            'total_query_time' => round($this->totalQueryTime, 2),
            'avg_query_time' => $this->queryCount > 0 ? round($this->totalQueryTime / $this->queryCount, 2) : 0,
            'slow_queries_count' => count($this->slowQueries),
            'duplicate_queries_count' => count($this->duplicateQueries),
            'memory_usage' => round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB'
        ];

        // Add response headers for debugging
        $response->headers->set('X-DB-Query-Count', $this->queryCount);
        $response->headers->set('X-DB-Query-Time', $this->totalQueryTime . 'ms');

        // Log performance issues
        $this->logPerformanceIssues($monitoringData);

        // Log slow queries
        $this->logSlowQueries($endpoint);

        // Log duplicate queries
        $this->logDuplicateQueries($endpoint);

        // Log summary for high query endpoints
        if ($this->queryCount > 10 || $this->totalQueryTime > 500) {
            Log::warning('High database usage detected', $monitoringData);
        }
    }

    /**
     * Normalize SQL query for duplicate detection
     */
    private function normalizeQuery(string $sql): string
    {
        // Remove parameter placeholders and normalize whitespace
        $normalized = preg_replace('/\?/', '?', $sql);
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        $normalized = trim($normalized);

        return strtolower($normalized);
    }

    /**
     * Track duplicate queries
     */
    private function trackDuplicateQueries(string $normalizedSql, array $queryData): void
    {
        if (!isset($this->duplicateQueries[$normalizedSql])) {
            $this->duplicateQueries[$normalizedSql] = [
                'count' => 0,
                'total_time' => 0,
                'example' => $queryData
            ];
        }

        $this->duplicateQueries[$normalizedSql]['count']++;
        $this->duplicateQueries[$normalizedSql]['total_time'] += $queryData['time'];
    }

    /**
     * Log performance issues
     */
    private function logPerformanceIssues(array $data): void
    {
        $issues = [];

        // Too many queries (N+1 problem indicator)
        if ($data['query_count'] > 20) {
            $issues[] = "High query count: {$data['query_count']} queries";
        }

        // Slow total time
        if ($data['total_query_time'] > 1000) {
            $issues[] = "Slow total query time: {$data['total_query_time']}ms";
        }

        // Slow average query time
        if ($data['avg_query_time'] > 50) {
            $issues[] = "Slow average query time: {$data['avg_query_time']}ms";
        }

        // High memory usage
        if (str_replace('MB', '', $data['memory_usage']) > 128) {
            $issues[] = "High memory usage: {$data['memory_usage']}";
        }

        if (!empty($issues)) {
            Log::warning('Database performance issues detected', [
                'endpoint' => $data['endpoint'],
                'issues' => $issues,
                'stats' => $data
            ]);
        }
    }

    /**
     * Log slow queries with details
     */
    private function logSlowQueries(string $endpoint): void
    {
        if (empty($this->slowQueries)) {
            return;
        }

        foreach ($this->slowQueries as $query) {
            Log::warning('Slow query detected', [
                'endpoint' => $endpoint,
                'sql' => $query['sql'],
                'bindings' => $query['bindings'],
                'time' => $query['time'] . 'ms',
                'suggestion' => $this->getSuggestionForQuery($query['sql'])
            ]);
        }
    }

    /**
     * Log duplicate queries
     */
    private function logDuplicateQueries(string $endpoint): void
    {
        $significantDuplicates = array_filter($this->duplicateQueries, function($duplicate) {
            return $duplicate['count'] > 2; // Only log if query is executed more than 2 times
        });

        if (empty($significantDuplicates)) {
            return;
        }

        foreach ($significantDuplicates as $normalizedSql => $duplicate) {
            Log::warning('Duplicate queries detected (possible N+1 problem)', [
                'endpoint' => $endpoint,
                'query' => $duplicate['example']['sql'],
                'execution_count' => $duplicate['count'],
                'total_time' => $duplicate['total_time'] . 'ms',
                'suggestion' => 'Consider using eager loading or query optimization'
            ]);
        }
    }

    /**
     * Get optimization suggestions for queries
     */
    private function getSuggestionForQuery(string $sql): string
    {
        $sql = strtolower($sql);

        if (strpos($sql, 'select') === 0) {
            if (strpos($sql, 'where') === false) {
                return 'Consider adding WHERE clause to limit results';
            }

            if (strpos($sql, 'order by') !== false && strpos($sql, 'limit') === false) {
                return 'Consider adding LIMIT clause when using ORDER BY';
            }

            if (preg_match('/select \* from/', $sql)) {
                return 'Consider selecting only needed columns instead of SELECT *';
            }
        }

        if (strpos($sql, 'join') !== false) {
            return 'Ensure joined tables have proper indexes on join columns';
        }

        if (strpos($sql, 'like') !== false) {
            return 'LIKE queries can be slow - consider full-text search for text searches';
        }

        return 'Consider adding database indexes for better performance';
    }

    /**
     * Get detailed query analysis
     */
    public function getQueryAnalysis(): array
    {
        return [
            'total_queries' => $this->queryCount,
            'total_time' => $this->totalQueryTime,
            'slow_queries' => $this->slowQueries,
            'duplicate_queries' => $this->duplicateQueries,
            'query_log' => $this->queryLog
        ];
    }
}
