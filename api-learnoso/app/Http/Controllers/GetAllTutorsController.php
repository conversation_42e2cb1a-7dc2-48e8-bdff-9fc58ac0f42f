<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Services\TutorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GetAllTutorsController extends ApiBaseController
{
    protected $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }


    public function __invoke(Request $request)
    {
        try {
            $tutors = $this->tutorService->getAllTutors($request->all());
            return $this->success($tutors, 'Tutors fetched successfully');
        } catch (\Exception $e) {
            Log::error("Error Fetching tutors: ". $e->getMessage());
            return $this->error('Error fetching tutors'. $e->getMessage());
        }
    }

}
