<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Http\Requests\CommentsRequest;
use App\Http\Requests\MaterialReviewsRequest;
use App\Http\Requests\UpdateCommentsRequest;
use App\Models\Comments;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Illuminate\Http\Request;
class CommentsController extends ApiBaseController
{
    /**
     * Display a listing of the resource.
     */
      protected CourseMaterialRepositoryInterface $courseMaterialRepository;

    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository){
        $this->courseMaterialRepository = $courseMaterialRepository;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(CommentsRequest $request){
        $comment = $this->courseMaterialRepository->createComment($request);
        if($comment['status']){
            return $this->success($comment['data'], message: 'Comment created successfully');
        }
        else{
            return $this->error(message:$comment['message']);
        }
    }

     public function update(CommentsRequest $request){
        $comment= $this->courseMaterialRepository->updateComment($request);
        if($comment['status']){
            return $this->success($comment['data'], message:'Comment updated successfully');
        }
        else{
            return $this->error($comment['message']);
        }
    }


     public function destroy(MaterialReviewsRequest $request){
        $result = $this->courseMaterialRepository->deleteComment($request->id);
        if($result['status']){
            return $this->success(message:'Successfully Deleted comment');
        }
        else{
            return $this->error(message:'Failed to Delete');
        }
    }
}


