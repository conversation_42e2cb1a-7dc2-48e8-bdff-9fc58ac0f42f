<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Services\TutorService;
use Illuminate\Http\JsonResponse;

class ViewTutorController extends ApiBaseController
{
    private $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }

    /**
     * Handle the incoming request.
     */
    public function __invoke($id)
    {
        $tutor = $this->tutorService->getTutorById($id);

        if (!$tutor) {
            return $this->error('No Tutor found with given id', JsonResponse::HTTP_NOT_FOUND);
        }

        return $this->success(data: $tutor, message: '<PERSON><PERSON> fetched');
    }
}
