<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\UpdateProfileImageRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Services\UploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ProfileController extends ApiBaseController
{
    public function updateProfile(UpdateProfileRequest $request)
    {
        $user = Auth::user();
        $user->update($request->validated());

        return $this->success($user, 'Profile updated successfully.');
    }

    public function updateProfileImage(UpdateProfileImageRequest $request)
    {
        $user = Auth::user();
        $imagePath = (new UploadService())->uploadFile($request->validated()['image'], 'profile_images');

        if ($user->currentRole->name === 'tutor') {
            $user->tutor->update(['profile_image' => $imagePath]);
        } elseif ($user->currentRole->name === 'student') {
            $user->student->update(['profile_image' => $imagePath]);
        }

        return $this->success($user->load('tutor', 'student'), 'Profile image updated successfully.');
    }

    public function changePassword(ChangePasswordRequest $request)
    {
        $user = Auth::user();

        // Check if the old password matches
        if (!Hash::check($request->old_password, $user->password)) {
            return $this->error('Old password is incorrect.', 400);
        }

        // Update the password
        $user->update(['password' => Hash::make($request->new_password)]);

        return $this->success(null, 'Password updated successfully.');
    }

    public function getProfile()
    {
        $user = Auth::user();
        return $this->success($user->load('tutor', 'student', 'courses', 'wallet'), 'Profile fetched successfully.');
    }

    public function switchRole(Request $request)
{
    $validator = Validator::make($request->all(), [
        'role' => 'required|exists:roles,name',
    ]);

    if ($validator->fails()) {
        return $this->error($validator->errors()->first(), 400);
    }

    $user = Auth::user();
    $newRole = $request->role;


    $user->switchRole($newRole);

    // Log the user back in to refresh the session
    Auth::logout();
    Auth::login($user);

    // Regenerate token (if you're using token-based authentication)
    $user->tokens()->delete();
    $token = $user->createToken('authToken')->plainTextToken;

    // Reload user relationships and info
    $user->load(['roles', 'currentRole', 'tutor', 'student']);

    $responseData = [
        'token' => $token,
        'user' => [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'name' => $user->name,
            'email' => $user->email,
            'email_verified_at' => $user->email_verified_at,
            'created_at' => $user->created_at,
            'updated_at' => $user->updated_at,
            'roles' => $user->roles->pluck('name'),
            'current_role' => $newRole,
        ],
        'onboarding_progress' => $user->onboardingStatus ? [
            'current_step' => $user->onboardingStatus->current_step,
            'data' => $user->onboardingStatus->data ? json_decode($user->onboardingStatus->data) : null,
        ] : null,
        'tutor' => $user->tutor,
        'student' => $user->student,
    ];

    return $this->success($responseData, 'Role switched and user relogged successfully.');
}

}
