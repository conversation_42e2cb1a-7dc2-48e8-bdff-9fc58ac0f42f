<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Services\StudentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GetAllStudentsController extends ApiBaseController
{
    protected $studentService;

    public function __construct(StudentService $studentService)
    {
        $this->studentService = $studentService;
    }

    public function __invoke(Request $request)
    {
        try {
            $tutors = $this->studentService->getAllStudents();
            return $this->success($tutors, 'Stuents fetched successfully');
        } catch (\Exception $e) {
            Log::error("Error Fetching tutors: ". $e->getMessage());
            return $this->error('Error fetching tutors'. $e->getMessage());
        }
    }

}
