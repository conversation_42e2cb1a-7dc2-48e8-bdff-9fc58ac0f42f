<?php

namespace App\Http\Controllers;

use App\Models\StripeConnectedAccount;
use App\Services\StripePaymentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class StripeController extends Controller
{
    public function callback(Request $request)
    {
        try {
            $user_id =  $request->input('user_id');
            $accountId = $request->query('account_id');

            if (!$user_id) {
                throw new Exception('User ID is missing in callback.');
            }

            if (!$accountId) {
                throw new Exception('Account ID is missing in callback.');
            }

            $callback = app(StripePaymentService::class)->handleStripeAccountLinkCallback($request);


            if ($callback) {
                return response()->json(['message' => 'Stripe account linked successfully.'], 200);
            }

            return response()->json(['error' => 'Failed to link Stripe account.'], 500);
        } catch (Exception $e) {
            Log::error('Stripe Account Link Callback Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to link Stripe account.'], 500);
        }
    }
}
