<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use DateTimeZone;

class TimezoneController extends ApiBaseController
{
    /**
     * Handle the incoming request.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke()
    {
        $timezones = DateTimeZone::listIdentifiers();

        return $this->success($timezones, "timezones fetched");
    }
}
