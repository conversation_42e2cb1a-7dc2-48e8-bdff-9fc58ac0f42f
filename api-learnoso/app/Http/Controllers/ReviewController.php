<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Http\Requests\ReviewRequest;
use App\Services\ReviewsService;
use Illuminate\Http\Request;

class ReviewController extends ApiBaseController
{
    private ReviewsService $reviewService;
    public function __construct(ReviewsService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    public function getTutorReviews($tutorId)
    {
        return $this->success($this->reviewService->getReviewsByTutorId($tutorId), 'Tutor reviews fetched');
    }

    public function getStudentReviews($studentId)
    {
        return $this->reviewService->getReviewsByStudentId($studentId);
    }

    public function createReview(ReviewRequest $request)
    {
        $reviewData = $request->all();
        try {
           $review = $this->reviewService->createReview($reviewData);
           return $this->success($review, 'Review created successfully');
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 400);
        }

    }

    public function updateReview(ReviewRequest $request, $reviewId)
    {
        $reviewData = $request->all();
        return $this->success($this->reviewService->updateReview($reviewId, $reviewData), 'Review updated');
    }

    public function deleteReview($reviewId)
    {
        return $this->reviewService->deleteReview($reviewId);
    }

    public function find($reviewId)
    {
        return $this->success($this->reviewService->find($reviewId), 'Review fetched');
    }

    public function all(Request $request)
    {
        return $this->success($this->reviewService->all($request), 'All reviews fetched');
    }
}
