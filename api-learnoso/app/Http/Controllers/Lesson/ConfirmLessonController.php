<?php

namespace App\Http\Controllers\Lesson;

use App\Base\ApiBaseController;
use App\Models\Lesson;
use App\Services\LessonService;
use Illuminate\Http\Request;

class ConfirmLessonController extends ApiBaseController
{
    protected $lessonService;
    public function __construct(LessonService $lessonService)
    {
        $this->lessonService = $lessonService;
    }
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {

        $lesson = Lesson::find($request->input('lesson_id'));

        if (!$lesson) {
            return $this->error('Lesson not found');
        }

        if ($lesson->student_id != $request->user()->id) {
            return $this->error('You are not authorized to confirm this lesson');
        }

        if ($lesson->confirmed) {
            return $this->error('Lesson already confirmed');
        }

        if ($lesson->status != 'scheduled') {
            return $this->error('Lesson is not scheduled');
        }

        if ($lesson->starts_at < now()) {
            return $this->error('Lesson has already started');
        }

        // check if theres duration in the confirm request
        if (!$request->has('duration')) {
            return $this->error('Duration is required');
        }

        $duration = $request->input('duration');

        if ($duration < 15) {
            return $this->error('Duration must be at least 15 minutes');
        }
        try {
            $this->lessonService->confirmLesson($lesson, $duration);
            return $this->success($lesson, 'Lesson confirmed successfully');
        } catch (\Exception $e) {
            return $this->error('Error: ' . $e->getMessage());
        }

    }
}
