<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Http\Resources\LessonResource;
use App\Models\Lesson;
use App\Repositories\Contracts\LessonRepositoryInterface;
use App\Services\QueryOptimizationService;
use App\Services\CacheService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LessonController extends ApiBaseController
{
    protected $lessonRepository;
    protected QueryOptimizationService $queryOptimization;
    protected CacheService $cacheService;

    public function __construct(
        LessonRepositoryInterface $lessonRepository,
        QueryOptimizationService $queryOptimization,
        CacheService $cacheService
    ) {
        $this->lessonRepository = $lessonRepository;
        $this->queryOptimization = $queryOptimization;
        $this->cacheService = $cacheService;
    }

    public function getLessonById($lessonId)
    {
        // Try cache first for frequently accessed lessons
        $cacheKey = "lesson_details_{$lessonId}";
        $cachedLesson = $this->cacheService->remember($cacheKey, CacheService::MEDIUM_TTL, function() use ($lessonId) {
            return $this->queryOptimization->getLessonsWithRelations(['id' => $lessonId])->first();
        });

        if (!$cachedLesson) {
            return $this->error('Lesson not found', 404);
        }

        return new LessonResource($cachedLesson);
    }

    public function getTutorLessons(Request $request)
    {
        $tutorId = Auth::user()->id;

        // Build filters from request
        $filters = [
            'tutor_id' => $tutorId,
            'status' => $request->get('status'),
            'date_from' => $request->get('date_from'),
            'date_to' => $request->get('date_to')
        ];

        // Remove null filters
        $filters = array_filter($filters, function($value) {
            return $value !== null;
        });

        // Use optimized query with caching
        $cacheKey = "tutor_lessons_" . md5(serialize($filters));
        $lessons = $this->cacheService->remember($cacheKey, CacheService::SHORT_TTL, function() use ($filters) {
            return $this->queryOptimization->getLessonsWithRelations($filters)
                ->orderBy('starts_at', 'desc')
                ->paginate(15);
        });

        return LessonResource::collection($lessons);
    }

    public function getStudentLessons(){

        $lessons = $this->lessonRepository->getLessonsByStudentId(studentId: Auth::user()->id);

        return LessonResource::collection($lessons);
    }

    protected function meetingAlreadyScheduledAtTime($user, $startsAt, $endsAt)
    {
        // Check if there is any lesson scheduled for the tutor or student within the specified time range
        return Lesson::where(function ($query) use ($user, $startsAt, $endsAt) {
            $query->where(function ($q) use ($user) {
                $q->where('tutor_id', $user->id)
                    ->orWhere('student_id', $user->id);
            })
            ->where(function ($q) use ($startsAt, $endsAt) {
                $q->whereBetween('starts_at', [$startsAt, $endsAt])
                    ->orWhereBetween('ends_at', [$startsAt, $endsAt])
                    ->orWhere(function ($q) use ($startsAt, $endsAt) {
                        $q->where('starts_at', '<=', $startsAt)
                            ->where('ends_at', '>=', $endsAt);
                    });
            });
        })->exists();
    }

    protected function meetingExists($user)
    {
        // Check if there is any lesson scheduled for the tutor or student within the specified time range
        return Lesson::where(function ($query) use ($user) {
            $query->where('tutor_id', $user->id)
                ->orWhere('student_id', $user->id);
        })->exists();
    }



    protected function MeetingDurationValid($startsAt, $endsAt)
    {
        // Calculate the duration in minutes
        $durationInMinutes = round((strtotime($endsAt) - strtotime($startsAt)) / 60);

        // Check if the duration is at least 15 minutes
        return $durationInMinutes >= 15;
    }

    public function updateLessonWithDuration(Request $request) {

        $$this->validate($request, [
            'duration' => 'required|integer',
            'lesson_id' => 'required|exists:lessons,id',
        ]);

        $lesson = Lesson::find($request->input('lesson_id'));

        if (!$lesson) {
            return $this->error('Lesson not found');
        }

        $lesson->duration = $request->duration;

        $lesson->save();


        return $this->success($lesson, "Lesson duration updated successfully");

    }



}
