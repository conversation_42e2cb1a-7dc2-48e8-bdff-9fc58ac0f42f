<?php

namespace App\Http\Controllers;

use App\Base\ApiBaseController;
use App\Models\User;
use App\Services\PayPalPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Support\Facades\Log;

class PayPalController extends ApiBaseController
{
    public function successRedirect(Request $request, PayPalPaymentService $paypalService)
    {
        $token = $request->query('token');
        $user = Auth::user();

        try {
            // Capture the payment from PayPal
            $response = $paypalService->captureOrder($token);

            if ($response['status'] === 'COMPLETED') {

                $userId = $response['purchase_units'][0]['reference_id'];


                // decrypt user id
                $userId = decrypt($userId);

                $user = User::find($userId);


                if (!$user) {
                    return redirect('/wallet')->with('error', 'User not found.');
                }

                // Extract payment details
                $amount = $response['purchase_units'][0]['payments']['captures'][0]['amount']['value'];
                $currency = $response['purchase_units'][0]['payments']['captures'][0]['amount']['currency_code'];

                // Mark transaction as completed in the database
                $user->deposit($amount, [
                    'type' => 'paypal_payment',
                    'currency' => $currency,
                ]);

                // Redirect to a different website
                return redirect(config('app.payment_success_url'))->with('success', 'Payment successful.');

            }

            // Redirect with error message
            return  redirect(config('app.payment_failure_url'))->with('error', 'Failed to capture payment. Please try again.');

          } catch (Exception $e) {
            Log::error('PayPal Capture Payment Error: ' . $e->getMessage());
            return redirect(config('app.payment_failure_url'))->with('error', 'Failed to capture payment. Please try again.');
        }
    }

    public function failureRedirect()
    {
        // Redirect with error message
        return $this->error('Failed to capture payment. Please try again.', );
    }
}
