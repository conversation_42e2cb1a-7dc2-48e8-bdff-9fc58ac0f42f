<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Resources\WalletTransactionResource;
use App\Services\WalletService;
use Illuminate\Http\Request;

class GetAllTransactionsController extends ApiBaseController
{

    protected $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
{
    try {
        $transactions = $this->walletService->getAllTransactions($request->all());

        return WalletTransactionResource::collection($transactions)
            ->response()
            ->setStatusCode(200);

    } catch (\Exception $e) {
        return response()->json(['message' => $e->getMessage()], 500);
    }
}

}
