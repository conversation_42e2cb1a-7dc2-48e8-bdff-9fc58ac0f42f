<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Notifications\TutorProfileApproved;
use App\Notifications\TutorProfileRejected;
use Illuminate\Http\Request;
use App\Models\Tutor;
use Carbon\Carbon;

class ManageTutorProfileController extends ApiBaseController
{
    public function approveProfile(Request $request)
{
    $id = $request->input('id');
    $profile = Tutor::with('user')->find($id);

    if ($profile) {
        $profile->profile_status = 'approved';
        $profile->profile_approved_at = now();
        $profile->save();

        $profile->user->notify(new TutorProfileApproved($profile));

        return $this->success($profile, 'Profile approved successfully.');
    }

    return $this->error('Profile not found.', 404);
}

public function rejectProfile(Request $request)
{
    $id = $request->input('id');
    $reason = $request->input('reason');
    $profile = Tutor::with('user')->find($id);

    if ($profile) {
        $profile->profile_status = 'rejected';
        $profile->profile_rejected_at = now();
        $profile->profile_rejection_reason = $reason;
        $profile->save();

        $profile->user->notify(new TutorProfileRejected($profile, $reason));

        return $this->success($profile, 'Profile rejected successfully.');
    }

    return $this->error('Profile not found.', 404);
}

}
