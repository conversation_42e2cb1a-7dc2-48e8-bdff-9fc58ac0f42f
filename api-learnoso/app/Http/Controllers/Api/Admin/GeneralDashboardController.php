<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\User;
use App\Services\StatisticsService;
use Illuminate\Http\Request;

class GeneralDashboardController extends ApiBaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $users = User::count();
        $activeUsers = User::whereNull('deleted_at')->count();
        $inactiveUsers = User::whereNotNull('deleted_at')->count();
        $courses = Course::count();
        $sessionCount = Lesson::count();
        $tutorsCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'tutor');
        })->count();

        $studentsCount = User::whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->count();

        $totalRevenue = StatisticsService::getTotalRevenue();

        return $this->success([
            'users' => $users,
            'active_users' => $activeUsers,
            'inactive_users' => $inactiveUsers,
            'total_users' => $users + $inactiveUsers,
            'courses' => $courses,
            'sessions' => $sessionCount,
            'tutors' => $tutorsCount,
            'students' => $studentsCount,
            'total_revenue' => $totalRevenue,
        ], 'General Dashboard');


    }
}
