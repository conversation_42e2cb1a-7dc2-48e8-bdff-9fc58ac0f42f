<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Services\TutorService;
use Illuminate\Http\Request;

class TutorApplicationController extends ApiBaseController
{
    protected TutorService $tutorService;

    public function __construct(TutorService $tutorService){
        $this->tutorService = $tutorService;
    }

    public function __invoke()
    {
        $tutorApplications = $this->tutorService->getTutorApplicationStats();
        return $this->success($tutorApplications, 'Tutor applications fetched successfully');
    }

    public function getTutorApplications(Request $request)
    {
        $tutorApplications = $this->tutorService->getTutorApplications($request->all());
        return $this->success($tutorApplications, 'Tutor applications fetched successfully');
    }


}
