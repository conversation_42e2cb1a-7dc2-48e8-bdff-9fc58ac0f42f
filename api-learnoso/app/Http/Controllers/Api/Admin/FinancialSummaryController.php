<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Services\StatisticsService;
use Illuminate\Http\JsonResponse;

class FinancialSummaryController extends ApiBaseController
{
    protected $statisticsService;

    public function __construct(StatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    public function __invoke(): JsonResponse
    {
        return $this->success($this->statisticsService->getFinancialSummary(), 'Financial summary');
    }
}
