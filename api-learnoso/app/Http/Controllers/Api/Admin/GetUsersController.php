<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Http\Request;

class GetUsersController extends ApiBaseController
{

    protected $userRepository;

    /**
     * Constructor
     *
     */

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        try {
            $users = $this->userRepository->all($request->all());

            return UserResource::collection($users)
                ->response()
                ->setStatusCode(200);

                } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
