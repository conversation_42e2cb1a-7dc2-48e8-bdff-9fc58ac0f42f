<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateCourseRequest;
use App\Http\Requests\UpdateCourseRequest;
use App\Repositories\Contracts\CourseRepositoryInterface;
use Illuminate\Http\Request;

class ManageCoursesController extends ApiBaseController
{
    protected $courseRepository;

    public function __construct(CourseRepositoryInterface $courseRepository)
    {
        $this->courseRepository = $courseRepository;
    }

    public function create(CreateCourseRequest $request)
    {
        $data = $request->validated();
        try {
            $course = $this->courseRepository->create($request->all());

            return $this->success($course, "Course created successfully");
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function update(UpdateCourseRequest $request, $id)
    {
        try {
            $course = $this->courseRepository->update($id, $request->all());

            return $this->success($course, "Course updated successfully");
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

    public function delete($id)
    {
        try {
            $course = $this->courseRepository->delete($id);

            return $this->success($course, "Course deleted successfully");
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }

}
