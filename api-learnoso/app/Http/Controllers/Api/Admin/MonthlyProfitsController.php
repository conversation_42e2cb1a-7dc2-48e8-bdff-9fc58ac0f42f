<?php

namespace App\Http\Controllers\Api\Admin;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Services\StatisticsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MonthlyProfitsController extends ApiBaseController
{
    protected $statisticsService;

    public function __construct(StatisticsService $statisticsService)
    {
        $this->statisticsService = $statisticsService;
    }

    public function __invoke(Request $request): JsonResponse
    {
        $filters = $request->only(['start_date', 'end_date', 'type']);
        return $this->success($this->statisticsService->getMonthlyProfits($filters), 'Monthly Profits');
    }

    public function monthlyTransactions(Request $request): JsonResponse
    {
        $filters = $request->only(['start_date', 'end_date', 'type']);
        return $this->success($this->statisticsService->getMonthlyTransactions($filters), 'Monthly Transactions');
    }
}
