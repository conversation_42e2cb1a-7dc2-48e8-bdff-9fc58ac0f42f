<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Services\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class TwoFactorAuthController extends ApiBaseController
{
    protected TwoFactorAuthService $twoFactorService;

    public function __construct(TwoFactorAuthService $twoFactorService)
    {
        $this->twoFactorService = $twoFactorService;
    }

    /**
     * Get 2FA status for current user
     */
    public function getStatus()
    {
        try {
            $user = Auth::user();

            $status = [
                'enabled' => $user->hasTwoFactorEnabled(),
                'pending' => $user->hasTwoFactorPending(),
                'confirmed_at' => $user->two_factor_confirmed_at?->toISOString(),
                'recovery_codes_count' => count($user->two_factor_recovery_codes ?? [])
            ];

            return $this->success($status, '2FA status retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to get 2FA status: ' . $e->getMessage());
        }
    }

    /**
     * Setup 2FA for user
     */
    public function setup()
    {
        try {
            $user = Auth::user();

            if ($user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is already enabled', 400);
            }

            $setupData = $this->twoFactorService->setupTwoFactor($user);

            // Don't return the secret in production for security
            $response = [
                'qr_code_url' => $setupData['qr_code_url'],
                'manual_entry_key' => $setupData['manual_entry_key'],
                'recovery_codes' => $setupData['recovery_codes']
            ];

            return $this->success($response, '2FA setup initiated. Please verify with your authenticator app.');
        } catch (\Exception $e) {
            Log::error('2FA setup failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to setup 2FA: ' . $e->getMessage());
        }
    }

    /**
     * Confirm 2FA setup
     */
    public function confirm(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();

            if ($user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is already confirmed', 400);
            }

            if (!$user->hasTwoFactorPending()) {
                return $this->error('No pending 2FA setup found. Please initiate setup first.', 400);
            }

            $confirmed = $this->twoFactorService->confirmTwoFactor($user, $request->code);

            if ($confirmed) {
                return $this->success([
                    'enabled' => true,
                    'confirmed_at' => $user->fresh()->two_factor_confirmed_at->toISOString()
                ], 'Two-factor authentication enabled successfully');
            } else {
                return $this->error('Invalid verification code', 400);
            }
        } catch (\Exception $e) {
            Log::error('2FA confirmation failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to confirm 2FA: ' . $e->getMessage());
        }
    }

    /**
     * Verify 2FA code
     */
    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();

            if (!$user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is not enabled', 400);
            }

            $verified = $this->twoFactorService->verifyCode($user, $request->code);

            if ($verified) {
                return $this->success(['verified' => true], 'Code verified successfully');
            } else {
                return $this->error('Invalid verification code', 400);
            }
        } catch (\Exception $e) {
            Log::error('2FA verification failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to verify code: ' . $e->getMessage());
        }
    }

    /**
     * Disable 2FA
     */
    public function disable(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();

            if (!$user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is not enabled', 400);
            }

            $disabled = $this->twoFactorService->disableTwoFactor($user, $request->password);

            if ($disabled) {
                return $this->success([
                    'enabled' => false,
                    'disabled_at' => $user->fresh()->two_factor_disabled_at->toISOString()
                ], 'Two-factor authentication disabled successfully');
            } else {
                return $this->error('Invalid password', 400);
            }
        } catch (\Exception $e) {
            Log::error('2FA disable failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to disable 2FA: ' . $e->getMessage());
        }
    }

    /**
     * Get recovery codes
     */
    public function getRecoveryCodes()
    {
        try {
            $user = Auth::user();

            if (!$user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is not enabled', 400);
            }

            $backupData = $this->twoFactorService->generateBackupMethod($user);

            return $this->success($backupData, 'Recovery codes retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to get recovery codes: ' . $e->getMessage());
        }
    }

    /**
     * Regenerate recovery codes
     */
    public function regenerateRecoveryCodes(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();

            if (!$user->hasTwoFactorEnabled()) {
                return $this->error('Two-factor authentication is not enabled', 400);
            }

            // Verify password before regenerating codes
            if (!Hash::check($request->password, $user->password)) {
                return $this->error('Invalid password', 400);
            }

            $newCodes = $this->twoFactorService->regenerateRecoveryCodes($user);

            return $this->success([
                'recovery_codes' => $newCodes,
                'count' => count($newCodes)
            ], 'Recovery codes regenerated successfully');
        } catch (\Exception $e) {
            Log::error('Recovery codes regeneration failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to regenerate recovery codes: ' . $e->getMessage());
        }
    }

    /**
     * Get 2FA statistics (admin only)
     */
    public function getStatistics()
    {
        try {
            $user = Auth::user();

            if (!$user->hasRole('admin')) {
                return $this->error('Unauthorized access', 403);
            }

            $statistics = $this->twoFactorService->getTwoFactorStatistics();

            return $this->success($statistics, '2FA statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to get 2FA statistics: ' . $e->getMessage());
        }
    }
}
