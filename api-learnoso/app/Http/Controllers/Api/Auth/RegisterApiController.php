<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use App\Http\Requests\RegisterRequest;
use App\Services\UserService;

class RegisterApiController extends ApiBaseController
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function __invoke(RegisterRequest $request)
    {
        try {
            $data = $request->validated();
            $user = $this->userService->registerUser($data);

            return $this->success($user, 'Account Created, please confirm your email');
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
