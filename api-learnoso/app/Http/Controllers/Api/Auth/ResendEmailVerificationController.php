<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use App\Http\Requests\ResendEmailVerificationRequest;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;

class ResendEmailVerificationController extends ApiBaseController
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function __invoke(ResendEmailVerificationRequest $request)
    {
        try {
            $data = $request->validated();

            $this->userService->resendEmailVerificationLink($data['email']);
            return $this->success($data['email'], 'Verification email resent');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
