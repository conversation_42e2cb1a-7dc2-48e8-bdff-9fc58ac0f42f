<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LogoutApiController extends ApiBaseController
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $request->user()->currentAccessToken()->delete();
        return $this->success(message: "logout successful");
    }
}
