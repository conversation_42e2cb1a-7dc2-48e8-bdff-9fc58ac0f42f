<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use App\Http\Requests\LoginRequest; // Create this request class to validate login input
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Event;

class LoginApiController extends ApiBaseController
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function __invoke(LoginRequest $request)
    {
        try {
            $credentials = $request->only('email', 'password');
            $ipAddress = $request->ip();
            $twoFactorCode = $request->input('two_factor_code');

            $result = $this->userService->attemptLogin($credentials, $ipAddress, $twoFactorCode);

            if (!$result['success']) {
                // Handle 2FA requirement
                if (isset($result['requires_2fa']) && $result['requires_2fa']) {
                    $statusCode = isset($result['invalid_2fa']) && $result['invalid_2fa'] ? 400 : 202;

                    $errorData = [
                        'message' => $result['message'],
                        'requires_2fa' => true
                    ];

                    if (isset($result['invalid_2fa']) && $result['invalid_2fa']) {
                        $errorData['invalid_2fa'] = true;
                    }

                    return $this->error($errorData, $statusCode);
                }

                // Handle account lockout or regular login failure
                $statusCode = $result['lockout'] ? 423 : 401; // 423 = Locked

                $errorData = [
                    'message' => $result['message'],
                    'lockout' => $result['lockout']
                ];

                if ($result['lockout']) {
                    $errorData['remaining_time'] = $result['remaining_time'];
                } else {
                    $errorData['remaining_attempts'] = $result['remaining_attempts'];
                }

                return $this->error($errorData, $statusCode);
            }

            $user = $result['user'];

            // if (!$user->hasVerifiedEmail()) {
            //     return $this->error("Email not verified", JsonResponse::HTTP_UNAUTHORIZED);
            // }

            // Dispatch the login event
            Event::dispatch('user.login', $user);

            $token = $user->createToken('authToken')->plainTextToken;

            // Get current role name
            $currentRoleName = $user->currentRole ? $user->currentRole->role->name : null;

            // Retrieve onboarding progress
            $onboardingProgress = $user->onboardingStatus ? [
                'current_step' => $user->onboardingStatus->current_step,
                'data' => $user->onboardingStatus->data ? json_decode($user->onboardingStatus->data) : null,
            ] : null;

            $user->load(['tutor', 'student']);

            $responseData = [
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'name' => $user->name,
                    'email' => $user->email,
                    'email_verified_at' => $user->email_verified_at,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                    'roles' => $user->roles->pluck('name'),
                    'current_role' => $currentRoleName,
                    'two_factor_enabled' => $user->hasTwoFactorEnabled(),
                ],
                'onboarding_progress' => $onboardingProgress,
                'tutor' => $user->tutor,
                'student' => $user->student
            ];

            return $this->success($responseData, 'Login successful');
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
