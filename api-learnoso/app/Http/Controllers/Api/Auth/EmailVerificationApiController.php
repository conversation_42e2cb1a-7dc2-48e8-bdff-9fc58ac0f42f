<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Auth\Events\Verified;
use Illuminate\Support\Facades\Log;
use Exception;
use Spatie\Permission\Models\Role;

class EmailVerificationApiController extends ApiBaseController
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function __invoke(Request $request)
    {
        try {
            $user = $this->userService->find($request->id);

            if ($user->markEmailAsVerified()) {
                event(new Verified($user));
                $user->email_verified_at = Carbon::now();
                $user->save();
            }

            $this->userService->assignRole($user, 'guest');

            // create user currentRole
            $user->currentRole()->firstOrCreate(
                ['user_id' => $user->id],
                [
                'role_id' => Role::where('name', 'guest')->first()->id,
            ]);

            return redirect()->intended(
                config('app.frontend_url') . '/login?verified=1'
            );
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return redirect()->intended(
                config('app.frontend_url') . '/login?verified=0'
            );
        }
    }
}
