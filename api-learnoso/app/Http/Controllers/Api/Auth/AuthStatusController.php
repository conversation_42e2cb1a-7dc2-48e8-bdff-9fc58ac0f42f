<?php

namespace App\Http\Controllers\Api\Auth;

use App\Base\ApiBaseController;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Lara<PERSON>\Sanctum\PersonalAccessToken;
use App\Models\User;

class AuthStatusController extends ApiBaseController
{
    /**
     * Handle the incoming request to check if a token is valid.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        // Extract the Bearer token from the request's Authorization header
        $token = $request->bearerToken();

        if (!$token) {
            return $this->error("Token not valid", 401);
        }

        // Use Sanctum's PersonalAccessToken model to validate the token
        $personalAccessToken = PersonalAccessToken::findToken($token);

        if (!$personalAccessToken) {
            return $this->error("Token not valid", 401);
        }

        // Retrieve the associated user
        $user = $personalAccessToken->tokenable;

        if (!$user instanceof User) {
            return $this->error("Token is not valid for given user", 401);
        }

        // Return a success response if the token is valid

        return $this->success(
            $user,
            "Token is valid"
        );
    }
}
