<?php

namespace App\Http\Controllers\Api\Payments;

use App\Base\ApiBaseController;
use App\Http\Requests\InitiateWithdrawalRequest;
use App\Services\PayPalPaymentService;
use App\Services\StripePaymentService;
use Illuminate\Support\Facades\Log;

class WithdrawalController extends ApiBaseController
{
    public function __invoke(InitiateWithdrawalRequest $request)
    {
        $paymentMethod = $request->input('payment_method');

        switch ($paymentMethod) {
            case 'paypal':
                return $this->handlePayPalWithdrawal($request);
            case 'stripe':
                return $this->handleStripeWithdrawal($request);
            default:
                return $this->error('Invalid payment method. Supported methods: paypal, stripe.', 400);
        }
    }

    protected function handlePayPalWithdrawal($request)
    {
        try {
            $payPalService = app(PayPalPaymentService::class);

            $recipientEmail = $request->input('recipient_email');
            $amount = $request->input('amount');
            $currency = $request->input('currency', 'USD');

            $response = $payPalService->withdrawFunds($recipientEmail, $amount, $currency);

            Log::info('PayPal Withdrawal Response: ' . json_encode($response));

            if (isset($response['batch_header']['batch_status'])) {
                $batchStatus = $response['batch_header']['batch_status'];
                $batchId = $response['batch_header']['payout_batch_id'];

                if ($batchStatus === 'PENDING') {
                    return $this->success(
                        ['payout_batch_id' => $batchId, 'status' => $batchStatus],
                        'Withdrawal initiated successfully. Use the batch ID to check payout status.'
                    );
                }
            }

            $errorMessage = $response['message'] ?? 'PayPal withdrawal failed. Please try again later.';
            return $this->error($errorMessage);
        } catch (\Exception $e) {
            Log::error('PayPal Withdrawal Error: ' . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }

    protected function handleStripeWithdrawal($request)
    {
        try {
            $stripeService = app(StripePaymentService::class);
            $data = $request->all();
            $user = $request->user();
            $destination = $data['destination'] ?? null;

            // Check if destination is provided
            if (!$destination) {
                // Attempt to retrieve or link the connected account
                $connectedAccount = $stripeService->getOrLinkConnectedAccount($user, $data['recipient']);


                if (!$connectedAccount) {
                    return $this->error('Failed to link or retrieve a connected Stripe account for the user.', 500);
                }

                // check if whats returned is a url
                if (is_string($connectedAccount)) {
                    return $this->success(['url' => $connectedAccount], 'Please complete the Stripe account linking process.');
                }

            }


            // Proceed with the withdrawal process
            $payout = $stripeService->withdrawFunds($data, $user);

            dd($payout);

            if ($payout->status === 'paid') {
                return $this->success(
                    ['transfer_id' => $payout->id, 'status' => $payout->status],
                    'Withdrawal initiated successfully.'
                );
            }

        } catch (\Exception $e) {
            Log::error('Stripe Withdrawal Error: ' . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }

    public function checkPayPalPayoutStatus($batchId)
    {
        try {
            $payPalService = app(PayPalPaymentService::class);

            $status = $payPalService->checkPayoutStatus($batchId);

            return $this->success(
                ['batch_id' => $batchId, 'status' => $status],
                'Payout status retrieved successfully.'
            );
        } catch (\Exception $e) {
            Log::error('PayPal Payout Status Check Error: ' . $e->getMessage());
            return $this->error($e->getMessage(), 500);
        }
    }
}
