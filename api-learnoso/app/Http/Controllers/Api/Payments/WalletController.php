<?php

namespace App\Http\Controllers\Api\Payments;

use App\Base\ApiBaseController;
use App\Services\WalletService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WalletController extends ApiBaseController
{
    protected $walletService;

    public function __construct(WalletService $walletService)
    {
        $this->walletService = $walletService;
    }

    public function getBalance()
    {
        $user = Auth::user();
        $walletInfo = $this->walletService->getWallet($user);
        return $this->success($walletInfo, 'User wallet fetchted');
    }

    public function getTransactions(Request $request)
    {
        $user = Auth::user();
        $transactions = $this->walletService->getTransactions($user, $request->all());
        return $this->success($transactions, 'transactions');
    }
}
