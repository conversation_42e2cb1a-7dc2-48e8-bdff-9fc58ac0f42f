<?php

namespace App\Http\Controllers\Api\Payments;

use App\Base\ApiBaseController;
use App\Http\Requests\InitiatePaymentRequest;
use Illuminate\Http\Request;
use App\Services\PayPalPaymentService;
use App\Services\StripePaymentService;
use Illuminate\Support\Facades\Log;

class TopupController extends ApiBaseController
{
    /**
     * Handle the incoming request based on the payment method.
     */
    public function __invoke(InitiatePaymentRequest $request)
    {


        // Determine the payment method
        $paymentMethod = $request->input('payment_method');

        switch ($paymentMethod) {
            case 'paypal':
                return $this->handlePayPal($request);
            case 'stripe':
                return $this->handleStripe($request);
            // Add more cases for additional payment methods as needed
            default:
                return $this->error('Invalid payment method.');
        }
    }

    /**
     * Handle the PayPal payment method.
     */
    protected function handlePayPal(Request $request)
    {
        try {
            $payPalService = app(PayPalPaymentService::class);
            $response = $payPalService->createOrder($request->all());

            if (isset($response['id']) && $response['id'] != null) {
                foreach ($response['links'] as $links) {
                    if ($links['rel'] == 'approve') {
                        return $this->success(['url' => $links['href']], 'Payment initiated, please open this on a new tab');
                    }
                }

                return $this->error('Something went wrong while processing the payment.');
            } else {
                $errorMessage = isset($response['error']) ? $response['error']['message'] : ($response['message'] ?? 'Something went wrong.');
                return $this->error($errorMessage);
            }
        } catch (\Exception $e) {
            Log::error('PayPal Payment Error: ' . $e->getMessage());
            return $this->error('Failed to create PayPal order. Please try again later.');
        }
    }

    /**
     * Handle the Stripe payment method.
     */
    protected function handleStripe(Request $request)
    {
        try {
            $stripeService = app(StripePaymentService::class);
            $response = $stripeService->handle($request);

            if (isset($response['error']) && $response['error']) {
                return $this->error($response['error']);
            }

            if (isset($response['checkout_url']) && $response['checkout_url']) {
                return $this->success( ['url' => $response['checkout_url']], 'Payment initiated');
            }

        } catch (\Exception $e) {
            Log::error('Stripe Payment Error: ' . $e->getMessage());
            return $this->error('Payment session creation failed.' . $e->getMessage());
        }
    }

    public function failure()
    {

        return $this->error('Payment could not be initiated, please try again later', );

    }
}
