<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Services\TutorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GetFullProfileController extends ApiBaseController
{
    protected TutorService $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $validation = Validator::make($request->all(),[
            'user_id' => 'required|exists:users,id'
        ]);

        if ($validation->fails()) {
            return $this->validationError($validation->errors()->toArray());
        }

        $tutor = $this->tutorService->getFullTutorProfile($request->user_id);
        return $this->success($tutor, "tutor full profile");
    }
    
}
