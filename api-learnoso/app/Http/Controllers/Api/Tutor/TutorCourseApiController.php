<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Requests\TutorCourseRequest;
use App\Services\TutorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TutorCourseApiController extends ApiBaseController
{
    protected $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }

    /**
     * Handle the incoming request.
     */
    public function __invoke(TutorCourseRequest $request)
    {
        try {
            // Use validated data from the request
            $user = $this->tutorService->onboardTutorWithCourses(
                $request->validated()['user_id'],
                $request->validated()['course_ids']
            );

            return $this->success($user->load('tutor', 'onboardingStatus'), 'Tutor successfully onboarded with selected courses.');
        } catch (\Exception $e) {
            Log::error('Error onboarding tutor: ' . $e->getMessage());
            return $this->error('Failed to onboard tutor.', ['error' => $e->getMessage()]);
        }
    }

    public function getTutorCourses(Request $request, $tutorId){
        $tutor = $this->tutorService->getTutorById($tutorId);
        if(!$tutor){
            return $this->notFoundError("tutor not found");
        }
        return $this->success($tutor->courses);
    }
}
