<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Models\Tutor;
use App\Services\TutorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TutorDashboardStatsController extends ApiBaseController
{
    private TutorService $tutorService;
    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }
    public function __invoke(Request $request, $tutorId)
    {
        if(!is_numeric($tutorId)){
            return $this->error('Invalid tutor id', 400);
        }
        if(!Tutor::find($tutorId)){
            return $this->error('Tu<PERSON> not found', 404);
        }
        $tutor = $this->tutorService->getTutorDashboard($tutorId);
        return $this->success($tutor, 'Tutor dashboard stats fetched successfully');
    }
}
