<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Requests\StoreEducationRequest;
use App\Services\EducationService;
use Illuminate\Http\JsonResponse;

class StoreEducationController extends ApiBaseController
{
    protected $educationService;

    public function __construct(EducationService $educationService)
    {
        $this->educationService = $educationService;
    }

    public function __invoke(StoreEducationRequest $request)
    {
        try {
            $educationData = $this->educationService->storeEducation($request->all());
            return $this->success(
                $educationData,
                'Education records stored successfully');
        }

        catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
