<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateTutorDescriptionAndMotivationRequest;
use App\Services\TutorService;
use Illuminate\Http\Request;

class UpdateTutorDescriptionAndMotivationController extends ApiBaseController
{
    /**
     * Handle the incoming request.
     */

    protected TutorService $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }
    public function __invoke(UpdateTutorDescriptionAndMotivationRequest $request)
    {
        try {

            // Use validated data from the request
            $user = $this->tutorService->setTutorDescriptionAndMotivation(
                $request->validated()['user_id'],
                $request->validated()['description'],
                $request->validated()['motivation'],
                $request->validated()['primary_language_id']
            );

            $user->load('tutor');

            return $this->success($user, 'Tutor description and motivation updated successfully');
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
