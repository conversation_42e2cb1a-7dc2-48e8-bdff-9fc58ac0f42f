<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\TutorProfileAndVideoUrlRequest;
use App\Services\TutorService;
use Illuminate\Http\Request;

class TutorProfileAndVideoUrlController extends ApiBaseController
{
    protected TutorService $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }

    /**
     * Handle the incoming request.
     */
    public function __invoke(TutorProfileAndVideoUrlRequest $request)
    {
        try {
            // Use validated data from the request
            $user = $this->tutorService->setTutorProfileAndVideoUrl($request);

            return $this->success($user, 'Tutor profile and video url updated successfully');
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
