<?php

namespace App\Http\Controllers\Api\Tutor;

use App\Base\ApiBaseController;
use App\Http\Requests\UpdatePriceAndAvailabilityRequest;
use App\Services\TutorService;
use Illuminate\Http\JsonResponse;

class PriceAndAvailabilityController extends ApiBaseController
{
    protected $tutorService;

    public function __construct(TutorService $tutorService)
    {
        $this->tutorService = $tutorService;
    }

    /**
     * Handle the incoming request.
     *
     * @param UpdatePriceAndAvailabilityRequest $request
     * @return JsonResponse
     */
    public function __invoke(UpdatePriceAndAvailabilityRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->tutorService->updatePriceAndAvailability($data);
        return $this->success($result);
    }
}
