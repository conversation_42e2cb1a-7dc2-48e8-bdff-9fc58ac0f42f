<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SystemSettingsController extends Controller
{
    public function index()
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'This action is unauthorized.'], 403);
        }

        $settings = SystemSetting::first();
        return response()->json(['data' => ['settings' => $settings->settings ?? []]]);
    }

    public function updateCommissionSettings(Request $request)
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'This action is unauthorized.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'lesson_commission_rate' => 'required|numeric|min:0|max:1',
            'minimum_commission_amount' => 'required|numeric|min:0',
            'maximum_commission_amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'The given data was invalid.', 'errors' => $validator->errors()], 422);
        }

        $settings = SystemSetting::first();
        $currentSettings = $settings->settings ?? [];

        $settings->settings = array_merge($currentSettings, [
            'lesson_commission_rate' => $request->lesson_commission_rate,
            'minimum_commission_amount' => $request->minimum_commission_amount,
            'maximum_commission_amount' => $request->maximum_commission_amount
        ]);

        $settings->save();

        return response()->json([
            'message' => 'Commission settings updated successfully',
            'data' => [
                'lesson_commission_rate' => $request->lesson_commission_rate,
                'minimum_commission_amount' => $request->minimum_commission_amount,
                'maximum_commission_amount' => $request->maximum_commission_amount
            ]
        ]);
    }

    public function updateWithdrawalSettings(Request $request)
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'This action is unauthorized.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'withdrawal_fee' => 'required|numeric|min:0|max:1',
            'minimum_withdrawal_amount' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'The given data was invalid.', 'errors' => $validator->errors()], 422);
        }

        $settings = SystemSetting::first();
        $currentSettings = $settings->settings ?? [];

        $settings->settings = array_merge($currentSettings, [
            'withdrawal_fee' => $request->withdrawal_fee,
            'minimum_withdrawal_amount' => $request->minimum_withdrawal_amount
        ]);

        $settings->save();

        return response()->json([
            'message' => 'Withdrawal settings updated successfully',
            'data' => [
                'withdrawal_fee' => $request->withdrawal_fee,
                'minimum_withdrawal_amount' => $request->minimum_withdrawal_amount
            ]
        ]);
    }

    public function updateNotificationSettings(Request $request)
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'This action is unauthorized.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|boolean',
            'push' => 'required|boolean',
            'sms' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'The given data was invalid.', 'errors' => $validator->errors()], 422);
        }

        $settings = SystemSetting::first();
        $currentSettings = $settings->settings ?? [];

        $settings->settings = array_merge($currentSettings, [
            'notification_settings' => [
                'email' => $request->email,
                'push' => $request->push,
                'sms' => $request->sms
            ]
        ]);

        $settings->save();

        return response()->json([
            'message' => 'Notification settings updated successfully',
            'data' => [
                'email' => $request->email,
                'push' => $request->push,
                'sms' => $request->sms
            ]
        ]);
    }

    public function update(Request $request)
    {
        if (!Auth::user()->hasRole('admin')) {
            return response()->json(['message' => 'This action is unauthorized.'], 403);
        }

        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'nullable'
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'The given data was invalid.', 'errors' => $validator->errors()], 422);
        }

        try {
            $setting = SystemSetting::first();
            if (!$setting) {
                $setting = new SystemSetting();
                $setting->settings = [];
            }

            $currentSettings = $setting->settings;
            $setting->settings = array_merge($currentSettings, $request->settings);
            $setting->save();

            return response()->json([
                'message' => 'System settings updated successfully',
                'data' => ['settings' => $setting->settings]
            ]);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to update system settings: ' . $e->getMessage()], 500);
        }
    }

    public function getCommissionSettings()
    {
        $settings = SystemSetting::first();
        $commissionSettings = $settings ? [
            'lesson_commission_rate' => $settings->settings['lesson_commission_rate'] ?? 0.10,
            'minimum_commission_amount' => $settings->settings['minimum_commission_amount'] ?? 1.00,
            'maximum_commission_amount' => $settings->settings['maximum_commission_amount'] ?? 50.00
        ] : [
            'lesson_commission_rate' => 0.10,
            'minimum_commission_amount' => 1.00,
            'maximum_commission_amount' => 50.00
        ];

        return response()->json([
            'message' => 'Commission settings retrieved successfully',
            'data' => $commissionSettings
        ]);
    }
}
