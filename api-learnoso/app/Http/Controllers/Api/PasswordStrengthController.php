<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Rules\StrongPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PasswordStrengthController extends ApiBaseController
{
    /**
     * Check password strength in real-time
     */
    public function checkStrength(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $password = $request->password;

            // Get strength analysis
            $strengthAnalysis = StrongPassword::getStrengthScore($password);

            // Check if password passes validation
            $strongPasswordRule = new StrongPassword();
            $isValid = true;
            $validationMessage = null;

            $strongPasswordRule->validate('password', $password, function($message) use (&$isValid, &$validationMessage) {
                $isValid = false;
                $validationMessage = $message;
            });

            $response = [
                'score' => $strengthAnalysis['score'],
                'strength' => $strengthAnalysis['strength'],
                'feedback' => $strengthAnalysis['feedback'],
                'is_valid' => $isValid,
                'validation_message' => $validationMessage,
                'requirements_met' => [
                    'min_length' => strlen($password) >= 8,
                    'has_uppercase' => preg_match('/[A-Z]/', $password),
                    'has_lowercase' => preg_match('/[a-z]/', $password),
                    'has_numbers' => preg_match('/[0-9]/', $password),
                    'has_special_chars' => preg_match('/[^A-Za-z0-9]/', $password),
                    'not_common' => !$this->isCommonPassword($password),
                    'no_repetition' => !preg_match('/(.)\1{3,}/', $password),
                    'no_sequences' => !$this->hasSequentialChars($password)
                ]
            ];

            return $this->success($response, 'Password strength analyzed successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to analyze password strength: ' . $e->getMessage());
        }
    }

    /**
     * Get password requirements info
     */
    public function getRequirements()
    {
        $requirements = [
            'minimum_length' => 8,
            'maximum_length' => 128,
            'required_elements' => [
                'At least one uppercase letter (A-Z)',
                'At least one lowercase letter (a-z)',
                'At least one number (0-9)',
                'At least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)',
            ],
            'restrictions' => [
                'Cannot be a commonly used password',
                'Cannot contain more than 3 repeated characters in a row',
                'Cannot contain obvious sequential patterns (abc, 123, qwe)'
            ],
            'strength_levels' => [
                'Very Weak' => '0-19 points',
                'Weak' => '20-39 points',
                'Medium' => '40-59 points',
                'Strong' => '60-79 points',
                'Very Strong' => '80-100 points'
            ]
        ];

        return $this->success($requirements, 'Password requirements retrieved successfully');
    }

    /**
     * Check if password is commonly used (private helper)
     */
    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', 'password123', '123456', '123456789', 'qwerty',
            'abc123', 'password1', 'admin', 'letmein', 'welcome',
            'monkey', '1234567890', 'iloveyou', 'princess', 'rockyou',
            '12345678', 'abc123', 'nicole', 'daniel', 'babygirl',
            'michael', 'ashley', 'qwerty123', 'welcome123', 'admin123',
            'root', 'toor', 'pass', 'test', 'guest', 'user'
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Check if password contains sequential characters (private helper)
     */
    private function hasSequentialChars(string $password): bool
    {
        $sequences = [
            'abcdefghijklmnopqrstuvwxyz',
            'zyxwvutsrqponmlkjihgfedcba',
            '01234567890',
            '09876543210',
            'qwertyuiop',
            'asdfghjkl',
            'zxcvbnm'
        ];

        $password = strtolower($password);

        foreach ($sequences as $sequence) {
            for ($i = 0; $i <= strlen($sequence) - 3; $i++) {
                $substr = substr($sequence, $i, 3);
                if (strpos($password, $substr) !== false) {
                    return true;
                }
            }
        }

        return false;
    }
}
