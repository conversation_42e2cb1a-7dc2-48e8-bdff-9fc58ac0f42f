<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Resources\CourseResource;
use App\Repositories\Contracts\CourseRepositoryInterface;
use Illuminate\Http\Request;

class CourseController extends ApiBaseController
{
    protected CourseRepositoryInterface $courseRepository;

    public function __construct(CourseRepositoryInterface $courseRepository)
    {
        $this->courseRepository = $courseRepository;
    }

    /**
     *  Get all courses
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function index(Request $request)
    {
        // Retrieve all courses using the repository
        $courses = $this->courseRepository->all();

        // Return the courses as a JSON response wrapped in the CourseResource collection
        return $this->success(CourseResource::collection($courses), "courses fetched");
    }
}
