<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Models\MaterialCurriculum;
use Illuminate\Http\Request;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\Validator;

class CurriculumController extends ApiBaseController
{
    protected CourseMaterialRepositoryInterface $courseMaterialRepository;
    
    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository)
    {
        $this->courseMaterialRepository = $courseMaterialRepository;
    }

    /**
     * Get one curriculum
     *  */ 
    public function getCurriculum(Request $request) {
        $validator = Validator::make($request->all(),[
            'id' => 'required|integer'
        ]);
        if($validator->fails()) {
            return $this->error($validator->errors(), 422);
        }
        
        $material = MaterialCurriculum::find($request->id);
        if ($material == null) {
            return $this->error("Not found", 404);
        }
        return $this->success([$material], "Course Curriculum Found");
    }

    /**
     * Get All the curriculums for a particular course material
     */
    public function getAllCurriculum(Request $request) {
        $validator = Validator::make($request->all(),[
            'material_id' => 'required|integer'
        ]);

        if($validator->fails()) {
            return $this->error($validator->errors(), 422);
        }
        
        $material = MaterialCurriculum::where("material_id", $request->material_id)->get();
        if ($material->toArray() == []) {
            return $this->error("Non Found", 404);
        } else {
            return $this->success($material, "Curriculums Found");
        }
        
    }

    /**
     * Create a curriclumn for a given course material
     */
    public function createCurriculum(Request $request) {
        $validator = Validator::make($request->all(),[
            'material_id'=> 'required',
            'level' => 'required|in:beginner,intermediate,advanced', 
            'modules' => 'required|array',
            'order' => 'required|integer'
        ]);
        if($validator->fails()) {
            return $this->error( $validator->errors(), 422);
        }
        
        try {
            
            $result = $this->courseMaterialRepository->createCurriculum($request->only(['material_id', 'level', 'modules','order']));

            if ($result) {
                return $this->success($result, 'Curriculum Created Successfully');
            } else {
                return $this->error('Level already exists',404);
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage(), 404);
        }
    }

    /**
     * Update a curriculum
     */
    public function updateCurriculum(Request $request) {
        $validator = Validator::make($request->all(),[
            'id'=> 'required',
            'level' => 'in:beginner,intermediate,advanced', 
            'modules' => 'array',
            'order' => 'integer'
        ]);

        if($validator->fails()) {
            return $this->error( $validator->errors(), 422);
        }

        try {
            $material = MaterialCurriculum::findOrfail($request->input('id'));
            
            if(isset($request->level)){
                $material->level = $request->level;
            }
            
            if(isset($request->modules)){
                $material->modules = $request->modules;
            }
            
            if(isset($request->order)){
                $material->order = $request->order;
            }
    
            $material->save();
    
            return $this->success([$material, 'Curriculum Updated Successfully'],200);

        } catch (Exception $e) {
            return $this->error($e->getMessage(), 404);
        }
    }        
}




