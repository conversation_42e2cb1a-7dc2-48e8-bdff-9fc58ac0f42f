<?php

namespace App\Http\Controllers\Api\Student;

use App\Base\ApiBaseController;
use App\Http\Requests\OnboardStudentRequest;
use App\Services\StudentService;
use App\Services\UserService;
use Illuminate\Container\Attributes\Log;
use Illuminate\Support\Facades\Log as FacadesLog;
use Spatie\Permission\Models\Role;

class OnboardStudentController extends ApiBaseController
{
    protected $studentService;
    protected $userService;

    public function __construct(StudentService $studentService, UserService $userService)
    {
        $this->studentService = $studentService;
        $this->userService = $userService;
    }

    public function __invoke(OnboardStudentRequest $request)
    {
        try {
            $data = $request->validated();
            $student = $this->studentService->onboardStudent($data);

            // check if student role exist, if not create a role called student and assign it to to the user, then switch user role to studne
            $this->studentService->assignRole($student->user, 'student');

            $student->user->switchToStudent();
            $student->fresh();

            return $this->success($student, 'Student onboarded successfully');
        } catch (\Exception $e) {
            return $this->internalServerError($e);
        }
    }
}
