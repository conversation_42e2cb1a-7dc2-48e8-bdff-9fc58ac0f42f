<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Http\Requests\MaterialPrerequisiteRequest;
use App\Models\MaterialPrerequisite;
use Illuminate\Http\Request;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Exception;
use Illuminate\Support\Facades\Validator;

class PrerequisiteController extends ApiBaseController
{
    protected CourseMaterialRepositoryInterface $courseMaterialRepository;
public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository)
    {
        $this->courseMaterialRepository = $courseMaterialRepository;
    }


    /**
     * Get one prerequisite
     *  */ 
    public function getPrerequisite(Request $request) {
        $validator = Validator::make($request->all(),[
            'id' => 'required|integer'
        ]);
        if($validator->fails()) {
            return $this->error($validator->errors(), 422);
        }
        
        $prerequisite = MaterialPrerequisite::find($request->id);
        if ($prerequisite == null) {
            return $this->error("Not found", 404);
        }
        return $this->success([$prerequisite], "Course Prerequisite Gotten");
    }


    /**
     * Create a prerequisite
     */

    public function createPrerequisite(MaterialPrerequisiteRequest $request)
    {
        try {
            $prerequisite = $this->courseMaterialRepository->createPrerequisite($request);
            return $this->success($prerequisite, 'Prerequisite Created Successfully');
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }


    /**
     * Update a curriculum
     */
    public function updatePrerequisite(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'concepts' => 'array',
            'tools' => 'array',
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors(), 422);
        }


        try {
            $prerequisite = MaterialPrerequisite::findOrfail($request->input('id'));

            if(isset($request->concepts)){
                $prerequisite->concepts = $request->concepts;
            }
            
            if(isset($request->tools)){
                $prerequisite->tools = $request->tools;
            }
    
            $prerequisite->save();
    
            return $this->success([$prerequisite, 'Prerequisite Updated Successfully'],200);

        } catch (Exception $e) {
            return $this->error($e->getMessage(), 404);
        }
    }
}
