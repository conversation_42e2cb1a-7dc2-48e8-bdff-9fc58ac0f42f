<?php

namespace App\Http\Controllers\Api\Lesson;

use App\Base\ApiBaseController;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UpdateMeetingMetaDataController extends ApiBaseController
{
    public function __invoke(Request $request, $lessonId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'meeting' => 'required|array',
                'meeting.title' => 'nullable|string',
                'meeting.endTime' => 'required|date',
                'meeting.status' => 'required|string|in:Not Started,In Progress,Completed,Cancelled',
                'host' => 'required|array',
                'host.id' => 'required|integer',
                'host.name' => 'required|string',
                'host.email' => 'required|email',
                'duration' => 'required|array',
                'duration.total' => 'required|string',
                'duration.active' => 'required|string',
                'duration.paused' => 'required|string',
                'participants' => 'required|array',
                'participants.total' => 'required|integer|min:1',
                'participants.list' => 'required|array',
                'participants.list.*.id' => 'required|integer',
                'participants.list.*.name' => 'required|string',
                'participants.list.*.role' => 'required|string|in:Host,Participant',
                'activity' => 'required|array',
                'activity.pauses' => 'required|array',
                'activity.pauses.count' => 'required|integer|min:0',
                'activity.pauses.totalDuration' => 'required|string',
                'activity.screenShare' => 'required|string|in:Yes,No',
                'activity.videoMuted' => 'required|string|in:Yes,No',
                'activity.audioMuted' => 'required|string|in:Yes,No',
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator->errors()->toArray(), 422);
            }

            $lesson = Lesson::findOrFail($lessonId);

            // // Check if user is authorized (must be either tutor or student of the lesson)
            // if (Auth::user()->id !== $lesson->tutor->user->id && Auth::user()->id !== $lesson->student->user->id) {
            //     return $this->error('You are not authorized to update this lesson\'s metadata', 403);
            // }

            $lesson->meeting_meta_data = $request->all();
            $lesson->save();

            return $this->success($lesson, 'Meeting metadata updated successfully');
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), 500);
        }
    }
}
