<?php

namespace App\Http\Controllers\Api\Lesson;

use App\Http\Controllers\LessonController;
use App\Http\Requests\ScheduleLessonRequest;
use App\Models\Course;
use App\Models\Lesson;
use App\Services\LessonService;
use App\Models\User;
use App\Services\AgoraService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ScheduleLessonController extends LessonController
{
    protected $lessonService;
    protected $agoraService;

    public function __construct(LessonService $lessonService, AgoraService $agoraService)
    {
        $this->lessonService = $lessonService;
        $this->agoraService = $agoraService;
    }

    public function __invoke(ScheduleLessonRequest $request)
    {
        try {
            $student = User::find(Auth::id());
            $tutor = User::with("tutor")->find($request->input('tutor_id'));

            // Check student's schedule
            if ($this->meetingAlreadyScheduledAtTime($student, $request->starts_at, $request->ends_at)) {
                return $this->error('Student already has a lesson scheduled during this time');
            }

            // Check tutor's schedule
            if ($this->meetingAlreadyScheduledAtTime($tutor, $request->starts_at, $request->ends_at)) {
                return $this->error('Tutor already has a lesson scheduled during this time');
            }

            if (!$this->MeetingDurationValid($request->starts_at, $request->ends_at)) {
                return $this->error('Meeting duration is invalid, must be at least 15mins from start time');
            }

            $course = Course::find($request->input('course_id'));

            $title = $course->name . " lesson with " . $tutor->first_name;

            // add the tile to the request
            $request->merge(['title' => $title]);

            $lesson = $this->lessonService->scheduleLesson($student, $tutor, $request->all());

            // Generate Agora Token
            $duration = (strtotime($request->ends_at) - strtotime($request->starts_at));

            // Round the duration to the nearest 15-minute interval
            $chanel = strtolower(Str::random(24));
            $userId = 0;
            $agoraToken = $this->agoraService->generateRtcToken($chanel, $userId, $duration);

            // Save Agora Token to the Lesson
            $lesson->agora_token = $agoraToken;
            $lesson->channel_name = $chanel;
            $lesson->save();

            $lesson->load('student', 'tutor', 'course');

            return $this->success($lesson, "Lesson scheduled successfully with Agora token");
        } catch (\Exception $e) {
            Log::error('Error:' . $e->getMessage());
            return $this->error($e->getMessage());
        }
    }

    public function regenerateAgoraToken(Request $request)
    {

        $lesson = Lesson::find($request->input('lesson_id'));
        $chanel = strtolower(Str::random(24));
        $userId = 0;
        $duration = (strtotime($request->ends_at) - strtotime($request->starts_at));

        $agoraToken = $this->agoraService->generateRtcToken($chanel, $userId, $duration);
        $lesson->agora_token = $agoraToken;
        $lesson->save();

        return $this->success($lesson, 'Agora token regenerated successfully');
    }

    public function generateNewAgoraTokenByChanel(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'channel_name' => 'required|string',

        ]);
        if ($validator->fails()) {
            return $this->error($validator->errors()->first());
        }

        $duration = (!$request->starts_at || !$request->ends_at) ? 3600 : (strtotime($request->ends_at) - strtotime($request->starts_at));
        $chanel = $request->input('channel_name');

        $userId = 0;
        $agoraToken = $this->agoraService->generateRtcToken($chanel, $userId, $duration);

        return $this->success($agoraToken, 'Agora token generated successfully');
    }
}


