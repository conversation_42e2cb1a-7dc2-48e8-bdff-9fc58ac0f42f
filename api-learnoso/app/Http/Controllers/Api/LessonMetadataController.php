<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LessonMetadataController extends Controller
{
    public function getMetadata(Lesson $lesson): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $lesson->getMetadata()
        ]);
    }

    public function updateMetadata(Request $request, Lesson $lesson): JsonResponse
    {
        $request->validate([
            'metadata' => 'required|array',
            'metadata.meeting' => 'nullable|array',
            'metadata.meeting.title' => 'nullable|string',
            'metadata.meeting.endTime' => 'nullable|string',
            'metadata.meeting.status' => 'nullable|string',
            'metadata.host' => 'nullable|array',
            'metadata.host.id' => 'nullable|integer',
            'metadata.host.name' => 'nullable|string',
            'metadata.host.email' => 'nullable|email',
            'metadata.duration' => 'nullable|array',
            'metadata.duration.total' => 'nullable|string',
            'metadata.duration.active' => 'nullable|string',
            'metadata.duration.paused' => 'nullable|string',
            'metadata.participants' => 'nullable|array',
            'metadata.participants.total' => 'nullable|integer',
            'metadata.participants.list' => 'nullable|array',
            'metadata.participants.list.*.id' => 'nullable|integer',
            'metadata.participants.list.*.name' => 'nullable|string',
            'metadata.participants.list.*.role' => 'nullable|string',
            'metadata.activity' => 'nullable|array',
            'metadata.activity.pauses' => 'nullable|array',
            'metadata.activity.pauses.count' => 'nullable|integer',
            'metadata.activity.pauses.totalDuration' => 'nullable|string',
            'metadata.activity.screenShare' => 'nullable|string',
            'metadata.activity.videoMuted' => 'nullable|string',
            'metadata.activity.audioMuted' => 'nullable|string',
        ]);

        $lesson->updateMetadata($request->metadata);

        return response()->json([
            'success' => true,
            'message' => 'Lesson metadata updated successfully',
            'data' => $lesson->getMetadata()
        ]);
    }
}
