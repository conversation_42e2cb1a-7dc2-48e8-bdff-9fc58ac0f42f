<?php


namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Requests\MaterialEvaluationRequest;
use App\Http\Requests\MaterialSubmissionRequest;
use App\Models\CourseMaterial;
use App\Models\MaterialEvaluation;
use App\Models\MaterialSubmission;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Exception;

class MaterialEvaluationController extends ApiBaseController
{
    protected CourseMaterialRepositoryInterface $courseMaterialRepository;


    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository)
    {
        $this->courseMaterialRepository = $courseMaterialRepository;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(MaterialEvaluationRequest $request)
    {
        $data = $this->courseMaterialRepository->allEvaluation($request->material_id);
        
        if($data->toArray() !== []){
            $courseName = CourseMaterial::find($request->material_id)->title;
            return $this->success($data, "All Evaluations for $courseName");
        } else {
            return $this->notFoundError("No Evaluation Found");
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(MaterialEvaluationRequest $request)
    {
        $result = $this->courseMaterialRepository->createEvaluation($request);
        if($result["status"]){
            return $this->success(["id" => $result["id"]], "Created Evaluation Successfully");
        } else {
            return $this->error($result["message"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        return $this->success($this->courseMaterialRepository->getEvaluation($id), "Successfully gotten one Evaluation");
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(MaterialEvaluationRequest $request, string $id)
    {
        $result = $this->courseMaterialRepository->updateEvaluation($request, $id);
        if($result["status"]){
            return $this->success(["id" => $result["id"]], "Updated Evaluation Successfully");
        } else {
            return $this->error($result["message"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try{
            MaterialEvaluation::destroy($id);
            return $this->success([], "Deleted Successfully");
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
