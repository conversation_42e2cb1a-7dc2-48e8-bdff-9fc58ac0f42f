<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Models\Language;
use Illuminate\Http\Request;

class LanguageController extends ApiBaseController
{
    public function index()
    {
        $languages = Language::all();
        $transformedLanguages = $this->transformLanguages($languages);

        return $this->success($transformedLanguages, 'Languages fetched!');
    }


    public function transformLanguages($languages)
    {
        return $languages->map(function ($language) {
            return [
                'id' => $language->id,
                'name' => $language->name,
            ];
        })->toArray();
    }

    public function search(Request $request)
    {
        $query = Language::query();

        // Check if the request has a 'name' parameter
        if ($request->has('name')) {
            $query->where('name', 'like', '%' . $request->input('name') . '%');
        }

        $result = $query->get();



        return $this->success($this->transformLanguages($result), 'Languages search results');
    }


}
