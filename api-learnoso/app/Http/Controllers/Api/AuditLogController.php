<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Models\AuditLog;
use App\Services\AuditLogService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AuditLogController extends ApiBaseController
{
    protected AuditLogService $auditService;

    public function __construct(AuditLogService $auditService)
    {
        $this->auditService = $auditService;
    }

    /**
     * Get audit logs with filtering and pagination
     */
    public function index(Request $request)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'event_type' => 'string',
            'user_id' => 'integer|exists:users,id',
            'actor_id' => 'integer|exists:users,id',
            'risk_level' => 'string|in:low,medium,high,critical',
            'is_suspicious' => 'boolean',
            'requires_review' => 'boolean',
            'start_date' => 'date',
            'end_date' => 'date|after_or_equal:start_date',
            'tags' => 'array',
            'per_page' => 'integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $query = AuditLog::with(['user', 'actor', 'reviewer'])
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->filled('event_type')) {
                $query->where('event_type', $request->event_type);
            }

            if ($request->filled('user_id')) {
                $query->where('user_id', $request->user_id);
            }

            if ($request->filled('actor_id')) {
                $query->where('actor_id', $request->actor_id);
            }

            if ($request->filled('risk_level')) {
                $query->where('risk_level', $request->risk_level);
            }

            if ($request->filled('is_suspicious')) {
                $query->where('is_suspicious', $request->boolean('is_suspicious'));
            }

            if ($request->filled('requires_review')) {
                $query->where('requires_review', $request->boolean('requires_review'));
            }

            if ($request->filled('start_date')) {
                $query->where('created_at', '>=', $request->start_date);
            }

            if ($request->filled('end_date')) {
                $query->where('created_at', '<=', $request->end_date);
            }

            if ($request->filled('tags')) {
                foreach ($request->tags as $tag) {
                    $query->whereJsonContains('tags', $tag);
                }
            }

            $perPage = $request->get('per_page', 20);
            $auditLogs = $query->paginate($perPage);

            return $this->success([
                'audit_logs' => $auditLogs->items(),
                'pagination' => [
                    'current_page' => $auditLogs->currentPage(),
                    'total_pages' => $auditLogs->lastPage(),
                    'per_page' => $auditLogs->perPage(),
                    'total' => $auditLogs->total()
                ]
            ], 'Audit logs retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve audit logs: ' . $e->getMessage());
        }
    }

    /**
     * Get a specific audit log
     */
    public function show(int $id)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        try {
            $auditLog = AuditLog::with(['user', 'actor', 'reviewer'])->findOrFail($id);

            return $this->success($auditLog, 'Audit log retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Audit log not found');
        }
    }

    /**
     * Get audit statistics
     */
    public function statistics(Request $request)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'days' => 'integer|min:1|max:365'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $days = $request->get('days', 30);
            $statistics = $this->auditService->getStatistics($days);

            return $this->success($statistics, 'Audit statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve audit statistics: ' . $e->getMessage());
        }
    }

    /**
     * Mark audit log as reviewed
     */
    public function markAsReviewed(Request $request, int $id)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'notes' => 'string|max:1000'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $auditLog = AuditLog::findOrFail($id);
            $reviewer = Auth::user();
            $notes = $request->input('notes');

            $auditLog->markAsReviewed($reviewer, $notes);

            // Log the review action
            $this->auditService->logAdminAction(
                'audit_review',
                "Marked audit log #{$id} as reviewed",
                $reviewer,
                null,
                ['audit_log_id' => $id, 'notes' => $notes]
            );

            return $this->success([
                'audit_log' => $auditLog->fresh(['reviewer']),
                'reviewed_at' => $auditLog->reviewed_at,
                'reviewed_by' => $auditLog->reviewer
            ], 'Audit log marked as reviewed successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to mark audit log as reviewed: ' . $e->getMessage());
        }
    }

    /**
     * Mark audit log as suspicious
     */
    public function markAsSuspicious(Request $request, int $id)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $auditLog = AuditLog::findOrFail($id);
            $reason = $request->input('reason');
            $admin = Auth::user();

            $auditLog->markAsSuspicious($reason);

            // Log the admin action
            $this->auditService->logAdminAction(
                'mark_suspicious',
                "Marked audit log #{$id} as suspicious",
                $admin,
                $auditLog->user,
                ['audit_log_id' => $id, 'reason' => $reason]
            );

            return $this->success([
                'audit_log' => $auditLog->fresh(),
                'marked_suspicious' => true,
                'reason' => $reason
            ], 'Audit log marked as suspicious successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to mark audit log as suspicious: ' . $e->getMessage());
        }
    }

    /**
     * Get events requiring review
     */
    public function pendingReview()
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        try {
            $pendingEvents = AuditLog::requiresReview()
                ->with(['user', 'actor'])
                ->orderBy('created_at', 'desc')
                ->get();

            return $this->success([
                'pending_events' => $pendingEvents,
                'count' => $pendingEvents->count()
            ], 'Pending review events retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve pending events: ' . $e->getMessage());
        }
    }

    /**
     * Get suspicious activities
     */
    public function suspicious(Request $request)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'days' => 'integer|min:1|max:90',
            'per_page' => 'integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $days = $request->get('days', 7);
            $perPage = $request->get('per_page', 20);

            $suspiciousEvents = AuditLog::suspicious()
                ->with(['user', 'actor'])
                ->where('created_at', '>=', now()->subDays($days))
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return $this->success([
                'suspicious_events' => $suspiciousEvents->items(),
                'pagination' => [
                    'current_page' => $suspiciousEvents->currentPage(),
                    'total_pages' => $suspiciousEvents->lastPage(),
                    'per_page' => $suspiciousEvents->perPage(),
                    'total' => $suspiciousEvents->total()
                ]
            ], 'Suspicious activities retrieved successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to retrieve suspicious activities: ' . $e->getMessage());
        }
    }

    /**
     * Export audit logs
     */
    public function export(Request $request)
    {
        if (!Auth::user()->hasRole(['admin', 'super-admin'])) {
            return $this->error('Unauthorized access', 403);
        }

        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'string|in:json,csv',
            'event_types' => 'array',
            'risk_levels' => 'array'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $admin = Auth::user();

            // Log the export action
            $this->auditService->logAdminAction(
                'data_export',
                'Exported audit logs from ' . $request->start_date . ' to ' . $request->end_date,
                $admin,
                null,
                [
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'format' => $request->get('format', 'json')
                ]
            );

            // Build query
            $query = AuditLog::with(['user', 'actor'])
                ->whereBetween('created_at', [$request->start_date, $request->end_date])
                ->orderBy('created_at', 'desc');

            if ($request->filled('event_types')) {
                $query->whereIn('event_type', $request->event_types);
            }

            if ($request->filled('risk_levels')) {
                $query->whereIn('risk_level', $request->risk_levels);
            }

            $auditLogs = $query->get();

            return $this->success([
                'export_data' => $auditLogs,
                'count' => $auditLogs->count(),
                'exported_at' => now()->toISOString(),
                'exported_by' => $admin->email
            ], 'Audit logs exported successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to export audit logs: ' . $e->getMessage());
        }
    }
}
