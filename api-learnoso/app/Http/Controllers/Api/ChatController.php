<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Services\ChatService;
use App\Services\RealTimeChatService;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class ChatController extends ApiBaseController
{
    protected ChatService $chatService;
    protected RealTimeChatService $realTimeChatService;

    public function __construct(ChatService $chatService, RealTimeChatService $realTimeChatService)
    {
        $this->chatService = $chatService;
        $this->realTimeChatService = $realTimeChatService;
    }

    /**
     * Get user's conversations list
     */
    public function getConversations(Request $request)
    {
        try {
            $user = Auth::user();
            $page = $request->get('page', 1);

            $conversations = $this->chatService->getUserConversations($user->id, $page);

            // Transform for API response
            $transformedConversations = $conversations->getCollection()->map(function($conversation) use ($user) {
                $otherParticipant = $conversation->getOtherParticipant($user->id);

                return [
                    'id' => $conversation->id,
                    'title' => $conversation->getConversationTitle($user->id),
                    'type' => $conversation->type,
                    'status' => $conversation->status,
                    'last_message_at' => $conversation->last_message_at?->toISOString(),
                    'unread_count' => $conversation->getUnreadCountForUser($user->id),
                    'total_messages' => $conversation->total_messages,
                    'other_participant' => [
                        'id' => $otherParticipant->id,
                        'name' => $otherParticipant->first_name . ' ' . $otherParticipant->last_name,
                        'avatar' => $otherParticipant->profile_image_url,
                        'role' => $otherParticipant->hasRole('tutor') ? 'tutor' : 'student'
                    ],
                    'lesson' => $conversation->lesson ? [
                        'id' => $conversation->lesson->id,
                        'starts_at' => $conversation->lesson->starts_at->toISOString(),
                        'course_name' => $conversation->lesson->course?->name,
                    ] : null,
                ];
            });

            $conversations->setCollection($transformedConversations);

            return $this->success($conversations, 'Conversations retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get conversations', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to retrieve conversations');
        }
    }

    /**
     * Start or get a conversation
     */
    public function startConversation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'participant_id' => 'required|exists:users,id',
            'type' => 'nullable|string|in:general,lesson_specific,support',
            'lesson_id' => 'nullable|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $participantId = $request->participant_id;

            // Prevent self-conversation
            if ($user->id === $participantId) {
                return $this->error('Cannot start conversation with yourself');
            }

            $conversation = $this->chatService->getOrCreateConversation(
                $user->id,
                $participantId,
                $request->get('type', 'general'),
                $request->lesson_id
            );

            $otherParticipant = $conversation->getOtherParticipant($user->id);

            return $this->success([
                'conversation' => [
                    'id' => $conversation->id,
                    'title' => $conversation->getConversationTitle($user->id),
                    'type' => $conversation->type,
                    'status' => $conversation->status,
                    'unread_count' => $conversation->getUnreadCountForUser($user->id),
                    'other_participant' => [
                        'id' => $otherParticipant->id,
                        'name' => $otherParticipant->first_name . ' ' . $otherParticipant->last_name,
                        'avatar' => $otherParticipant->profile_image_url,
                        'role' => $otherParticipant->hasRole('tutor') ? 'tutor' : 'student'
                    ]
                ]
            ], 'Conversation started successfully');
        } catch (\Exception $e) {
            Log::error('Failed to start conversation', [
                'user_id' => $user->id,
                'participant_id' => $request->participant_id,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to start conversation: ' . $e->getMessage());
        }
    }

    /**
     * Send a text message
     */
    public function sendMessage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'content' => 'required|string|max:5000',
            'reply_to_message_id' => 'nullable|exists:messages,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();

            $message = $this->chatService->sendMessage(
                $request->conversation_id,
                $user->id,
                $request->get('content'),
                $request->get('reply_to_message_id')
            );

            return $this->success([
                'message' => [
                    'id' => $message->id,
                    'content' => $message->content,
                    'type' => $message->type,
                    'status' => $message->status,
                    'created_at' => $message->created_at->toISOString(),
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->first_name . ' ' . $message->sender->last_name,
                    ],
                    'reply_to' => $message->replyToMessage ? [
                        'id' => $message->replyToMessage->id,
                        'content' => $message->replyToMessage->getPreview(50),
                        'sender_name' => $message->replyToMessage->sender
                            ? $message->replyToMessage->sender->first_name . ' ' . $message->replyToMessage->sender->last_name
                            : 'System'
                    ] : null
                ]
            ], 'Message sent successfully');
        } catch (\Exception $e) {
            Log::error('Failed to send message', [
                'user_id' => Auth::id(),
                'conversation_id' => $request->conversation_id,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to send message: ' . $e->getMessage());
        }
    }

    /**
     * Send message with file attachment
     */
    public function sendMessageWithFile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'content' => 'nullable|string|max:1000',
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $file = $request->file('file');

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

            if (!in_array($file->getMimeType(), $allowedTypes)) {
                return $this->error('File type not allowed');
            }

            $message = $this->chatService->sendMessageWithFile(
                $request->conversation_id,
                $user->id,
                $request->get('content') ?? '',
                $file
            );

            return $this->success([
                'message' => [
                    'id' => $message->id,
                    'content' => $message->content,
                    'type' => $message->type,
                    'status' => $message->status,
                    'created_at' => $message->created_at->toISOString(),
                    'attachment' => [
                        'file_name' => $message->file_name,
                        'file_type' => $message->file_type,
                        'file_size' => $message->getFormattedFileSize(),
                        'file_url' => $message->getFileUrl(),
                    ],
                    'sender' => [
                        'id' => $message->sender->id,
                        'name' => $message->sender->first_name . ' ' . $message->sender->last_name,
                    ]
                ]
            ], 'Message with file sent successfully');
        } catch (\Exception $e) {
            Log::error('Failed to send message with file', [
                'user_id' => Auth::id(),
                'conversation_id' => $request->conversation_id,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to send message with file: ' . $e->getMessage());
        }
    }

    /**
     * Get messages in a conversation
     */
    public function getMessages(Request $request, $conversationId)
    {
        $validator = Validator::make(['conversation_id' => $conversationId], [
            'conversation_id' => 'required|exists:conversations,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $page = $request->get('page', 1);

            $messages = $this->chatService->getConversationMessages($conversationId, $user->id, $page);

            // Transform messages for API response
            $transformedMessages = $messages->getCollection()->map(function($message) {
                return [
                    'id' => $message->id,
                    'content' => $message->content,
                    'type' => $message->type,
                    'status' => $message->status,
                    'created_at' => $message->created_at->toISOString(),
                    'sender' => $message->sender ? [
                        'id' => $message->sender->id,
                        'name' => $message->sender->first_name . ' ' . $message->sender->last_name,
                        'avatar' => $message->sender->profile_image_url,
                    ] : ['id' => null, 'name' => 'System', 'avatar' => null],
                    'reply_to' => $message->replyToMessage ? [
                        'id' => $message->replyToMessage->id,
                        'content' => $message->replyToMessage->getPreview(50),
                        'sender_name' => $message->replyToMessage->sender
                            ? $message->replyToMessage->sender->first_name . ' ' . $message->replyToMessage->sender->last_name
                            : 'System'
                    ] : null,
                    'attachment' => $message->hasAttachment() ? [
                        'file_name' => $message->file_name,
                        'file_type' => $message->file_type,
                        'file_size' => $message->getFormattedFileSize(),
                        'file_url' => $message->getFileUrl(),
                    ] : null,
                    'metadata' => $message->metadata,
                ];
            });

            $messages->setCollection($transformedMessages);

            return $this->success($messages, 'Messages retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get messages', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to retrieve messages: ' . $e->getMessage());
        }
    }

    /**
     * Mark conversation as read
     */
    public function markAsRead(Request $request, $conversationId)
    {
        try {
            $user = Auth::user();

            $success = $this->chatService->markConversationAsRead($conversationId, $user->id);

            if ($success) {
                return $this->success(null, 'Conversation marked as read');
            } else {
                return $this->error('Failed to mark conversation as read');
            }
        } catch (\Exception $e) {
            Log::error('Failed to mark conversation as read', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to mark conversation as read');
        }
    }

    /**
     * Get unread message count
     */
    public function getUnreadCount(Request $request)
    {
        try {
            $user = Auth::user();
            $count = $this->chatService->getUnreadMessageCount($user->id);

            return $this->success([
                'unread_count' => $count,
                'user_id' => $user->id
            ], 'Unread count retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get unread count', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to get unread count');
        }
    }

    /**
     * Search messages
     */
    public function searchMessages(Request $request, $conversationId)
    {
        $validator = Validator::make(array_merge($request->all(), ['conversation_id' => $conversationId]), [
            'conversation_id' => 'required|exists:conversations,id',
            'query' => 'required|string|min:2|max:100'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $page = $request->get('page', 1);

            $messages = $this->chatService->searchMessages($conversationId, $user->id, $request->get('query'), $page);

            return $this->success($messages, 'Search results retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to search messages', [
                'user_id' => Auth::id(),
                'conversation_id' => $conversationId,
                'query' => $request->query,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to search messages: ' . $e->getMessage());
        }
    }

    /**
     * Delete a message
     */
    public function deleteMessage(Request $request, $messageId)
    {
        try {
            $user = Auth::user();

            $success = $this->chatService->deleteMessage($messageId, $user->id);

            if ($success) {
                return $this->success(null, 'Message deleted successfully');
            } else {
                return $this->error('Failed to delete message or unauthorized');
            }
        } catch (\Exception $e) {
            Log::error('Failed to delete message', [
                'user_id' => Auth::id(),
                'message_id' => $messageId,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to delete message: ' . $e->getMessage());
        }
    }

    /**
     * Send lesson link
     */
    public function sendLessonLink(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'lesson_id' => 'required|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $lesson = Lesson::with('course')->findOrFail($request->lesson_id);

            $message = $this->chatService->sendLessonLink($request->conversation_id, $user->id, $lesson);

            return $this->success([
                'message' => [
                    'id' => $message->id,
                    'content' => $message->content,
                    'type' => $message->type,
                    'metadata' => $message->metadata,
                    'created_at' => $message->created_at->toISOString(),
                ]
            ], 'Lesson link sent successfully');
        } catch (\Exception $e) {
            Log::error('Failed to send lesson link', [
                'user_id' => Auth::id(),
                'conversation_id' => $request->conversation_id,
                'lesson_id' => $request->lesson_id,
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to send lesson link: ' . $e->getMessage());
        }
    }

    /**
     * Get online users (for initial load)
     */
    public function getOnlineUsers(Request $request)
    {
        try {
            $onlineUsers = $this->realTimeChatService->getOnlineUsers();

            return $this->success([
                'online_users' => $onlineUsers,
                'count' => count($onlineUsers)
            ], 'Online users retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get online users', [
                'error' => $e->getMessage()
            ]);
            return $this->error('Failed to get online users');
        }
    }
}
