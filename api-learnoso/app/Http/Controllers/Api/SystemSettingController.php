<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class SystemSettingController extends ApiBaseController
{
    public function index(Request $request)
    {
        // Only admins can view all settings
        if (!Auth::user()->hasRole('admin')) {
            return $this->forbiddenError('Only administrators can view system settings');
        }

        $settings = SystemSetting::first();
        return $this->success($settings ? $settings->settings : [], 'System settings retrieved successfully');
    }

    public function update(Request $request)
    {
        // Only admins can update settings
        if (!Auth::user()->hasRole('admin')) {
            return $this->forbiddenError('Only administrators can update system settings');
        }

        $validator = Validator::make($request->all(), [
            'settings' => 'required|array',
            'settings.*' => 'nullable'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $setting = SystemSetting::first();
            if (!$setting) {
                $setting = new SystemSetting();
            }

            $setting->settings = $request->settings;
            $setting->save();

            return $this->success($setting->settings, 'System settings updated successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to update system settings: ' . $e->getMessage());
        }
    }

    public function getCommissionSettings()
    {
        $settings = SystemSetting::first();
        $commissionSettings = $settings ? [
            'lesson_commission_rate' => $settings->settings['lesson_commission_rate'] ?? 0.10,
            'minimum_commission_amount' => $settings->settings['minimum_commission_amount'] ?? 1.00,
            'maximum_commission_amount' => $settings->settings['maximum_commission_amount'] ?? 50.00
        ] : [
            'lesson_commission_rate' => 0.10,
            'minimum_commission_amount' => 1.00,
            'maximum_commission_amount' => 50.00
        ];

        return $this->success($commissionSettings, 'Commission settings retrieved successfully');
    }
}
