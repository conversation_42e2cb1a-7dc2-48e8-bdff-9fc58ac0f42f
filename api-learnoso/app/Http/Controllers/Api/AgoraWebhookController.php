<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Models\Lesson;
use App\Models\LessonTracker;
use App\Services\LessonTrackerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Exception;

class AgoraWebhookController extends ApiBaseController
{
    protected LessonTrackerService $trackerService;

    public function __construct(LessonTrackerService $trackerService)
    {
        $this->trackerService = $trackerService;
    }

    /**
     * Handle Agora channel events (user join/leave)
     */
    public function handleChannelEvent(Request $request)
    {
        try {
            $payload = $request->all();

            Log::info('Agora webhook received', [
                'payload' => $payload,
                'headers' => $request->headers->all()
            ]);

            // Validate the webhook payload
            $validator = Validator::make($payload, [
                'eventType' => 'required|string',
                'channelName' => 'required|string',
                'uid' => 'required',
                'eventMs' => 'required|integer'
            ]);

            if ($validator->fails()) {
                Log::warning('Invalid Agora webhook payload', [
                    'errors' => $validator->errors(),
                    'payload' => $payload
                ]);
                return $this->validationError($validator->errors()->toArray());
            }

            // Process the event based on type
            $result = $this->processAgoraEvent($payload);

            return $this->success($result, 'Agora event processed successfully');

        } catch (Exception $e) {
            Log::error('Failed to process Agora webhook', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return $this->error('Failed to process webhook: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Process specific Agora events
     */
    private function processAgoraEvent(array $payload): array
    {
        $eventType = $payload['eventType'];
        $channelName = $payload['channelName'];
        $uid = $payload['uid'];
        $eventTimestamp = $payload['eventMs'];

        // Find the lesson by channel name
        $lesson = Lesson::where('channel_name', $channelName)->first();

        if (!$lesson) {
            Log::warning('Lesson not found for Agora channel', [
                'channel_name' => $channelName,
                'event_type' => $eventType
            ]);
            return ['message' => 'Lesson not found for channel'];
        }

        switch ($eventType) {
            case 'user_joined':
                return $this->handleUserJoined($lesson, $uid, $eventTimestamp);

            case 'user_left':
                return $this->handleUserLeft($lesson, $uid, $eventTimestamp);

            case 'channel_created':
                return $this->handleChannelCreated($lesson, $eventTimestamp);

            case 'channel_destroyed':
                return $this->handleChannelDestroyed($lesson, $eventTimestamp);

            default:
                Log::info('Unhandled Agora event type', [
                    'event_type' => $eventType,
                    'lesson_id' => $lesson->id
                ]);
                return ['message' => 'Event type not handled'];
        }
    }

    /**
     * Handle user joining the channel
     */
    private function handleUserJoined(Lesson $lesson, $uid, int $timestamp): array
    {
        try {
            // Get or create tracker
            $tracker = $this->trackerService->initializeTracker($lesson);

            // Auto-start session if this is the first user joining
            if (!$tracker->is_active && $tracker->status === 'not_started') {
                $this->trackerService->startSession($lesson->id, $lesson->channel_name);

                Log::info('Auto-started lesson session via Agora webhook', [
                    'lesson_id' => $lesson->id,
                    'uid' => $uid,
                    'timestamp' => $timestamp
                ]);
            }

            // Update session metadata with participant info
            $currentMetadata = $tracker->session_metadata ?? [];
            $participants = $currentMetadata['participants'] ?? [];

            // Add participant if not already present
            if (!in_array($uid, array_column($participants, 'uid'))) {
                $participants[] = [
                    'uid' => $uid,
                    'joined_at' => now()->toISOString(),
                    'status' => 'active'
                ];
            }

            $tracker->update([
                'session_metadata' => array_merge($currentMetadata, [
                    'participants' => $participants,
                    'last_activity' => now()->toISOString()
                ])
            ]);

            return [
                'action' => 'user_joined_processed',
                'lesson_id' => $lesson->id,
                'tracker_id' => $tracker->id,
                'participants_count' => count($participants)
            ];

        } catch (Exception $e) {
            Log::error('Failed to handle user joined event', [
                'lesson_id' => $lesson->id,
                'uid' => $uid,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Handle user leaving the channel
     */
    private function handleUserLeft(Lesson $lesson, $uid, int $timestamp): array
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lesson->id)->first();

            if (!$tracker) {
                return ['message' => 'No tracker found for lesson'];
            }

            // Update participant status in metadata
            $currentMetadata = $tracker->session_metadata ?? [];
            $participants = $currentMetadata['participants'] ?? [];

            // Update participant status to left
            foreach ($participants as &$participant) {
                if ($participant['uid'] == $uid) {
                    $participant['status'] = 'left';
                    $participant['left_at'] = now()->toISOString();
                    break;
                }
            }

            $tracker->update([
                'session_metadata' => array_merge($currentMetadata, [
                    'participants' => $participants,
                    'last_activity' => now()->toISOString()
                ])
            ]);

            // Check if all participants have left - auto-end session if so
            $activeParticipants = array_filter($participants, function($p) {
                return $p['status'] === 'active';
            });

            if (empty($activeParticipants) && $tracker->is_active) {
                $this->trackerService->endSession($lesson->id);

                Log::info('Auto-ended lesson session - all participants left', [
                    'lesson_id' => $lesson->id,
                    'uid' => $uid
                ]);

                return [
                    'action' => 'session_auto_ended',
                    'lesson_id' => $lesson->id,
                    'reason' => 'all_participants_left'
                ];
            }

            return [
                'action' => 'user_left_processed',
                'lesson_id' => $lesson->id,
                'active_participants' => count($activeParticipants)
            ];

        } catch (Exception $e) {
            Log::error('Failed to handle user left event', [
                'lesson_id' => $lesson->id,
                'uid' => $uid,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Handle channel creation
     */
    private function handleChannelCreated(Lesson $lesson, int $timestamp): array
    {
        Log::info('Agora channel created', [
            'lesson_id' => $lesson->id,
            'channel_name' => $lesson->channel_name,
            'timestamp' => $timestamp
        ]);

        return [
            'action' => 'channel_created',
            'lesson_id' => $lesson->id,
            'channel_name' => $lesson->channel_name
        ];
    }

    /**
     * Handle channel destruction
     */
    private function handleChannelDestroyed(Lesson $lesson, int $timestamp): array
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lesson->id)->where('is_active', true)->first();

            if ($tracker) {
                $this->trackerService->endSession($lesson->id);

                Log::info('Auto-ended lesson session - Agora channel destroyed', [
                    'lesson_id' => $lesson->id,
                    'timestamp' => $timestamp
                ]);
            }

            return [
                'action' => 'channel_destroyed_processed',
                'lesson_id' => $lesson->id,
                'session_ended' => $tracker ? true : false
            ];

        } catch (Exception $e) {
            Log::error('Failed to handle channel destroyed event', [
                'lesson_id' => $lesson->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Handle Agora recording events
     */
    public function handleRecordingEvent(Request $request)
    {
        try {
            $payload = $request->all();

            Log::info('Agora recording webhook received', [
                'payload' => $payload
            ]);

            // Extract recording information
            $eventType = $payload['eventType'] ?? null;
            $channelName = $payload['channelName'] ?? null;

            if ($eventType && $channelName) {
                $lesson = Lesson::where('channel_name', $channelName)->first();

                if ($lesson) {
                    $tracker = LessonTracker::where('lesson_id', $lesson->id)->first();

                    if ($tracker) {
                        $currentMetadata = $tracker->session_metadata ?? [];
                        $recordingData = $currentMetadata['recording'] ?? [];

                        $recordingData[] = [
                            'event' => $eventType,
                            'timestamp' => now()->toISOString(),
                            'payload' => $payload
                        ];

                        $tracker->update([
                            'session_metadata' => array_merge($currentMetadata, [
                                'recording' => $recordingData
                            ])
                        ]);
                    }
                }
            }

            return $this->success(['status' => 'processed'], 'Recording event processed');

        } catch (Exception $e) {
            Log::error('Failed to process Agora recording webhook', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return $this->error('Failed to process recording webhook', 500);
        }
    }

    /**
     * Get real-time session participants
     */
    public function getSessionParticipants(Request $request, $lessonId)
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lessonId)->first();

            if (!$tracker) {
                return $this->error('Session not found', 404);
            }

            $metadata = $tracker->session_metadata ?? [];
            $participants = $metadata['participants'] ?? [];

            $activeParticipants = array_filter($participants, function($p) {
                return $p['status'] === 'active';
            });

            return $this->success([
                'lesson_id' => $lessonId,
                'total_participants' => count($participants),
                'active_participants' => count($activeParticipants),
                'participants' => $participants
            ], 'Participants retrieved successfully');

        } catch (Exception $e) {
            return $this->error('Failed to get participants: ' . $e->getMessage(), 500);
        }
    }
}
