<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Services\RealTimeNotificationService;
use App\Models\Lesson;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class RealTimeController extends ApiBaseController
{
    protected RealTimeNotificationService $realTimeService;

    public function __construct(RealTimeNotificationService $realTimeService)
    {
        $this->realTimeService = $realTimeService;
    }

    /**
     * Send a custom notification to a user
     */
    public function sendNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'type' => 'required|string|in:info,warning,error,success',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = User::findOrFail($request->user_id);

            $notification = [
                'type' => $request->type,
                'title' => $request->title,
                'message' => $request->message,
                'data' => $request->data ?? []
            ];

            $success = $this->realTimeService->sendNotificationToUser($user, $notification);

            if ($success) {
                return $this->success(null, 'Notification sent successfully');
            } else {
                return $this->error('Failed to send notification');
            }
        } catch (\Exception $e) {
            Log::error('Failed to send custom notification', [
                'error' => $e->getMessage(),
                'user_id' => $request->user_id
            ]);
            return $this->error('Failed to send notification: ' . $e->getMessage());
        }
    }

    /**
     * Send lesson reminder notifications
     */
    public function sendLessonReminder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|exists:lessons,id',
            'minutes_until_start' => 'required|integer|min:1|max:60'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $lesson = Lesson::with(['student', 'tutor'])->findOrFail($request->lesson_id);

            $success = $this->realTimeService->sendLessonReminder(
                $lesson,
                $request->minutes_until_start
            );

            if ($success) {
                return $this->success(null, 'Lesson reminder sent successfully');
            } else {
                return $this->error('Failed to send lesson reminder');
            }
        } catch (\Exception $e) {
            Log::error('Failed to send lesson reminder', [
                'error' => $e->getMessage(),
                'lesson_id' => $request->lesson_id
            ]);
            return $this->error('Failed to send lesson reminder: ' . $e->getMessage());
        }
    }

    /**
     * Broadcast system maintenance notification
     */
    public function broadcastSystemMaintenance(Request $request)
    {
        // Check if user has admin permissions
        $user = Auth::user();
        if (!$user->hasRole('admin') && !$user->hasRole('super-admin')) {
            return $this->error('Unauthorized', 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
            'scheduled_time' => 'nullable|date_format:Y-m-d H:i:s'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $scheduledTime = $request->scheduled_time
                ? new \DateTime($request->scheduled_time)
                : null;

            $success = $this->realTimeService->broadcastSystemMaintenance(
                $request->message,
                $scheduledTime
            );

            if ($success) {
                return $this->success(null, 'System maintenance notification broadcasted successfully');
            } else {
                return $this->error('Failed to broadcast system maintenance notification');
            }
        } catch (\Exception $e) {
            Log::error('Failed to broadcast system maintenance', [
                'error' => $e->getMessage(),
                'admin_id' => $user->id
            ]);
            return $this->error('Failed to broadcast maintenance notification: ' . $e->getMessage());
        }
    }

    /**
     * Get user's notification count
     */
    public function getNotificationCount(Request $request)
    {
        try {
            $user = Auth::user();
            $count = $this->realTimeService->getUnreadNotificationCount($user);

            return $this->success([
                'unread_count' => $count,
                'user_id' => $user->id
            ], 'Notification count retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get notification count', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return $this->error('Failed to get notification count');
        }
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'notification_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $success = $this->realTimeService->markNotificationAsRead(
                $user,
                $request->notification_id
            );

            if ($success) {
                return $this->success(null, 'Notification marked as read');
            } else {
                return $this->error('Failed to mark notification as read');
            }
        } catch (\Exception $e) {
            Log::error('Failed to mark notification as read', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'notification_id' => $request->notification_id
            ]);
            return $this->error('Failed to mark notification as read');
        }
    }

    /**
     * Test broadcasting functionality
     */
    public function testBroadcast(Request $request)
    {
        // Only allow in development/testing environments
        if (!config('app.debug')) {
            return $this->error('Test endpoint only available in debug mode', 403);
        }

        try {
            $user = Auth::user();

            $testNotification = [
                'type' => 'info',
                'title' => 'Test Notification',
                'message' => 'This is a test real-time notification',
                'data' => [
                    'test' => true,
                    'timestamp' => now()->toISOString()
                ]
            ];

            $success = $this->realTimeService->sendNotificationToUser($user, $testNotification);

            return $this->success([
                'broadcast_success' => $success,
                'test_notification' => $testNotification
            ], 'Test broadcast completed');
        } catch (\Exception $e) {
            Log::error('Test broadcast failed', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return $this->error('Test broadcast failed: ' . $e->getMessage());
        }
    }

    /**
     * Get broadcasting configuration for frontend
     */
    public function getBroadcastingConfig(Request $request)
    {
        try {
            $user = Auth::user();

            $config = [
                'pusher' => [
                    'key' => config('broadcasting.connections.pusher.key'),
                    'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                    'encrypted' => config('broadcasting.connections.pusher.options.encrypted', true)
                ],
                'channels' => [
                    'user_notifications' => "private-user.{$user->id}.notifications",
                    'available_channels' => [
                        'user_notifications',
                        'lesson_updates',
                        'dashboard_updates'
                    ]
                ],
                'auth_endpoint' => '/broadcasting/auth',
                'csrf_token' => csrf_token()
            ];

            return $this->success($config, 'Broadcasting configuration retrieved successfully');
        } catch (\Exception $e) {
            Log::error('Failed to get broadcasting config', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id()
            ]);
            return $this->error('Failed to get broadcasting configuration');
        }
    }

    /**
     * Send payment notification
     */
    public function sendPaymentNotification(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = User::findOrFail($request->user_id);

            $success = $this->realTimeService->sendPaymentReceivedNotification(
                $user,
                $request->amount,
                $request->description ?? ''
            );

            if ($success) {
                return $this->success(null, 'Payment notification sent successfully');
            } else {
                return $this->error('Failed to send payment notification');
            }
        } catch (\Exception $e) {
            Log::error('Failed to send payment notification', [
                'error' => $e->getMessage(),
                'user_id' => $request->user_id
            ]);
            return $this->error('Failed to send payment notification: ' . $e->getMessage());
        }
    }
}
