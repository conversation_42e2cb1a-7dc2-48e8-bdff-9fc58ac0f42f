<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class NotificationSettingsController extends ApiBaseController
{
    public function getSettings()
    {
        $user = Auth::user();
        $settings = $user->getNotificationSettings();

        return $this->success($settings, 'Notification settings retrieved successfully');
    }

    public function updateSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'boolean',
            'push' => 'boolean',
            'sms' => 'boolean',
            'notification_types' => 'array',
            'notification_types.lesson_scheduled' => 'boolean',
            'notification_types.lesson_cancelled' => 'boolean',
            'notification_types.lesson_reminder' => 'boolean',
            'notification_types.payment_received' => 'boolean',
            'notification_types.withdrawal_processed' => 'boolean',
            'notification_types.system_updates' => 'boolean',

            // Real-time chat notification preferences
            'real_time_chat' => 'array',
            'real_time_chat.new_messages' => 'boolean',
            'real_time_chat.typing_indicators' => 'boolean',
            'real_time_chat.online_status' => 'boolean',
            'real_time_chat.message_sounds' => 'boolean',
            'real_time_chat.desktop_notifications' => 'boolean',
            'real_time_chat.email_for_missed_messages' => 'boolean',
            'real_time_chat.conversation_notifications' => 'boolean',

            // Browser notification preferences
            'browser_notifications' => 'array',
            'browser_notifications.enabled' => 'boolean',
            'browser_notifications.chat_messages' => 'boolean',
            'browser_notifications.lesson_updates' => 'boolean',
            'browser_notifications.system_alerts' => 'boolean',

            // Mobile push notification preferences
            'mobile_push' => 'array',
            'mobile_push.enabled' => 'boolean',
            'mobile_push.chat_messages' => 'boolean',
            'mobile_push.lesson_reminders' => 'boolean',
            'mobile_push.payment_updates' => 'boolean'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $user = Auth::user();
            $user->updateNotificationSettings($request->all());

            return $this->success($user->getNotificationSettings(), 'Notification settings updated successfully');
        } catch (\Exception $e) {
            return $this->error('Failed to update notification settings: ' . $e->getMessage());
        }
    }
}
