<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Base\ApiBaseController;
use App\Http\Requests\MaterialReviewsRequest;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Illuminate\Validation\Rules\Exists;

class MaterialReviewsController extends ApiBaseController
{
    protected CourseMaterialRepositoryInterface $courseMaterialRepository;

    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository){
        $this->courseMaterialRepository = $courseMaterialRepository;
    }
    public function create(MaterialReviewsRequest $request)
    {
        $result = $this->courseMaterialRepository->createReview($request);

        if($result['status']){
            return $this->success($result['data'], message: "Created Review Successfully");
        }
        else{
            return $this->error($result["message"]);
        }
    }


    public function index(MaterialReviewsRequest $request){
        $result = $this->courseMaterialRepository->getReview($request->material_id);
        if($result['status']){
            return $this->success($result["data"], message:'Successfully gotten a Review');
        }
        else{
            return $this->error(message:$result['message']);
        }
    }

    public function update(MaterialReviewsRequest $request){
        $review = $this->courseMaterialRepository->updateReview($request);
        if($review['status']){
            return $this->success($review['data'], message:'Review updated successfully');
        }
        else{
            return $this->error($review['message']);
        }
    }
    public function destroy(MaterialReviewsRequest $request){
        $result = $this->courseMaterialRepository->deleteReview($request->id);
        if($result['status']){
            return $this->success(message:'Successfully Deleted Review');
        }
        else{
            return $this->error(message:'Failed to Delete Review');
        }
    }
}
