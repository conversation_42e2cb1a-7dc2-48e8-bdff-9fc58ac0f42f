<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Requests\CourseMaterialRequest;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Exception;

class CourseMaterialController extends ApiBaseController
{
    protected CourseMaterialRepositoryInterface $courseMaterialRepository;

    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository)
    {
        $this->courseMaterialRepository = $courseMaterialRepository;
    }

    /**
     * Display a listing of course materials.
     */
    public function index(Request $request)
    {
        $filters = $request->all();
        $materials = $this->courseMaterialRepository->all($filters);

        return $this->success($materials, "Course materials retrieved successfully");
    }

    /**
     * Store a newly created course material.
     */
    public function store(CourseMaterialRequest $request)
    {
        try {
            $data = $request->all();
            // Set the tutor ID from the authenticated user
            // Create the course material
            $material = $this->courseMaterialRepository->create($data);

            return $this->success($material, "Course material created successfully", 201);
        } catch (Exception $e) {
            return $this->error($e->getMessage(), 422);
        }
    }

    /**
     * Display the specified course material.
     */
    public function show($id)
    {
        $material = $this->courseMaterialRepository->find($id);

        return $this->success($material, "Course material retrieved successfully", 200);
    }

    /**
     * Update the specified course material.
     */
    public function update(CourseMaterialRequest $request, $id)
    {
        $material = $this->courseMaterialRepository->find($id);

        // Check if the user is authorized to update this material
        if (Auth::id() !== $material->tutor_id && !Auth::user()->hasRole('admin')) {
            return $this->error("Unauthorized to update this material", 403);
        }

        $data = $request->validated();
        $material = $this->courseMaterialRepository->update($id, $data);

        return $this->success($material, "Course material updated successfully");
    }

    /**
     * Remove the specified course material.
     */
    public function destroy($id)
    {
        $material = $this->courseMaterialRepository->find($id);

        // Check if the user is authorized to delete this material
        if (Auth::id() !== $material->tutor_id && !Auth::user()->hasRole('admin')) {
            return $this->error("Unauthorized to delete this material", 403);
        }

        $this->courseMaterialRepository->delete($id);

        return $this->success(null, "Course material deleted successfully");
    }
}
