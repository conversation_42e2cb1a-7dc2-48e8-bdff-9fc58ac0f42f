<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Controllers\Controller;
use App\Services\LessonTrackerService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Exception;

class LessonTrackerController extends ApiBaseController
{
    protected LessonTrackerService $trackerService;

    public function __construct(LessonTrackerService $trackerService)
    {
        $this->trackerService = $trackerService;
    }

    /**
     * Start a lesson tracking session
     */
    public function startSession(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id',
            'agora_channel' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $tracker = $this->trackerService->startSession(
                $request->lesson_id,
                $request->agora_channel
            );

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'session_started_at' => $tracker->session_started_at->toISOString(),
                'is_active' => $tracker->is_active
            ], 'Lesson session started successfully');

        } catch (Exception $e) {
            return $this->error('Failed to start lesson session: ' . $e->getMessage(), 500);
        }
    }

    /**
     * End a lesson tracking session
     */
    public function endSession(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $tracker = $this->trackerService->endSession($request->lesson_id);

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'session_ended_at' => $tracker->session_ended_at->toISOString(),
                'total_duration' => [
                    'seconds' => $tracker->total_duration_seconds,
                    'formatted' => $tracker->getFormattedTotalDuration()
                ],
                'active_duration' => [
                    'seconds' => $tracker->active_duration_seconds,
                    'formatted' => $tracker->getFormattedActiveDuration()
                ],
                'break_duration' => [
                    'seconds' => $tracker->break_duration_seconds,
                    'formatted' => $tracker->getFormattedBreakDuration()
                ],
                'break_count' => $tracker->break_count,
                'billable_amount' => $tracker->billable_amount,
                'is_active' => $tracker->is_active
            ], 'Lesson session ended successfully');

        } catch (Exception $e) {
            return $this->error('Failed to end lesson session: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Start a break during the lesson
     */
    public function startBreak(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $tracker = $this->trackerService->startBreak($request->lesson_id);

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'break_started_at' => $tracker->last_break_started_at->toISOString(),
                'is_on_break' => $tracker->isOnBreak()
            ], 'Break started successfully');

        } catch (Exception $e) {
            return $this->error('Failed to start break: ' . $e->getMessage(), 500);
        }
    }

    /**
     * End a break during the lesson
     */
    public function endBreak(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $tracker = $this->trackerService->endBreak($request->lesson_id);

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'break_count' => $tracker->break_count,
                'total_break_duration' => [
                    'seconds' => $tracker->break_duration_seconds,
                    'formatted' => $tracker->getFormattedBreakDuration()
                ],
                'is_on_break' => $tracker->isOnBreak()
            ], 'Break ended successfully');

        } catch (Exception $e) {
            return $this->error('Failed to end break: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get real-time session status
     */
    public function getSessionStatus(Request $request, $lessonId)
    {
        try {
            $status = $this->trackerService->getSessionStatus($lessonId);

            return $this->success($status, 'Session status retrieved successfully');

        } catch (Exception $e) {
            return $this->error('Failed to get session status: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get comprehensive lesson statistics
     */
    public function getLessonStatistics(Request $request, $lessonId)
    {
        try {
            $statistics = $this->trackerService->getLessonStatistics($lessonId);

            if (isset($statistics['error'])) {
                return $this->error($statistics['error'], 404);
            }

            return $this->success($statistics, 'Lesson statistics retrieved successfully');

        } catch (Exception $e) {
            return $this->error('Failed to get lesson statistics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get active sessions for the authenticated user
     */
    public function getActiveSessions(Request $request)
    {
        try {
            $userId = Auth::id();
            $activeSessions = $this->trackerService->getActiveSessionsForUser($userId);

            return $this->success([
                'active_sessions' => $activeSessions,
                'total_active' => count($activeSessions)
            ], 'Active sessions retrieved successfully');

        } catch (Exception $e) {
            return $this->error('Failed to get active sessions: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Initialize tracker for a lesson (called when lesson starts)
     */
    public function initializeTracker(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $lesson = \App\Models\Lesson::findOrFail($request->lesson_id);
            $tracker = $this->trackerService->initializeTracker($lesson);

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'is_active' => $tracker->is_active,
                'agora_channel' => $tracker->agora_channel
            ], 'Lesson tracker initialized successfully');

        } catch (Exception $e) {
            return $this->error('Failed to initialize tracker: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get session history for a specific lesson
     */
    public function getSessionHistory(Request $request, $lessonId)
    {
        try {
            $tracker = \App\Models\LessonTracker::with(['lesson', 'student', 'tutor'])
                ->where('lesson_id', $lessonId)
                ->first();

            if (!$tracker) {
                return $this->error('Session history not found', 404);
            }

            $history = [
                'lesson_id' => $lessonId,
                'tracker_id' => $tracker->id,
                'status' => $tracker->status,
                'session_timeline' => [
                    'started_at' => $tracker->session_started_at?->toISOString(),
                    'ended_at' => $tracker->session_ended_at?->toISOString()
                ],
                'durations' => [
                    'total' => [
                        'seconds' => $tracker->total_duration_seconds,
                        'formatted' => $tracker->getFormattedTotalDuration()
                    ],
                    'active' => [
                        'seconds' => $tracker->active_duration_seconds,
                        'formatted' => $tracker->getFormattedActiveDuration()
                    ],
                    'breaks' => [
                        'seconds' => $tracker->break_duration_seconds,
                        'formatted' => $tracker->getFormattedBreakDuration()
                    ]
                ],
                'break_summary' => [
                    'count' => $tracker->break_count,
                    'total_duration' => $tracker->getFormattedBreakDuration()
                ],
                'billing' => [
                    'is_billable' => $tracker->is_billable,
                    'amount' => $tracker->billable_amount,
                    'minimum_session_time' => $tracker->minimum_session_seconds
                ],
                'participants' => [
                    'student' => $tracker->student ? [
                        'id' => $tracker->student->id,
                        'name' => $tracker->student->first_name . ' ' . $tracker->student->last_name
                    ] : null,
                    'tutor' => $tracker->tutor ? [
                        'id' => $tracker->tutor->id,
                        'name' => $tracker->tutor->first_name . ' ' . $tracker->tutor->last_name
                    ] : null
                ]
            ];

            return $this->success($history, 'Session history retrieved successfully');

        } catch (Exception $e) {
            return $this->error('Failed to get session history: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update session metadata
     */
    public function updateSessionMetadata(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|integer|exists:lessons,id',
            'metadata' => 'required|array'
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        try {
            $tracker = \App\Models\LessonTracker::where('lesson_id', $request->lesson_id)->firstOrFail();

            $currentMetadata = $tracker->session_metadata ?? [];
            $updatedMetadata = array_merge($currentMetadata, $request->metadata);

            $tracker->update(['session_metadata' => $updatedMetadata]);

            return $this->success([
                'tracker_id' => $tracker->id,
                'lesson_id' => $tracker->lesson_id,
                'metadata' => $tracker->session_metadata
            ], 'Session metadata updated successfully');

        } catch (Exception $e) {
            return $this->error('Failed to update session metadata: ' . $e->getMessage(), 500);
        }
    }
}
