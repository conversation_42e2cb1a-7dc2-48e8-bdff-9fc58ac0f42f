<?php

namespace App\Http\Controllers\Api;

use App\Enums\KeyCurrencies;
use App\Base\ApiBaseController;
use App\Enums\SupportCurrency;
use App\Services\ExchangeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CurrencyController extends ApiBaseController
{
    public function getAll()
    {
        return $this->success(SupportCurrency::getKeyValuePairs());
    }

    public function convertCurrency(Request $request)
    {
        $validated = $request->validate([
            'from_currency' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'to_currency' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'amount' => 'required|numeric|min:1',
        ]);

        try {
            $fromCurrency = $validated['from_currency'];
            $toCurrency = $validated['to_currency'];
            $amount = $validated['amount'];

            $exchangeRate = $this->getExchangeRate($fromCurrency, $toCurrency);

            if (!$exchangeRate) {
                return $this->error('Unable to get exchange rate.', 400);
            }

            $convertedAmount = $amount * $exchangeRate;

            return $this->success([
                'from_currency' => $fromCurrency,
                'to_currency' => $toCurrency,
                'amount' => $amount,
                'converted_amount' => $convertedAmount,
                'exchange_rate' => $exchangeRate,
            ]);
        } catch (\Exception $e) {
            Log::error("Currency Conversion Error: " . $e->getMessage());
            return $this->error('Error processing currency conversion.', 500);
        }
    }

    public function convert(Request $request)
    {
        $validated = $request->validate([
            'from' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'to' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'amount' => 'required|numeric|min:0.01',
            'date' => 'nullable|date_format:Y-m-d',
        ]);

        $conversion = ExchangeService::convertCurrency(
            $validated['from'],
            $validated['to'],
            $validated['amount'],
            $validated['date'] ?? null
        );

        return $this->success(['conversion' => $conversion]);
    }

    public function exchangeRate(Request $request)
    {
        $validated = $request->validate([
            'from' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'to' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
        ]);

        $rate = $this->getExchangeRate($validated['from'], $validated['to']);

        if ($rate) {
            return $this->success(['rate' => $rate]);
        }

        return $this->error('Unable to fetch exchange rate');
    }

    private function getExchangeRate($fromCurrency, $toCurrency)
    {
        $rate = ExchangeService::convertCurrency($fromCurrency, $toCurrency, 1);
        return $rate['rate'] ?? null;
    }
}
