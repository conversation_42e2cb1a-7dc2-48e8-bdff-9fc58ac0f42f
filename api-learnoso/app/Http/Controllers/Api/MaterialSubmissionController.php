<?php

namespace App\Http\Controllers\Api;

use App\Base\ApiBaseController;
use App\Http\Requests\MaterialSubmissionRequest;
use App\Models\MaterialSubmission;
use App\Repositories\Contracts\CourseMaterialRepositoryInterface;
use Exception;

class MaterialSubmissionController extends ApiBase<PERSON>ontroller
{

    protected CourseMaterialRepositoryInterface $courseMaterialRepository;


    public function __construct(CourseMaterialRepositoryInterface $courseMaterialRepository)
    {
        $this->courseMaterialRepository = $courseMaterialRepository;
    }
    

    public function getAllSubmissions(MaterialSubmissionRequest $request)
    {
        $data = $this->courseMaterialRepository->allSubmissions($request->evaluation_id);
        
        if($data->toArray() !== []){
            return $this->success($data, "All Submissions gotten");
        } else {
            return $this->notFoundError("No Submissions Found for this Evaluation");
        }
    }
    
    
    public function createSubmission(MaterialSubmissionRequest $request)
    {
        $result = $this->courseMaterialRepository->createSubmission($request);
        
        if($result["status"]){
            return $this->success(["id" => $result["id"]], "Created Submission Successfully");
        } else {
            return $this->error($result["message"]);
        }
    }
    
    
    public function getSubmission($id)
    {        
        $data = $this->courseMaterialRepository->getSubmission($id);

        if($data->toArray() !== []){
            return $this->success($data, "Submission gotten");
        } else {
            return $this->notFoundError("No Submission Found");
        }
    }
    
    
    public function upateSubmission(MaterialSubmissionRequest $request, $id)
    {
        $result = $this->courseMaterialRepository->updateSubmission($request, $id);

        if($result){
            return $this->success([], "Updated Successfully");
        } else {
            return $this->error("Unable to Update");
        }
    }

    
    public function deleteSubmission(int $id)
    {
        try{
            MaterialSubmission::destroy($id);
            return $this->success([], "Deleted Successfully");
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
