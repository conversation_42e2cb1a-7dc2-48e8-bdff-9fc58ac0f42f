<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletTransactionResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'amount' => $this->amount,
            'description' => $this->description,
            'status' => $this->confirmed ? 'confirmed' : 'pending',
            'type' => $this->type,
            'created_at' => $this->created_at,

        ];
    }
}
