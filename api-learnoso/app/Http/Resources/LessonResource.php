<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class LessonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        // Get the authenticated user's timezone from their profile, default to UTC
        $timezone = Auth::user()->tutor->timezone ??  Auth::user()->student->timezone ?? 'UTC';

        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'starts_at' => Carbon::parse($this->starts_at)->timezone($timezone)->toDateTimeString(),
            'ends_at' => Carbon::parse($this->ends_at)->timezone($timezone)->toDateTimeString(),
            'attended' => $this->attended,
            'meeting_link' => $this->meeting_link,
            'meeting_meta_data' => $this->meeting_meta_data,
            'agora_token' => $this->agora_token,
            'channel_name' => $this->channel_name,
            'status' => $this->status,
            'timezone' => $timezone,
            'tutor' => new UserResource($this->whenLoaded('tutor')),
            'student' => new UserResource($this->whenLoaded('student')),
            'course' => new CourseResource($this->whenLoaded('course')),

        ];
    }
}
