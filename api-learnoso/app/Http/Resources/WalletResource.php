<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class WalletResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'description' => $this->wallet->description,
            'balance' => $this->wallet->balance,
            'decimal_places' => $this->wallet->decimal_places,
            'holder_name' => $this->first_name . " " . $this->last_name,
            'email' => $this->email,
            'currency' => $this->tutor
                ? $this->tutor->currency
                : ($this->student
                    ? $this->student->currency
                    : 'USD'),
        ];
    }
}
