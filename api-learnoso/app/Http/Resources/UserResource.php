<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        $data = [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'country' => $this->country,
            'timezone' => optional($this->tutor)->timezone ?? optional($this->student)->timezone,
            'profile_picture' => optional($this->tutor)->profile_picture ?? optional($this->student)->profile_picture,
            'email_verified_at' => $this->email_verified_at,
            'is_admin' => $this->isAdmin(),
            'is_super_admin' => $this->isSuperAdmin(),
            'is_student' => $this->isStudent(),
            'is_tutor' => $this->isTutor(),
            'languages' => LanguageResource::collection($this->languages),
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
        ];

        if ($this->isTutor()) {
            $data['profile_status'] = optional($this->tutor)->profile_status;
            $data['onboarding_status'] = $this->onboardingStatus;
            $data['tutor_profile'] = $this->tutor;
            $data['courses'] = CourseResource::collection($this->courses);
        }

        return $data;
    }
}
