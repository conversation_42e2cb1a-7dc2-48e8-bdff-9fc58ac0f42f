<?php

namespace App\Http\Requests;

use App\Models\CourseMaterial;
use App\Models\Tutor;
use App\Models\User;
use Closure;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Http\Request;

class CourseMaterialRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'title' => 'required|string|max:255',
            'slug' => 'string|max:255',
            'description' => 'required|string',
            'course_id' => [
                'required',
                'exists:courses,id',
            ],
            'tutor_id' => [
                'required',
                'exists:tutors,id'
            ],
            'language' => 'required|string|max:50',
            'skill_level' => 'required|string|in:beginner,intermediate,advanced',
            'currency' => 'required|string|max:3',
            'price_per_hour' => 'required|numeric|min:0',
            'is_published' => 'boolean',
            'last_updated' => 'required|date'
        ];

        // If this is an update request, make some fields optional
        if ($this->method() === 'PUT' || $this->method() === 'PATCH') {
            $rules = array_map(function ($rule) {
                return str_replace('required|', '', $rule);
            }, $rules);
        }

        return $rules;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $data = [];

        // Generate slug from title if not provided
        if ($this->has('title') && !$this->has('slug')) {
            $data['slug'] = Str::slug($this->input('title')) . '-' . bin2hex(random_bytes(6));
        }

        // Always set last_updated when creating/updating
        $data['last_updated'] = now();

        // Merge the prepared data
        if (!empty($data)) {
            $this->merge($data);
        }
    }

    /**
     * Custom messages for validation errors
     */
    public function messages(): array
    {
        return [
            'title.required' => 'The title field is required',
            'title.max' => 'The title cannot exceed 255 characters',
            'description.required' => 'The description field is required',
            'course_id.required' => 'The course field is required',
            'course_id.exists' => 'The selected course does not exist',
            'tutor_id.required' => 'The tutor field is required',
            'tutor_id.exists' => 'The selected tutor does not exist',
            'language.required' => 'The language field is required',
            'skill_level.required' => 'The skill level field is required',
            'skill_level.in' => 'The skill level must be beginner, intermediate, or advanced',
            'currency.required' => 'The currency field is required',
            'currency.max' => 'The currency code cannot exceed 3 characters',
            'price_per_hour.required' => 'The price per hour field is required',
            'price_per_hour.numeric' => 'The price per hour must be a number',
            'price_per_hour.min' => 'The price per hour cannot be negative',
            'total_hours.required' => 'The total hours field is required',
            'total_hours.integer' => 'The total hours must be a whole number',
            'total_hours.min' => 'The total hours must be at least 1',
            'last_updated.required' => 'The last updated date is required',
            'last_updated.date' => 'The last updated must be a valid date'
        ];
    }
}

