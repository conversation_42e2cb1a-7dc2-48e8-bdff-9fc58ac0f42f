<?php

namespace App\Http\Requests;

use App\Enums\SupportCurrency;
use Illuminate\Foundation\Http\FormRequest;
class InitiatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Change this to true to allow validation
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'description' => 'required|string',
            'currency' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (!in_array($value, SupportCurrency::getValidCurrencies())) {
                        $fail($attribute . ' is not a valid currency.');
                    }
                }
            ],
            'total' => 'required|numeric',
            'payment_method' => 'required|in:stripe,paypal,payoneer,orange_momo,mtn_momo',
        ];
    }



    /**
     * Get custom error messages for validation failures.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User ID is required',
            'currency.required' => 'Currency is required',
            'currency.string' => 'Currency must be a string',
            'currency.in' => 'Currency must be one of: ' . implode(', ', SupportCurrency::getValidCurrencies()),
            'total.required' => 'Total is required',
            'total.numeric' => 'Total must be a number',
            'total.min' => 'Total must be at least 0',
            'payment_method.required' => 'Payment method is required',
            'payment_method.in' => 'Payment method must be one of: stripe, paypal, payoneer, orange_momo, mtn_momo',
        ];
    }
}
