<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreEducationRequest extends FormRequest
{
    public function authorize()
    {
        return true;  // Adjust based on your auth logic
    }

    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'subject' => 'required|string|max:255',
            'certificate' => 'required|string|max:255',
            'institution' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'description' => 'nullable|string',
            'attachment' => 'required|file|mimes:pdf,jpg,png|max:3056', // Adjust as needed for file validation
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => 'User ID is required',
            'user_id.exists' => 'The selected user does not exist',
            'subject.required' => 'Subject is required',
            'institution.required' => 'Institution is required',
            'start_date.required' => 'Start date is required',
            'end_date.after_or_equal' => 'End date must be after or equal to the start date',
            'attachement.mimes' => 'The attachment must be a file of type: pdf, jpg, png',
            'attachement.max' => 'The attachment size must not exceed 3MB',
        ];
    }
}
