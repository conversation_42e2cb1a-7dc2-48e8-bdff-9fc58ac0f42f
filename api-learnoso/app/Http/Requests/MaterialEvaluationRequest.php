<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\CourseMaterialRequest;
use Illuminate\Validation\Rule;

class MaterialEvaluationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'material_id' => 'required|exists:course_materials,id',
            'title' => "string",
            'type' => "in:question,assignment,both,none",
            'due_date' => 'date',
            "questions" => "array",
            "assignments" => "array",
            "assignees" => "array",
        ];
    }

    /**
     * Return messages on validation error
     */
    public function messages(): array
    {
        return [
            'material_id.required' => "The Course's material_id is required",
            'material_id.exists' => "The Course's material_id must exist in the database",
            'title.string' =>  "The :attribute is must be a string",
            'type.in' =>  "The :attribute is must either be question, assignment, both or none",
            'due_date.date' =>  "The :attribute must be of the format 2025-05-12 19:20:25",
            'questions.array' => 'The :attribute must be of json format',
            'assignments.array' => 'The :attribute must be of json format',
            'assignees.array' => 'The :attribute must be of json format',
        ];
    }
}
