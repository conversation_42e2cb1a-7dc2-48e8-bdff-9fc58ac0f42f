<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class InitiateWithdrawalRequest extends FormRequest
{
    public function rules()
    {
        return [
            'payment_method' => 'required|string|in:paypal,stripe,flutterwave',
            'recipient_email' => 'required_if:payment_method,paypal|email',
            'recipient' => 'required_if:payment_method,stripe|string',
            'amount' => 'required|numeric|min:1',
            'currency' => 'nullable|string|size:3',
        ];
    }

    public function authorize()
    {
        return true;
    }
}
