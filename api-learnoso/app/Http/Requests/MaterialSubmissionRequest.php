<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class MaterialSubmissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'evaluation_id' => 'required|integer',
            'student_id'  => 'required|integer',
            'type' => 'required|string|max:255',
            'content'=> 'required|string',
            'file_paths'=> 'array',
            'is_graded'=> 'boolean',
            'score'=> 'numeric',
            'feedback'=> 'string',
        ];


        // If this is an update request, make some fields optional
        if ($this->method() === 'PUT' || $this->method() === 'PATCH' || $this->method() === 'GET') {
            $rules = array_map(function ($rule) {
                return str_replace('required|', '', $rule);
            }, $rules);
        }

        return $rules;
    }

    /**
     * Custom messages for validation errors
     */
    public function messages(): array
    {
        return [
            'evaluation_id.required' => "The :attribute id is required",
            'evaluation_id.integer' => "The :attribute id must be an integer",
            'student_id.required' => 'The :attribute id is required',
            'student_id.integer' => 'The :attribute id must be an integer',
            'type.required' => 'The :attribute field is required',
            'type.string' => 'The :attribute field must be a string',
            'type.max' => 'The :attribute field must not be longer thatn 255',
            'content.required' => 'The :attribute field is required',
            'content.string' => 'The :attribute must be a string',
            'file_path.file' => 'The :attribute must be a file',  // check out
            'is_graded.required' => 'The :attribute is required',
            'is_graded.boolean' => 'The :attribute must be a boolean',
            'score.required' => 'The :attribute is required',
            'score.decimal' => 'The :attribute must be a decimal',
            'feedback.string' => 'The :attribute must be a string'
        ];
    }
}
