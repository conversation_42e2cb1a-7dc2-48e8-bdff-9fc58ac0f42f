<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Http\Requests\CourseMaterialRequest;
use Illuminate\Validation\Rule;

class MaterialReviewsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // $rules1 = [
        //     'material_id'=> 'required|integer|exists:course_materials,id',
        // ];
        $rules2 = [
            'id'=> 'integer',
            'material_id'=> 'integer|exists:course_materials,id',
            'student_id'=>'integer|exists:students,id',
            'content'=>'string',
            'votes'=> 'integer',
            'rating'=>'integer'
        ];

        // if($this->method() == 'GET' || $this->method() == 'PUT' || $this->method() == 'PATCH' ){
        //     str_replace('required|','', $rules2);
        // }

        return $rules2;
    }


    public function messages(): array{
        return [
            'material_id.required'=> 'The material_id is required',
            'material_id.exists'=> 'The material_id does not exist in the database',
            'material_id.integer'=> 'The material_id must be an integer',
            'student_id.required'=> 'The student_id is required',
            'student_id.exists'=> 'The student_id does not exist in the database',
            'student_id.integer'=> 'The student_id must be an integer',
            'content.required'=> 'The :attribute is required',
            'content.string'=> 'The :attribute must be a string',
            'rating.integer'=> 'The :attribute must be an integer'
        ];
    }
}
