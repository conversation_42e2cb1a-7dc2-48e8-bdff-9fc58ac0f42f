<?php

namespace App\Http\Requests;

use App\Enums\SupportCurrency;
use Illuminate\Foundation\Http\FormRequest;

class OnboardStudentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id',
            'budget' => 'nullable|numeric',
            'courses_of_preference' => 'nullable|array',
            'courses_of_preference.*' => 'exists:courses,id',
            'reasons_for_learning' => 'nullable|array',
            'availability_times' => 'nullable|array',
            'timezone' => 'nullable|timezone',
            'prefered_language' => 'nullable|string',
            'currency' => 'nullable'
        ];
    }

    public function authorize()
    {
        return true;
    }
}
