<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MaterialPrerequisiteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isUpdate = $this->isMethod('PUT') || $this->isMethod('PATCH');
        $isDelete = $this->isMethod('DELETE');
        
        // For DELETE requests, only validate the ID
        if ($isDelete) {
            return [
                'material_id' => 'required|exists:course_materials,id',
            ];
        }
        
        // For CREATE and UPDATE requests
        return [
            'material_id' => 'required|exists:course_materials,id',
            'concepts' => ($isUpdate ? 'sometimes|' : 'required|') . 'array|min:1',
            'concepts.*' => 'required|string|max:255',
            'tools' => ($isUpdate ? 'sometimes|' : 'required|') . 'array|min:1',
            'tools.*' => 'required|string|max:255',
            'resources' => ($isUpdate ? 'sometimes|' : 'required|') . 'array|min:1',
            'resources.*.title' => 'required|string|max:255',
            'resources.*.resource_link' => 'nullable|url|max:2048',
            'resources.*.image' => 'required|image|max:2048',
        ];
    }

    /**
     * Return messages on validation error
     */
    public function messages(): array
    {
        return [
            'material_id.required' => "The Course's material_id is required",
            'material_id.exists' => "The Course's material_id must exist in the database",
            
            'concepts.required' => 'The concepts field is required',
            'concepts.sometimes' => 'The concepts field is optional for updates',
            'concepts.array' => 'The concepts must be an array',
            'concepts.min' => 'At least one concept is required',
            'concepts.*.required' => 'Each concept is required',
            'concepts.*.string' => 'Each concept must be a string',
            'concepts.*.max' => 'Each concept cannot exceed 255 characters',
            
            'tools.required' => 'The tools field is required',
            'tools.sometimes' => 'The tools field is optional for updates',
            'tools.array' => 'The tools must be an array',
            'tools.min' => 'At least one tool is required',
            'tools.*.required' => 'Each tool is required',
            'tools.*.string' => 'Each tool must be a string',
            'tools.*.max' => 'Each tool cannot exceed 255 characters',
            
            'resources.required' => 'The resources field is required',
            'resources.sometimes' => 'The resources field is optional for updates',
            'resources.array' => 'The resources must be an array',
            'resources.min' => 'At least one resource is required',
            'resources.*.title.required' => 'Resource title is required',
            'resources.*.title.string' => 'Resource title must be a string',
            'resources.*.title.max' => 'Resource title cannot exceed 255 characters',
            'resources.*.resource_link.url' => 'Resource link must be a valid URL',
            'resources.*.resource_link.max' => 'Resource link cannot exceed 2048 characters',
            'resources.*.image.required' => 'Resource image is required',
            'resources.*.image.string' => 'Resource image must be a string',
            'resources.*.image.max' => 'Resource image path cannot exceed 2048 characters',
        ];
    }
}