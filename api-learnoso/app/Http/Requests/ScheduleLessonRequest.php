<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class ScheduleLessonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Assuming only authenticated users can schedule a lesson
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'tutor_id' => 'required|exists:users,id',
            'student_id' => 'required|exists:users,id',
            'course_id' => 'required|exists:courses,id',
            'description' => 'nullable|string',
            'starts_at' => 'required|date',
            'ends_at' => 'required|date|after:starts_at',
            'timezone' => 'required|timezone',
            'duration' => 'nullable|integer|min:15', // Duration in minutes
        ];
    }

    /**
     * Configure the validator instance with custom role checks.
     *
     * @param \Illuminate\Contracts\Validation\Validator $validator
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $tutor = User::find($this->tutor_id);
            $student = User::find($this->student_id);

            // Check tutor and student roles
            if (!$tutor || !$tutor->hasRole('tutor')) {
                $validator->errors()->add('tutor_id', 'The selected tutor is not valid or does not currently have the role "tutor".');
            }

            if (!$student || !$student->hasRole('student')) {
                $validator->errors()->add('student_id', 'The selected student is not valid or does not currently have the role "student".');
            }

            if ($this->tutor_id == $this->student_id) {
                $validator->errors()->add('tutor_id', 'You cannot book a lesson with yourself.');
            }
        });
    }
}
