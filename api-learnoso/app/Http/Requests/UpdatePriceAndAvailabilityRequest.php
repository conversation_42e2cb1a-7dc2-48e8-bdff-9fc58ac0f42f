<?php

namespace App\Http\Requests;

use App\Enums\SupportCurrency;
use Illuminate\Foundation\Http\FormRequest;

class UpdatePriceAndAvailabilityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;  // Change this based on your authorization logic
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'currency' => 'required|string|in:' . implode(',', SupportCurrency::getValidCurrencies()),
            'price' => 'required|numeric|min:0',  // Ensure price is a non-negative number
            'availability' => 'required|array',  // Validate availability as an array
        ];
    }

    /**
     * Get custom error messages for validation failures.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'User ID is required',
            'currency.required' => 'Currency is required',
            'currency.string' => 'Currency must be a string',
            'currency.in' => 'Currency must be one of: ' . implode(', ', SupportCurrency::getValidCurrencies()),
            'price.required' => 'Price is required',
            'price.numeric' => 'Price must be a number',
            'price.min' => 'Price must be at least 0',
            'availability.required' => 'Availability is required',
            'availability.array' => 'Availability must be an array',
        ];
    }
}
