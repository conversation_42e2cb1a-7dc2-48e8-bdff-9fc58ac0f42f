<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CommentsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "id"=> "integer",
            "review_id"=> "integer|exists:material_reviews,id",
            "student_id"=> "integer|exists:students,id",
            "content"=> "string",
            "votes"=> "integer",
            "parent_id"=> "nullable|integer",
        ];
    }

    public function messages(): array{
        return [
           'review_id.required' => "The review_id is required",
            'review_id.exists' => "The review_id must exist in the database",
            'review_id.integer' => "The review_id must be an integer",
            'student_id.required' => "The student_id is required",
            'student_id.exists' => "The student_id must exist in the database",
            'student_id.integer' => "The student_id must be an integer",
            'content.required' =>  "The content is required",
            'content.string' =>  "The :attribute is must be a string",
            "parent_id.required"=> "The parent_id is required",
            "parent_id.nullable"=> "The parent id can have a null value",
            "parent_id.integer"=> "The parent id should be an integer",
           
        ] ;
    }
}
