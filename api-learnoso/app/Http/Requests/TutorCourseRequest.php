<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TutorCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Optionally, implement authorization logic, or return true to allow all users.
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'course_ids' => 'required|array',
            'course_ids.*' => 'exists:courses,id',
        ];
    }

    /**
     * Custom messages for validation errors.
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'The tutor (user) is required.',
            'user_id.exists' => 'The tutor (user) must exist in the database.',
            'course_ids.required' => 'Please select at least one course.',
            'course_ids.array' => 'The selected courses must be an array.',
            'course_ids.*.exists' => 'The selected course is invalid.',
        ];
    }
}
