<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\LessonTrackerService;

class AutoCompleteExpiredLessons extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lessons:auto-complete-expired
                            {--hours=4 : Maximum hours before auto-completion}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically complete lesson sessions that have exceeded maximum duration';

    protected LessonTrackerService $trackerService;

    /**
     * Create a new command instance.
     */
    public function __construct(LessonTrackerService $trackerService)
    {
        parent::__construct();
        $this->trackerService = $trackerService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting auto-completion of expired lesson sessions...');

        try {
            // Set the max session hours if provided
            $maxHours = (int) $this->option('hours');
            config(['app.max_session_hours' => $maxHours]);

            $this->info("Checking for sessions longer than {$maxHours} hours...");

            // Call the service method to auto-complete expired sessions
            $this->trackerService->autoCompleteExpiredSessions();

            $this->info('Auto-completion process completed successfully.');

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to auto-complete expired sessions: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
