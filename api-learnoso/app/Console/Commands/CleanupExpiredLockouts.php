<?php

namespace App\Console\Commands;

use App\Services\AccountLockoutService;
use Illuminate\Console\Command;

class CleanupExpiredLockouts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:cleanup-lockouts {--dry-run : Show what would be cleaned without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired account lockouts and reset lockout data';

    protected $lockoutService;

    public function __construct(AccountLockoutService $lockoutService)
    {
        parent::__construct();
        $this->lockoutService = $lockoutService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting cleanup of expired account lockouts...');

        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            if (!$this->option('dry-run')) {
                $cleaned = $this->lockoutService->cleanupExpiredLockouts();

                if ($cleaned > 0) {
                    $this->info("Successfully cleaned up {$cleaned} expired lockouts");
                } else {
                    $this->info("No expired lockouts found to clean up");
                }
            } else {
                // For dry run, just show statistics
                $stats = $this->lockoutService->getLockoutStatistics();
                $this->table(
                    ['Metric', 'Count'],
                    [
                        ['Currently Locked Users', $stats['currently_locked']],
                        ['Users with Failed Attempts', $stats['users_with_failed_attempts']],
                        ['Recent Lockouts (24h)', $stats['recent_lockouts_24h']]
                    ]
                );
            }

            // Show current statistics
            $stats = $this->lockoutService->getLockoutStatistics();
            $this->newLine();
            $this->info('Current lockout statistics:');
            $this->line("Currently locked accounts: {$stats['currently_locked']}");
            $this->line("Accounts with failed attempts: {$stats['users_with_failed_attempts']}");
            $this->line("Recent lockouts (24h): {$stats['recent_lockouts_24h']}");

            $this->newLine();
            $this->info('Cleanup completed successfully');

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Failed to cleanup expired lockouts: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
