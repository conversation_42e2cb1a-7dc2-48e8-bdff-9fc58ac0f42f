<?php

namespace App\Console\Commands;

use App\Models\AuditLog;
use Illuminate\Console\Command;
use Carbon\Carbon;

class CleanupAuditLogs extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'audit:cleanup
                            {--days=90 : Number of days to retain audit logs}
                            {--keep-critical=365 : Days to retain critical risk logs}
                            {--keep-suspicious=180 : Days to retain suspicious logs}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old audit logs based on retention policies';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $daysToKeep = (int) $this->option('days');
        $keepCritical = (int) $this->option('keep-critical');
        $keepSuspicious = (int) $this->option('keep-suspicious');
        $dryRun = $this->option('dry-run');

        $this->info("Audit Log Cleanup Starting...");
        $this->info("Retention Policy:");
        $this->info("- Standard logs: {$daysToKeep} days");
        $this->info("- Critical logs: {$keepCritical} days");
        $this->info("- Suspicious logs: {$keepSuspicious} days");

        if ($dryRun) {
            $this->warn("DRY RUN MODE - No logs will be deleted");
        }

        $totalDeleted = 0;

        // Clean up standard logs
        $standardCutoff = Carbon::now()->subDays($daysToKeep);
        $standardQuery = AuditLog::where('created_at', '<', $standardCutoff)
            ->where('risk_level', '!=', AuditLog::RISK_CRITICAL)
            ->where('is_suspicious', false);

        $standardCount = $standardQuery->count();

        if ($standardCount > 0) {
            $this->info("Standard logs to delete: {$standardCount}");

            if (!$dryRun) {
                $deleted = $standardQuery->delete();
                $totalDeleted += $deleted;
                $this->info("Deleted {$deleted} standard audit logs");
            }
        } else {
            $this->info("No standard logs to delete");
        }

        // Clean up old critical logs
        $criticalCutoff = Carbon::now()->subDays($keepCritical);
        $criticalQuery = AuditLog::where('created_at', '<', $criticalCutoff)
            ->where('risk_level', AuditLog::RISK_CRITICAL)
            ->where('is_suspicious', false);

        $criticalCount = $criticalQuery->count();

        if ($criticalCount > 0) {
            $this->info("Critical logs to delete: {$criticalCount}");

            if (!$dryRun) {
                $deleted = $criticalQuery->delete();
                $totalDeleted += $deleted;
                $this->info("Deleted {$deleted} critical audit logs");
            }
        } else {
            $this->info("No critical logs to delete");
        }

        // Clean up old suspicious logs
        $suspiciousCutoff = Carbon::now()->subDays($keepSuspicious);
        $suspiciousQuery = AuditLog::where('created_at', '<', $suspiciousCutoff)
            ->where('is_suspicious', true);

        $suspiciousCount = $suspiciousQuery->count();

        if ($suspiciousCount > 0) {
            $this->info("Suspicious logs to delete: {$suspiciousCount}");

            if (!$dryRun) {
                $deleted = $suspiciousQuery->delete();
                $totalDeleted += $deleted;
                $this->info("Deleted {$deleted} suspicious audit logs");
            }
        } else {
            $this->info("No suspicious logs to delete");
        }

        // Summary
        $remainingCount = AuditLog::count();

        if ($dryRun) {
            $wouldDelete = $standardCount + $criticalCount + $suspiciousCount;
            $this->info("DRY RUN SUMMARY:");
            $this->info("- Would delete: {$wouldDelete} logs");
            $this->info("- Would remain: " . ($remainingCount - $wouldDelete) . " logs");
        } else {
            $this->info("CLEANUP SUMMARY:");
            $this->info("- Total deleted: {$totalDeleted} logs");
            $this->info("- Remaining: {$remainingCount} logs");
        }

        // Show storage statistics
        $this->showStorageStats();

        $this->info("Audit log cleanup completed successfully!");

        return Command::SUCCESS;
    }

    /**
     * Show storage and performance statistics
     */
    protected function showStorageStats(): void
    {
        $this->info("\nStorage Statistics:");

        // Count by risk level
        $stats = [
            'Low Risk' => AuditLog::where('risk_level', AuditLog::RISK_LOW)->count(),
            'Medium Risk' => AuditLog::where('risk_level', AuditLog::RISK_MEDIUM)->count(),
            'High Risk' => AuditLog::where('risk_level', AuditLog::RISK_HIGH)->count(),
            'Critical Risk' => AuditLog::where('risk_level', AuditLog::RISK_CRITICAL)->count(),
            'Suspicious' => AuditLog::where('is_suspicious', true)->count(),
            'Pending Review' => AuditLog::where('requires_review', true)->whereNull('reviewed_at')->count(),
        ];

        foreach ($stats as $label => $count) {
            $this->line("- {$label}: {$count}");
        }

        // Show recent activity
        $recentCount = AuditLog::where('created_at', '>=', Carbon::now()->subDay())->count();
        $this->line("- Last 24 hours: {$recentCount}");
    }
}
