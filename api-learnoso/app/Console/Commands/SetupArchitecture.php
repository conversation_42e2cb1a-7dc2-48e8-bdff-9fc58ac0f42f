<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SetupArchitecture extends Command
{
    protected $signature = 'setup:architecture';
    protected $description = 'Set up the folder structure for Controller, Service, Repository with interfaces and create a base API controller';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $directories = [
            'app/Http/Controllers',
            'app/Services',
            'app/Repositories',
            'app/Repositories/Contracts', // This is where your interfaces go
            'app/Repositories/Eloquent',  // Eloquent implementation of repositories
            'app/Base'
        ];

        foreach ($directories as $dir) {
            if (!File::isDirectory($dir)) {
                File::makeDirectory($dir, 0755, true);
                $this->info("Created directory: $dir");
            } else {
                $this->info("Directory already exists: $dir");
            }
        }

        $this->createApiBaseController();

        $this->info('Folder structure and base API controller have been set up successfully!');
    }

    protected function createApiBaseController()
    {
        $path = base_path('app/Base/ApiBaseController.php');

        if (!File::exists($path)) {
            $content = <<<PHP
<?php

namespace App\Base;

use App\Enums\SupportCurrency;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ApiBaseController extends Controller
{
    protected \$statusCode = JsonResponse::HTTP_OK;

    public function success(\$data = [], \$message = '')
    {
        return response()->json([
            'success' => true,
            'data' => \$data,
            'message' => \$message,
        ], \$this->statusCode);
    }

    public function error(\$message = '', \$code = JsonResponse::HTTP_BAD_REQUEST)
    {
        return response()->json([
            'success' => false,
            'message' => \$message,
        ], \$code);
    }

    public function validationError(array \$errors, \$code = JsonResponse::HTTP_UNPROCESSABLE_ENTITY)
    {
        return response()->json([
            'success' => false,
            'errors' => \$errors,
        ], \$code);
    }

    public function internalServerError(\Exception \$e, \$code = JsonResponse::HTTP_INTERNAL_SERVER_ERROR)
    {
        Log::error(\$e->getMessage());

        return response()->json([
            'success' => false,
            'message' => 'An internal server error occurred. Please try again later.',
        ], \$code);
    }

    public function notFoundError(\$message = 'Resource not found')
    {
        return \$this->error(\$message, JsonResponse::HTTP_NOT_FOUND);
    }

    public function unauthorizedError(\$message = 'Unauthorized')
    {
        return \$this->error(\$message, JsonResponse::HTTP_UNAUTHORIZED);
    }

    public function forbiddenError(\$message = 'Forbidden')
    {
        return \$this->error(\$message, JsonResponse::HTTP_FORBIDDEN);
    }

    public function setStatusCode(\$statusCode)
    {
        \$this->statusCode = \$statusCode;
        return \$this;
    }

    public function getStatusCode()
    {
        return \$this->statusCode;
    }

    public function currencyIsValidCurrency(\$currency)
    {
        return SupportCurrency::hasValue(\$currency);
    }
}
PHP;

            File::put($path, $content);
            $this->info("Created base API controller: $path");
        } else {
            $this->info("Base API controller already exists: $path");
        }
    }
}
