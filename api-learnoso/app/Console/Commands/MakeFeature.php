<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MakeFeature extends Command
{
    protected $signature = 'make:repository {name}';
    protected $description = 'Create a repository with its interface and service';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $name = Str::studly($this->argument('name'));
        $interfaceName = "{$name}RepositoryInterface";
        $repositoryName = "{$name}Repository";
        $serviceName = "{$name}Service";

        // Define paths
        $basePath = base_path();
        $repositoriesPath = $basePath . '/app/Repositories/Contracts';
        $implementationsPath = $basePath . '/app/Repositories/Eloquent';
        $servicesPath = $basePath . '/app/Services';

        // Create directories if they don't exist
        File::makeDirectory($repositoriesPath, 0755, true, true);
        File::makeDirectory($implementationsPath, 0755, true, true);
        File::makeDirectory($servicesPath, 0755, true, true);

        // Create the interface
        $interfacePath = "{$repositoriesPath}/{$interfaceName}.php";
        $this->createFile($interfacePath, $this->generateInterfaceContent($name));

        // Create the repository
        $repositoryPath = "{$implementationsPath}/{$repositoryName}.php";
        $this->createFile($repositoryPath, $this->generateRepositoryContent($name));

        // Create the service
        $servicePath = "{$servicesPath}/{$serviceName}.php";
        $this->createFile($servicePath, $this->generateServiceContent($name));

        $this->info("Repository, interface, and service for {$name} created successfully!");
    }

    private function createFile($path, $content)
    {
        if (!File::exists($path)) {
            File::put($path, $content);
        }
    }

    private function generateInterfaceContent($name)
    {
        return <<<PHP
<?php

namespace App\Repositories\Contracts;

interface {$name}RepositoryInterface
{
    // Define the methods for your repository here
}
PHP;
    }

    private function generateRepositoryContent($name)
    {
        return <<<PHP
        <?php

        namespace App\Repositories\Eloquent;

        use App\Repositories\Contracts\\{$name}RepositoryInterface;

        class {$name}Repository implements {$name}RepositoryInterface
        {
            // Implement the methods defined in the interface
        }
        PHP;
            }

            private function generateServiceContent($name)
            {
                return <<<PHP
        <?php

        namespace App\Services;

        use App\Repositories\Contracts\\{$name}RepositoryInterface;

        class {$name}Service
        {
            protected \${$name}Repository;

            public function __construct({$name}RepositoryInterface \${$name}Repository)
            {
                \$this->{$name}Repository = \${$name}Repository;
            }

            // Define service methods that use the repository
        }
        PHP;
            }
}
