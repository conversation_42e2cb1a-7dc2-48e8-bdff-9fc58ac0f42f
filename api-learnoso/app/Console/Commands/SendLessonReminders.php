<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Lesson;
use App\Services\RealTimeNotificationService;
use Carbon\Carbon;

class SendLessonReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lessons:send-reminders
                            {--minutes=15 : Minutes before lesson start to send reminder}
                            {--dry-run : Show what would be done without actually sending}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send real-time lesson reminders to students and tutors';

    protected RealTimeNotificationService $realTimeService;

    /**
     * Create a new command instance.
     */
    public function __construct(RealTimeNotificationService $realTimeService)
    {
        parent::__construct();
        $this->realTimeService = $realTimeService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $minutesUntilStart = (int) $this->option('minutes');
        $isDryRun = $this->option('dry-run');

        $this->info("Looking for lessons starting in {$minutesUntilStart} minutes...");

        // Calculate the time window for lessons that need reminders
        $reminderTime = Carbon::now()->addMinutes($minutesUntilStart);
        $windowStart = $reminderTime->copy()->subMinute(); // 1-minute window
        $windowEnd = $reminderTime->copy()->addMinute();

        // Find lessons that start within the reminder window
        $lessons = Lesson::with(['student', 'tutor'])
            ->whereBetween('starts_at', [$windowStart, $windowEnd])
            ->whereIn('status', ['scheduled', 'confirmed'])
            ->where('starts_at', '>', Carbon::now()) // Only future lessons
            ->get();

        if ($lessons->isEmpty()) {
            $this->info('No lessons found that need reminders at this time.');
            return Command::SUCCESS;
        }

        $this->info("Found {$lessons->count()} lesson(s) that need reminders:");

        $successCount = 0;
        $failureCount = 0;

        foreach ($lessons as $lesson) {
            $studentName = $lesson->student->first_name . ' ' . $lesson->student->last_name;
            $tutorName = $lesson->tutor->first_name . ' ' . $lesson->tutor->last_name;
            $lessonTime = $lesson->starts_at->format('Y-m-d H:i:s');

            $this->line("Lesson ID: {$lesson->id}");
            $this->line("  Student: {$studentName}");
            $this->line("  Tutor: {$tutorName}");
            $this->line("  Start Time: {$lessonTime}");

            if ($isDryRun) {
                $this->line("  [DRY RUN] Would send reminder notification");
                $successCount++;
            } else {
                try {
                    $success = $this->realTimeService->sendLessonReminder($lesson, $minutesUntilStart);

                    if ($success) {
                        $this->line("  ✓ Reminder sent successfully");
                        $successCount++;
                    } else {
                        $this->line("  ✗ Failed to send reminder");
                        $failureCount++;
                    }
                } catch (\Exception $e) {
                    $this->error("  ✗ Error sending reminder: " . $e->getMessage());
                    $failureCount++;
                }
            }

            $this->line(''); // Empty line for readability
        }

        // Summary
        if ($isDryRun) {
            $this->info("DRY RUN COMPLETE: Would have processed {$lessons->count()} lesson(s)");
        } else {
            $this->info("REMINDER SENDING COMPLETE:");
            $this->info("  Successful: {$successCount}");
            if ($failureCount > 0) {
                $this->warn("  Failed: {$failureCount}");
            }
        }

        return $successCount > 0 ? Command::SUCCESS : Command::FAILURE;
    }
}
