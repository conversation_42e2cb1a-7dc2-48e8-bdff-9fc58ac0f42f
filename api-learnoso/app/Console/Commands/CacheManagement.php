<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\CacheService;

class CacheManagement extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:manage
                            {action : The action to perform (warm-up, clear, stats, clear-pattern)}
                            {--pattern= : Pattern for clearing specific cache keys}
                            {--force : Force action without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage application cache (warm-up, clear, statistics)';

    protected CacheService $cacheService;

    /**
     * Create a new command instance.
     */
    public function __construct(CacheService $cacheService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'warm-up':
                return $this->warmUpCache();

            case 'clear':
                return $this->clearCache();

            case 'stats':
                return $this->showStats();

            case 'clear-pattern':
                return $this->clearByPattern();

            default:
                $this->error('Invalid action. Available actions: warm-up, clear, stats, clear-pattern');
                return Command::FAILURE;
        }
    }

    /**
     * Warm up the cache
     */
    private function warmUpCache(): int
    {
        $this->info('Starting cache warm-up...');

        try {
            $warmedUp = $this->cacheService->warmUpCache();

            $this->info('Cache warm-up completed successfully!');
            $this->table(['Warmed Up Components'], array_map(fn($item) => [$item], $warmedUp));

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Cache warm-up failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Clear cache
     */
    private function clearCache(): int
    {
        if (!$this->option('force') && !$this->confirm('Are you sure you want to clear all cache?')) {
            $this->info('Cache clear cancelled.');
            return Command::SUCCESS;
        }

        $this->info('Clearing all cache...');

        try {
            $result = $this->cacheService->clearAllCache();

            if ($result) {
                $this->info('All cache cleared successfully!');
                return Command::SUCCESS;
            } else {
                $this->error('Failed to clear cache.');
                return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error('Cache clear failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Show cache statistics
     */
    private function showStats(): int
    {
        $this->info('Retrieving cache statistics...');

        try {
            $stats = $this->cacheService->getCacheStats();

            if (isset($stats['error'])) {
                $this->error($stats['error']);
                return Command::FAILURE;
            }

            $this->info('Redis Cache Statistics:');
            $this->newLine();

            $tableData = [
                ['Redis Version', $stats['redis_version']],
                ['Used Memory', $stats['used_memory']],
                ['Connected Clients', $stats['connected_clients']],
                ['Total Commands Processed', number_format($stats['total_commands_processed'])],
                ['Keyspace Hits', number_format($stats['keyspace_hits'])],
                ['Keyspace Misses', number_format($stats['keyspace_misses'])],
                ['Hit Rate', $stats['hit_rate']],
                ['Uptime', $this->formatUptime($stats['uptime_in_seconds'])]
            ];

            $this->table(['Metric', 'Value'], $tableData);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to retrieve cache stats: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Clear cache by pattern
     */
    private function clearByPattern(): int
    {
        $pattern = $this->option('pattern');

        if (!$pattern) {
            $this->error('Pattern is required. Use --pattern option.');
            return Command::FAILURE;
        }

        if (!$this->option('force') && !$this->confirm("Are you sure you want to clear cache matching pattern: {$pattern}?")) {
            $this->info('Cache clear cancelled.');
            return Command::SUCCESS;
        }

        $this->info("Clearing cache with pattern: {$pattern}");

        try {
            $deleted = $this->cacheService->invalidateByPattern($pattern);

            $this->info("Cache cleared successfully! Deleted {$deleted} keys.");
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Cache clear by pattern failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Format uptime in seconds to human readable format
     */
    private function formatUptime(int $seconds): string
    {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);

        $parts = [];

        if ($days > 0) {
            $parts[] = "{$days} day" . ($days > 1 ? 's' : '');
        }

        if ($hours > 0) {
            $parts[] = "{$hours} hour" . ($hours > 1 ? 's' : '');
        }

        if ($minutes > 0) {
            $parts[] = "{$minutes} minute" . ($minutes > 1 ? 's' : '');
        }

        if (empty($parts)) {
            return 'Less than a minute';
        }

        return implode(', ', $parts);
    }
}
