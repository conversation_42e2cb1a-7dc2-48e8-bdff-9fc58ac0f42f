<?php

namespace App\Base;

use App\Enums\SupportCurrency;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class ApiBaseController extends Controller
{
    protected $statusCode = JsonResponse::HTTP_OK;

    public function success($data = [], $message = '', $code = JsonResponse::HTTP_OK)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
        ], $code);
    }

    public function error($message = '', $code = JsonResponse::HTTP_BAD_REQUEST)
    {
        return response()->json([
            'success' => false,
            'message' => $message,
        ], $code);
    }

    public function validationError(array $errors, $code = JsonResponse::HTTP_UNPROCESSABLE_ENTITY)
    {
        return response()->json([
            'success' => false,
            'errors' => $errors,
        ], $code);
    }

    public function internalServerError(\Exception $e, $code = JsonResponse::HTTP_INTERNAL_SERVER_ERROR)
    {
        Log::error($e->getMessage());

        return response()->json([
            'success' => false,
            'message' => 'An internal server error occurred. Please try again later.',
        ], $code);
    }

    public function notFoundError($message = 'Resource not found')
    {
        return $this->error($message, JsonResponse::HTTP_NOT_FOUND);
    }

    public function unauthorizedError($message = 'Unauthorized')
    {
        return $this->error($message, JsonResponse::HTTP_UNAUTHORIZED);
    }

    public function forbiddenError($message = 'Forbidden')
    {
        return $this->error($message, JsonResponse::HTTP_FORBIDDEN);
    }

    public function setStatusCode($statusCode)
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    public function getStatusCode()
    {
        return $this->statusCode;
    }

    public function currencyIsValidCurrency($currency)
    {
        return SupportCurrency::hasValue($currency);
    }

    protected function validateOrderRequest(array $data)
    {
        $validator = validator($data, [
            'description' => 'required|string',
            'currency' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (!in_array($value, SupportCurrency::getValidCurrencies())) {
                        $fail($attribute . ' is not a valid currency.');
                    }
                }
            ],
            'total' => 'required|numeric',
            'payment_method' => 'required|in:stripe,paypal,payoneer,orange_momo,mtn_momo',
        ]);

        if ($validator->fails()) {
            // Return validation errors directly instead of throwing an exception
            return $this->validationError($validator->errors()->toArray());
        }
    }
}
