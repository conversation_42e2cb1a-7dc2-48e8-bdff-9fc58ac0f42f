<?php

use App\Models\User;
use App\Models\SystemSetting;
use Illuminate\Support\Facades\DB;

if (!function_exists('getSystemUser')) {
    /**
     * Get the system user.
     *
     * @return \App\Models\User|null
     */
    function getSystemUser()
    {
        return User::where('email', '<EMAIL>')->first();
    }
}

if (!function_exists('getSystemTimeZone')) {
    /**
     * Get the system timezone.
     *
     * @return string
     */
    function getSystemTimeZone()
    {
        return date_default_timezone_get();
    }
}

if (!function_exists('getSystemSetting')) {
    /**
     * Get a specific system setting.
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function getSystemSetting(string $key, $default = null)
    {
        $setting = SystemSetting::first();
        if (!$setting) {
            return $default;
        }
        return $setting->settings[$key] ?? $default;
    }
}
