<?php

namespace App\Helpers;

class CommentHelper
{
    public static function buildCommentTree($comments, $parentId = null)
    {
        $branch = [];

        foreach ($comments as $comment) {
            if ($comment->parent_id === $parentId) {
                $children = self::buildCommentTree($comments, $comment->id);
                if ($children) {
                    $comment->replies = $children;
                }
                $branch[] = $comment;
            }
        }

        return $branch;
    }
}
