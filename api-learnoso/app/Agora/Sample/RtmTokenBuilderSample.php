<?php

namespace App\Agora\Sample;

use App\Agora\Src\RtmTokenBuilder;
use DateTime;
use DateTimeZone;


class RtmTokenBuilderSample {

    public function __construct(){

        $appId = config('agora.app_id');
        $appCertificate = config('agora.app_certificate');


        $user = 
        $role = RtmTokenBuilder::RoleRtmUser;
        $expireTimeInSeconds = 3600;
        $currentTimestamp = (new DateTime("now", new DateTimeZone('UTC')))->getTimestamp();
        $privilegeExpiredTs = $currentTimestamp + $expireTimeInSeconds;

        echo "App Id: " . $appId . PHP_EOL;
        echo "App Certificate: " . $appCertificate . PHP_EOL;
        if ($appId == "" || $appCertificate == "") {
            echo "Need to set environment variable AGORA_APP_ID and AGORA_APP_CERTIFICATE" . PHP_EOL;
            exit;
        }

        $token = RtmTokenBuilder::buildToken($appId, $appCertificate, $user, $role, $privilegeExpiredTs);
        echo 'Rtm Token: ' . $token . PHP_EOL;


    }

}
// Need to set environment variable AGORA_APP_ID
