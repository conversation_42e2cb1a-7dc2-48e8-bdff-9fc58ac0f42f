<?php
namespace App\Agora\Sample;
use App\Agora\Src\RtcTokenBuilder2;



class RtcTokenBuilder2Sample {
    public $channel;
    public $userId;

    public $duration;
    public function __construct($channel, $userId, $duration) {
        $this->channel = $channel;
        $this->userId = $userId;
        $this->duration = $duration;

    }

    public function main() {

        $appId = config('agora.app_id');
        $appCertificate = config('agora.app_certificate');

        $channelName = $this->channel;
        $uid = $this->userId;
        $tokenExpirationInSeconds = $this->duration * 60;
        $privilegeExpirationInSeconds = $this->duration * 60;
        
        $token = RtcTokenBuilder2::buildTokenWithUid($appId, $appCertificate, $channelName, $uid, RtcTokenBuilder2::ROLE_PUBLISHER, $tokenExpirationInSeconds, $privilegeExpirationInSeconds);

        return $token;
    }
}
