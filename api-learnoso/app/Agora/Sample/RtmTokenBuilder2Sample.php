<?php
namespace App\Agora\Sample;

use App\Agora\Src\RtmTokenBuilder2;

class RtmTokenBuilder2Sample {

    public static function main() {

        $appId = config('agora.app_id');
        $appCertificate = config('agora.app_certificate');

        $user = "2882341273";
        $expireTimeInSeconds = 3600;


        $token = RtmTokenBuilder2::buildToken($appId, $appCertificate, $user, $expireTimeInSeconds);

        return $token;
    }
}
