<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaterialCurriculum extends Model
{
    protected $table = 'material_curriculums';
    protected $fillable = ['material_id', 'level', 'modules', 'order'];

    protected $casts = [
        'modules' => 'array'
    ];

    public function material(): BelongsTo
    {
        return $this->belongsTo(CourseMaterial::class);
    }
}

