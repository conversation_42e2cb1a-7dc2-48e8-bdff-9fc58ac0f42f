<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SystemSetting extends Model
{
    protected $fillable = ['settings'];

    protected $casts = [
        'settings' => 'array'
    ];

    public static function getValue(string $key, $default = null)
    {
        $setting = self::first();
        if (!$setting) {
            return $default;
        }
        return $setting->settings[$key] ?? $default;
    }

    public static function setValue(string $key, $value)
    {
        $setting = self::first();
        if (!$setting) {
            $setting = new self();
            $setting->settings = [];
        }

        $settings = $setting->settings;
        $settings[$key] = $value;
        $setting->settings = $settings;
        $setting->save();

        return $setting;
    }

    public static function getCommissionRate(): float
    {
        return (float) self::getValue('lesson_commission_rate', 0.10);
    }

    public static function getMinimumCommission(): float
    {
        return (float) self::getValue('minimum_commission_amount', 1.00);
    }

    public static function getMaximumCommission(): float
    {
        return (float) self::getValue('maximum_commission_amount', 50.00);
    }

    public static function getWithdrawalFee(): float
    {
        return (float) self::getValue('withdrawal_fee', 0.02);
    }

    public static function getMinimumWithdrawalAmount(): float
    {
        return (float) self::getValue('minimum_withdrawal_amount', 10.00);
    }

    public static function getNotificationSettings(): array
    {
        return self::getValue('notification_settings', [
            'email' => true,
            'push' => true,
            'sms' => false
        ]);
    }

    public static function getSystemCurrency(): string
    {
        return self::getValue('system_currency', 'USD');
    }

    public static function getTimezone(): string
    {
        return self::getValue('timezone', 'UTC');
    }

    public static function getDefaultLocale(): string
    {
        return self::getValue('default_locale', 'en');
    }

    public static function updateSettings(array $settings)
    {
        $setting = self::first();
        if (!$setting) {
            $setting = new self();
            $setting->settings = [];
        }

        $currentSettings = $setting->settings;
        $setting->settings = array_merge($currentSettings, $settings);
        $setting->save();

        return $setting;
    }
}
