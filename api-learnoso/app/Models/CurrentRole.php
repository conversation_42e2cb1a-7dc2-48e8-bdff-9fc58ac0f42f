<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class CurrentRole extends Model
{
    protected $table = 'current_role';

    protected $fillable = [
        'user_id',
        'role_id',
        'lastly_switched_on'
    ];

    protected $casts = [
        'lastly_switched_on' => 'datetime',
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function role()
    {
        return $this->belongsTo(Role::class, 'role_id');
    }
}
