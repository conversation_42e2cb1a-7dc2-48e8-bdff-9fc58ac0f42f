<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseMaterial extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'course_id',
        'tutor_id',
        'title',
        'slug',
        'description',
        'language',
        'skill_level',
        'last_updated',
        'currency',
        'price_per_hour',
        'is_published',
    ];

    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function tutor(): BelongsTo
    {
        return $this->belongsTo(Tutor::class, 'tutor_id');
    }

    public function curriculums(): HasMany
    {
        return $this->hasMany(MaterialCurriculum::class, 'material_id');
    }

    public function prerequisite(): HasOne
    {
        return $this->hasOne(MaterialPrerequisite::class, 'material_id');
    }

    // check this and convert to one to many relations
    public function evaluation(): HasMany
    {
        return $this->hasMany(MaterialEvaluation::class, 'material_id');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(MaterialReview::class, 'material_id');
    }
}
