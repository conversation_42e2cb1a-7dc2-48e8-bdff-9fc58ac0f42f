<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaterialPrerequisite extends Model
{
    protected $fillable = [
        'material_id',
        'concepts',
        'tools',
        'resources',
    ];

    protected $casts = [
        'concepts' => 'array',
        'tools' => 'array', // {name, }
        'resources' => 'array', // {title,link, url}
    ];

    public function material(): BelongsTo
    {
        return $this->belongsTo(CourseMaterial::class, 'material_id');
    }
}
