<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TutorCourseTopic extends Model
{
    use HasFactory;

    protected $table = "tutor_course_topic";

    protected $fillable = [
        'tutor_id',
        'course_id',
        'topic_id',
        'description',
    ];

    public function tutor()
    {
        return $this->belongsTo(User::class);
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }


}
