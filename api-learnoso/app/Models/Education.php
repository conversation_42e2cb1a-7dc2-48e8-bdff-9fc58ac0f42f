<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Education extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'educations';

    protected $fillable = [
        'user_id',
        'subject',
        'certificate',
        'institution',
        'start_date',
        'end_date',
        'description',
        'url',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

}
