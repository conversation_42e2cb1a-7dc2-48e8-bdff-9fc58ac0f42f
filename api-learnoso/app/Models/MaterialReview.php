<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MaterialReview extends Model
{
    protected $fillable = ['material_id', 'student_id', 'content', 'rating', 'votes'];
    

    public function material(): BelongsTo
    {
        return $this->belongsTo(CourseMaterial::class, 'material_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function comments(){
        return $this->hasMany(Comments::class, 'review_id');
    }
}