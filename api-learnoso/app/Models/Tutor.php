<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tutor extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'profile_picture',
        'bio',
        'phone_number',
        'city',
        'timezone',
        'native_language',
        'rating',
        'price',
        'availability',
        'currency',
        'is_active',
        'short_description',
        'motivation_to_students',
        'video_url',
        'profile_status',
        'profile_rejection_reason'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function students()
    {
        return $this->belongsToMany(Student::class, 'student_tutor', 'tutor_id', 'student_id');
    }

    public function getIsApprovedAttribute()
    {
        return $this->profile_is_approved;
    }

}
