<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Experience extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'company',
        'start_date',
        'end_date',
        'description',
    ];

    /**
     * Get the user that owns this experience record.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope the query to only include experience records for the given user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get the latest experience record for the user.
     *
     * @param int $userId
     * @return Experience|null
     */
    public static function getLatestForUser($userId)
    {
        return self::forUser($userId)->latest()->first();
    }

    /**
     * Get the experience records for the user in reverse chronological order.
     *
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getForUserInReverseChronologicalOrder($userId)
    {
        return self::forUser($userId)->orderBy('end_date', 'desc')->get();
    }

    /**
     * Get the total years of experience for the user.
     *
     * @param int $userId
     * @return float
     */
    public static function getTotalYearsOfExperienceForUser($userId)
    {
        $experienceRecords = self::getForUserInReverseChronologicalOrder($userId);

        $totalYearsOfExperience = 0;
        foreach ($experienceRecords as $experienceRecord) {
            $totalYearsOfExperience += $experienceRecord->getYearsOfExperience();
        }

        return $totalYearsOfExperience;
    }
}
