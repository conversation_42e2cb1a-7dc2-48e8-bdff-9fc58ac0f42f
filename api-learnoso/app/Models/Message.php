<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Message extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'conversation_id',
        'sender_id',
        'content',
        'type',
        'file_path',
        'file_name',
        'file_type',
        'file_size',
        'metadata',
        'status',
        'read_at',
        'delivered_at',
        'reply_to_message_id'
    ];

    protected $casts = [
        'metadata' => 'array',
        'read_at' => 'datetime',
        'delivered_at' => 'datetime',
        'file_size' => 'integer'
    ];

    /**
     * Relationships
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function replyToMessage(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'reply_to_message_id');
    }

    public function replies(): HasMany
    {
        return $this->hasMany(Message::class, 'reply_to_message_id');
    }

    /**
     * Business Logic Methods
     */

    /**
     * Mark message as read
     */
    public function markAsRead(): void
    {
        if ($this->status !== 'read') {
            $this->update([
                'status' => 'read',
                'read_at' => Carbon::now()
            ]);
        }
    }

    /**
     * Mark message as delivered
     */
    public function markAsDelivered(): void
    {
        if ($this->status === 'sent') {
            $this->update([
                'status' => 'delivered',
                'delivered_at' => Carbon::now()
            ]);
        }
    }

    /**
     * Check if message has attachment
     */
    public function hasAttachment(): bool
    {
        return !empty($this->file_path);
    }

    /**
     * Get file URL for attachments
     */
    public function getFileUrl(): ?string
    {
        if (!$this->hasAttachment()) {
            return null;
        }

        // Return storage URL for the file
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get human-readable file size
     */
    public function getFormattedFileSize(): ?string
    {
        if (!$this->file_size) {
            return null;
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if message is a reply
     */
    public function isReply(): bool
    {
        return !is_null($this->reply_to_message_id);
    }

    /**
     * Get message preview (for list display)
     */
    public function getPreview(int $maxLength = 100): string
    {
        if ($this->type === 'text') {
            return strlen($this->content) > $maxLength
                ? substr($this->content, 0, $maxLength) . '...'
                : $this->content;
        }

        switch ($this->type) {
            case 'image':
                return '📷 Image';
            case 'file':
                return '📎 ' . ($this->file_name ?: 'File');
            case 'lesson_link':
                return '🔗 Lesson link';
            case 'system':
                return '🔔 ' . $this->content;
            default:
                return $this->content;
        }
    }

    /**
     * Create a system message
     */
    public static function createSystemMessage(int $conversationId, string $content, array $metadata = []): self
    {
        return self::create([
            'conversation_id' => $conversationId,
            'sender_id' => null, // System messages don't have a sender
            'content' => $content,
            'type' => 'system',
            'metadata' => $metadata,
            'status' => 'delivered'
        ]);
    }

    /**
     * Scopes
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeUnread($query)
    {
        return $query->where('status', '!=', 'read');
    }

    public function scopeFromSender($query, int $senderId)
    {
        return $query->where('sender_id', $senderId);
    }

    public function scopeInConversation($query, int $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    public function scopeReplies($query)
    {
        return $query->whereNotNull('reply_to_message_id');
    }

    public function scopeTopLevel($query)
    {
        return $query->whereNull('reply_to_message_id');
    }
}
