<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class LessonTracker extends Model
{
    use HasFactory;

    protected $fillable = [
        'lesson_id',
        'student_id',
        'tutor_id',
        'session_started_at',
        'session_ended_at',
        'total_duration_seconds',
        'active_duration_seconds',
        'break_duration_seconds',
        'status',
        'is_active',
        'break_count',
        'last_break_started_at',
        'last_break_ended_at',
        'agora_channel',
        'session_metadata',
        'is_billable',
        'billable_amount',
        'minimum_session_seconds'
    ];

    protected $casts = [
        'session_started_at' => 'datetime',
        'session_ended_at' => 'datetime',
        'last_break_started_at' => 'datetime',
        'last_break_ended_at' => 'datetime',
        'session_metadata' => 'array',
        'is_active' => 'boolean',
        'is_billable' => 'boolean',
        'billable_amount' => 'decimal:2'
    ];

    // Relationships
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    // Business Logic Methods

    /**
     * Start the lesson session
     */
    public function startSession(): self
    {
        $this->update([
            'session_started_at' => now(),
            'status' => 'in_progress',
            'is_active' => true
        ]);

        return $this;
    }

    /**
     * End the lesson session
     */
    public function endSession(): self
    {
        if ($this->is_active && $this->session_started_at) {
            // If there's an active break, end it first
            if ($this->last_break_started_at && !$this->last_break_ended_at) {
                $this->endBreak();
            }

            $endTime = now();
            $totalSeconds = $this->session_started_at->diffInSeconds($endTime);

            $this->update([
                'session_ended_at' => $endTime,
                'total_duration_seconds' => $totalSeconds,
                'active_duration_seconds' => $totalSeconds - $this->break_duration_seconds,
                'status' => 'completed',
                'is_active' => false
            ]);

            // Calculate billable amount
            $this->calculateBillableAmount();
        }

        return $this;
    }

    /**
     * Start a break
     */
    public function startBreak(): self
    {
        if ($this->is_active && !$this->last_break_started_at) {
            $this->update([
                'last_break_started_at' => now(),
                'status' => 'paused'
            ]);
        }

        return $this;
    }

    /**
     * End a break
     */
    public function endBreak(): self
    {
        if ($this->last_break_started_at && !$this->last_break_ended_at) {
            $breakEnd = now();
            $breakDuration = $this->last_break_started_at->diffInSeconds($breakEnd);

            $this->update([
                'last_break_ended_at' => $breakEnd,
                'break_duration_seconds' => $this->break_duration_seconds + $breakDuration,
                'break_count' => $this->break_count + 1,
                'status' => 'in_progress'
            ]);

            // Reset break timestamps for next break
            $this->update([
                'last_break_started_at' => null,
                'last_break_ended_at' => null
            ]);
        }

        return $this;
    }

    /**
     * Calculate billable amount based on active duration
     */
    public function calculateBillableAmount(): self
    {
        if (!$this->is_billable || !$this->lesson) {
            return $this;
        }

        $tutorPrice = $this->lesson->tutor->tutor->price ?? 0;
        $activeHours = $this->active_duration_seconds / 3600;

        // Only bill if minimum session time is met
        if ($this->active_duration_seconds >= $this->minimum_session_seconds) {
            $this->update([
                'billable_amount' => $tutorPrice * $activeHours
            ]);
        } else {
            $this->update(['billable_amount' => 0]);
        }

        return $this;
    }

    // Helper Methods

    /**
     * Get formatted total duration
     */
    public function getFormattedTotalDuration(): string
    {
        return $this->formatDuration($this->total_duration_seconds);
    }

    /**
     * Get formatted active duration
     */
    public function getFormattedActiveDuration(): string
    {
        return $this->formatDuration($this->active_duration_seconds);
    }

    /**
     * Get formatted break duration
     */
    public function getFormattedBreakDuration(): string
    {
        return $this->formatDuration($this->break_duration_seconds);
    }

    /**
     * Format duration in seconds to HH:MM:SS
     */
    private function formatDuration(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
    }

    /**
     * Check if session is currently on break
     */
    public function isOnBreak(): bool
    {
        return $this->last_break_started_at && !$this->last_break_ended_at;
    }

    /**
     * Get current session duration in seconds
     */
    public function getCurrentSessionDuration(): int
    {
        if (!$this->session_started_at) {
            return 0;
        }

        $endTime = $this->session_ended_at ?? now();
        return $this->session_started_at->diffInSeconds($endTime);
    }

    /**
     * Scope for active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for completed sessions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }
}
