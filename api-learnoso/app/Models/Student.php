<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'budget',
        'courses_of_preference',
        'reasons_for_learning',
        'availability_times',
        'timezone',
        'prefered_language',
        'currency',
        'profile_picture',

    ];


    protected $casts = [
        'availability_times' => 'array',
        'courses_of_preference' => 'array', // If this is also JSON
        'reasons_for_learning' => 'array'
    ];

    // Link the Student to the User model
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Many-to-many relationship with languages
    public function languages()
    {
        return $this->belongsToMany(Language::class)->withPivot('level');
    }

    // Student can enroll in many courses
    public function courses()
    {
        return $this->belongsToMany(CourseUser::class, 'course_user', 'user_id', 'course_id');
    }


    public function tutors()
    {
        return $this->belongsToMany(Tutor::class, 'student_tutor', 'student_id', 'tutor_id');
    }

}
