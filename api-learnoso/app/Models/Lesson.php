<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Lesson extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'course_id',
        'student_id',
        'tutor_id',
        'starts_at',
        'ends_at',
        'duration',
        'status',
        'attended',
        'confirmed',
        'confirmed_at',
        'cancellation_reason',
        'metadata'
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'attended' => 'boolean',
        'confirmed' => 'boolean',
        'metadata' => 'array'
    ];

    // Define relationships
    public function tutor()
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function tracker()
    {
        return $this->hasOne(LessonTracker::class);
    }

    public function getStartsAtAttribute($value)
    {
        $timezone = $this->tutor->timezone ?? $this->student->timezone ?? 'UTC';
        return Carbon::parse($value)->setTimezone($timezone);
    }

    // Accessor for ends_at
    public function getEndsAtAttribute($value)
    {
        $timezone = $this->tutor->timezone ?? $this->student->timezone ?? 'UTC';
        return Carbon::parse($value)->setTimezone($timezone);
    }

    public function updateMetadata(array $metadata)
    {
        $currentMetadata = $this->metadata ?? [];
        $this->metadata = array_merge($currentMetadata, $metadata);
        $this->save();
        return $this;
    }

    public function getMetadata()
    {
        return $this->metadata ?? [
            'meeting' => [
                'title' => null,
                'endTime' => $this->ends_at?->format('d/m/Y, H:i:s'),
                'status' => 'Not Started'
            ],
            'host' => [
                'id' => $this->tutor?->id,
                'name' => $this->tutor?->user?->name,
                'email' => $this->tutor?->user?->email
            ],
            'duration' => [
                'total' => '00:00:00',
                'active' => '00:00:00',
                'paused' => '00:00:00'
            ],
            'participants' => [
                'total' => 2,
                'list' => [
                    [
                        'id' => $this->tutor?->id,
                        'name' => $this->tutor?->user?->name,
                        'role' => 'Host'
                    ],
                    [
                        'id' => $this->student?->id,
                        'name' => $this->student?->user?->name,
                        'role' => 'Participant'
                    ]
                ]
            ],
            'activity' => [
                'pauses' => [
                    'count' => 0,
                    'totalDuration' => '00:00:00'
                ],
                'screenShare' => 'No',
                'videoMuted' => 'No',
                'audioMuted' => 'No'
            ]
        ];
    }
}
