<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Conversation extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'tutor_id',
        'lesson_id',
        'title',
        'type',
        'status',
        'last_message_at',
        'last_message_by',
        'student_last_read_at',
        'tutor_last_read_at',
        'total_messages',
        'student_unread_count',
        'tutor_unread_count'
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
        'student_last_read_at' => 'datetime',
        'tutor_last_read_at' => 'datetime',
        'total_messages' => 'integer',
        'student_unread_count' => 'integer',
        'tutor_unread_count' => 'integer'
    ];

    /**
     * Relationships
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function tutor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tutor_id');
    }

    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    public function lastMessageBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_message_by');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class)->orderBy('created_at', 'asc');
    }

    public function latestMessage(): BelongsTo
    {
        return $this->belongsTo(Message::class, 'last_message_by');
    }

    /**
     * Get recent messages (limited)
     */
    public function recentMessages(): HasMany
    {
        return $this->hasMany(Message::class)
            ->orderBy('created_at', 'desc')
            ->limit(50);
    }

    /**
     * Business Logic Methods
     */

    /**
     * Find or create a conversation between two users
     */
    public static function findOrCreateBetween(int $studentId, int $tutorId, string $type = 'general', int $lessonId = null): self
    {
        $query = self::where('student_id', $studentId)
            ->where('tutor_id', $tutorId)
            ->where('type', $type);

        if ($lessonId) {
            $query->where('lesson_id', $lessonId);
        }

        $conversation = $query->first();

        if (!$conversation) {
            $conversation = self::create([
                'student_id' => $studentId,
                'tutor_id' => $tutorId,
                'lesson_id' => $lessonId,
                'type' => $type,
                'status' => 'active'
            ]);
        }

        return $conversation;
    }

    /**
     * Get unread count for a specific user
     */
    public function getUnreadCountForUser(int $userId): int
    {
        if ($userId === $this->student_id) {
            return $this->student_unread_count;
        } elseif ($userId === $this->tutor_id) {
            return $this->tutor_unread_count;
        }

        return 0;
    }

    /**
     * Mark conversation as read for a user
     */
    public function markAsReadForUser(int $userId): bool
    {
        $now = Carbon::now();

        if ($userId === $this->student_id) {
            $this->update([
                'student_last_read_at' => $now,
                'student_unread_count' => 0
            ]);
        } elseif ($userId === $this->tutor_id) {
            $this->update([
                'tutor_last_read_at' => $now,
                'tutor_unread_count' => 0
            ]);
        } else {
            return false;
        }

        return true;
    }

    /**
     * Update conversation when new message is added
     */
    public function updateOnNewMessage(Message $message): void
    {
        $senderId = $message->sender_id;
        $now = Carbon::now();

        // Update last message info
        $this->update([
            'last_message_at' => $now,
            'last_message_by' => $senderId,
            'total_messages' => $this->total_messages + 1,
        ]);

        // Update unread counts
        if ($senderId === $this->student_id) {
            // Student sent message, increment tutor's unread count
            $this->increment('tutor_unread_count');
        } elseif ($senderId === $this->tutor_id) {
            // Tutor sent message, increment student's unread count
            $this->increment('student_unread_count');
        }
    }

    /**
     * Get the other participant in the conversation
     */
    public function getOtherParticipant(int $currentUserId): ?User
    {
        if ($currentUserId === $this->student_id) {
            return $this->tutor;
        } elseif ($currentUserId === $this->tutor_id) {
            return $this->student;
        }

        return null;
    }

    /**
     * Check if user is participant in conversation
     */
    public function isParticipant(int $userId): bool
    {
        return $userId === $this->student_id || $userId === $this->tutor_id;
    }

    /**
     * Get conversation title
     */
    public function getConversationTitle(int $currentUserId): string
    {
        if ($this->title) {
            return $this->title;
        }

        $otherParticipant = $this->getOtherParticipant($currentUserId);

        if ($otherParticipant) {
            return $otherParticipant->first_name . ' ' . $otherParticipant->last_name;
        }

        return 'Conversation';
    }

    /**
     * Scope for active conversations
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for conversations with a specific user
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where(function($q) use ($userId) {
            $q->where('student_id', $userId)
              ->orWhere('tutor_id', $userId);
        });
    }

    /**
     * Scope for conversations with unread messages for a user
     */
    public function scopeWithUnreadForUser($query, int $userId)
    {
        return $query->where(function($q) use ($userId) {
            $q->where('student_id', $userId)->where('student_unread_count', '>', 0)
              ->orWhere('tutor_id', $userId)->where('tutor_unread_count', '>', 0);
        });
    }
}
