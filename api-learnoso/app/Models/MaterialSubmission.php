<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class MaterialSubmission extends Model
{
    use SoftDeletes;
    
    protected $table = "material_submissions";

    protected $fillable = [
        'evaluation_id', 'student_id', 'type', 'content',
        'file_paths', 'is_graded', 'score', 'feedback'
    ];

    protected $casts = [
        'file_paths' => 'array',
    ];

    public function evaluation(): BelongsTo
    {
        return $this->belongsTo(MaterialEvaluation::class, 'evaluation_id');
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class, 'student_id');
    }
}
