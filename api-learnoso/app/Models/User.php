<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON>tie\Permission\Traits\HasRoles;
use Bavix\Wallet\Traits\HasWallet;
use Bavix\Wallet\Interfaces\Wallet;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Spatie\Permission\Models\Role;

class User extends Authenticatable implements MustVerifyEmail, Wallet
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, HasWallet;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'country',
        'password',
        'phone',
        'notification_settings',
        'failed_login_attempts',
        'last_failed_login_at',
        'account_locked_until',
        'lockout_reason',
        'last_successful_login_at',
        'last_login_ip',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'two_factor_disabled_at'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'notification_settings' => 'array',
        'last_failed_login_at' => 'datetime',
        'account_locked_until' => 'datetime',
        'last_successful_login_at' => 'datetime',
        'two_factor_enabled' => 'boolean',
        'two_factor_recovery_codes' => 'array',
        'two_factor_confirmed_at' => 'datetime',
        'two_factor_disabled_at' => 'datetime'
    ];

    // Link to the Tutor model
    public function tutor()
    {
        return $this->hasOne(Tutor::class);
    }

    // Link to the Student model
    public function student()
    {
        return $this->hasOne(Student::class);
    }

    public function isAdmin()
    {
        return $this->hasRole('admin');
    }

    public function isSuperAdmin()
    {
        return $this->hasRole('super_admin');
    }

    public function isStudent()
    {
        return $this->hasRole('student');
    }

    public function isTutor()
    {
        return $this->hasRole('tutor');
    }

    public function courses()
    {
        return $this->belongsToMany(Course::class, 'course_user', 'user_id', 'course_id');
    }

    // Conversations as student
    public function studentConversations()
    {
        return $this->hasMany(Conversation::class, 'student_id');
    }

    // Conversations as tutor
    public function tutorConversations()
    {
        return $this->hasMany(Conversation::class, 'tutor_id');
    }

    // All conversations for this user
    public function allConversations()
    {
        return Conversation::where('student_id', $this->id)
            ->orWhere('tutor_id', $this->id);
    }

    // Messages sent by this user
    public function sentMessages()
    {
        return $this->hasMany(Message::class, 'sender_id');
    }

    // Reviews given by this user as tutor
    public function tutorReviews()
    {
        return $this->hasMany(TutorReview::class, 'tutor_id');
    }

    // Lessons where this user is the student
    public function studentLessons()
    {
        return $this->hasMany(Lesson::class, 'student_id');
    }

    // Lessons where this user is the tutor
    public function tutorLessons()
    {
        return $this->hasMany(Lesson::class, 'tutor_id');
    }

    public function currentRole()
    {
        return $this->hasOne(CurrentRole::class);
    }

    public function onboardingStatus()
    {
        return $this->hasOne(OnboardingProgress::class);
    }

    public function switchRole(string $roleName)
    {
        $user = Auth::user();
        $role = Role::where('name', $roleName)->firstOrFail();

        if ($user->hasRole($roleName)) {
            Log::info("User {$user->id} already has role {$roleName}. ");
        }

        // Update the user's current role
        $currentRole = CurrentRole::where('user_id', $user->id)->firstOrFail();
        $currentRole->update(['role_id' => $role->id, 'lastly_switched_on' => now()]);

        // Update user's currentRole attribute for the session
        $user->setRelation('currentRole', $currentRole);

        return true;
    }


    public function switchToStudent()
    {
        return $this->switchRole('student');
    }


    public function switchToTutor()
    {
        return $this->switchRole('tutor');
    }


    public function switchToAdmin()
    {
        return $this->switchRole('admin');
    }

    public function languages()
    {
        return $this->belongsToMany(Language::class, 'language_user', 'user_id', 'language_id');
    }

    public function educations()
    {
        return $this->hasMany(Education::class);
    }

    public function stripeConnectedAccount()
    {
        return $this->hasOne(StripeConnectedAccount::class);
    }

    public function getNotificationSettings()
    {
        return $this->notification_settings ?? [
            'email' => true,
            'push' => true,
            'sms' => false,
            'notification_types' => [
                'lesson_scheduled' => true,
                'lesson_cancelled' => true,
                'lesson_reminder' => true,
                'payment_received' => true,
                'withdrawal_processed' => true,
                'system_updates' => true
            ],
            'real_time_chat' => [
                'new_messages' => true,
                'typing_indicators' => true,
                'online_status' => true,
                'message_sounds' => true,
                'desktop_notifications' => true,
                'email_for_missed_messages' => false,
                'conversation_notifications' => true
            ],
            'browser_notifications' => [
                'enabled' => true,
                'chat_messages' => true,
                'lesson_updates' => true,
                'system_alerts' => true
            ],
            'mobile_push' => [
                'enabled' => true,
                'chat_messages' => true,
                'lesson_reminders' => true,
                'payment_updates' => true
            ]
        ];
    }

    public function updateNotificationSettings(array $settings)
    {
        $this->notification_settings = array_merge($this->getNotificationSettings(), $settings);
        $this->save();
        return $this->notification_settings;
    }

    /**
     * Check if account is currently locked
     */
    public function isAccountLocked(): bool
    {
        return $this->account_locked_until && $this->account_locked_until->isFuture();
    }

    /**
     * Get remaining lockout time in minutes
     */
    public function getRemainingLockoutTime(): int
    {
        if (!$this->isAccountLocked()) {
            return 0;
        }

        return $this->account_locked_until->diffInMinutes(now());
    }

    /**
     * Record failed login attempt
     */
    public function recordFailedLoginAttempt(string $ipAddress): void
    {
        $this->increment('failed_login_attempts');
        $this->update([
            'last_failed_login_at' => now(),
            'last_login_ip' => $ipAddress
        ]);
    }

    /**
     * Lock account for specified duration
     */
    public function lockAccount(int $minutes, string $reason = 'Multiple failed login attempts'): void
    {
        $this->update([
            'account_locked_until' => now()->addMinutes($minutes),
            'lockout_reason' => $reason
        ]);
    }

    /**
     * Reset failed login attempts
     */
    public function resetFailedLoginAttempts(): void
    {
        $this->update([
            'failed_login_attempts' => 0,
            'last_failed_login_at' => null,
            'account_locked_until' => null,
            'lockout_reason' => null
        ]);
    }

    /**
     * Record successful login
     */
    public function recordSuccessfulLogin(string $ipAddress): void
    {
        $this->update([
            'last_successful_login_at' => now(),
            'last_login_ip' => $ipAddress,
            'failed_login_attempts' => 0,
            'last_failed_login_at' => null,
            'account_locked_until' => null,
            'lockout_reason' => null
        ]);
    }

    /**
     * Check if user has exceeded max failed attempts
     */
    public function hasExceededMaxFailedAttempts(int $maxAttempts = 5): bool
    {
        return $this->failed_login_attempts >= $maxAttempts;
    }

    /**
     * Check if user has 2FA enabled and confirmed
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->two_factor_enabled &&
               !is_null($this->two_factor_secret) &&
               !is_null($this->two_factor_confirmed_at);
    }

    /**
     * Check if user has 2FA setup but not confirmed
     */
    public function hasTwoFactorPending(): bool
    {
        return !is_null($this->two_factor_secret) &&
               is_null($this->two_factor_confirmed_at);
    }

    /**
     * Enable 2FA for the user
     */
    public function enableTwoFactor(string $secret, array $recoveryCodes): void
    {
        $this->update([
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt($secret),
            'two_factor_recovery_codes' => $recoveryCodes,
            'two_factor_confirmed_at' => now(),
            'two_factor_disabled_at' => null
        ]);
    }

    /**
     * Disable 2FA for the user
     */
    public function disableTwoFactor(): void
    {
        $this->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
            'two_factor_confirmed_at' => null,
            'two_factor_disabled_at' => now()
        ]);
    }

    /**
     * Get decrypted 2FA secret
     */
    public function getTwoFactorSecret(): ?string
    {
        return $this->two_factor_secret ? decrypt($this->two_factor_secret) : null;
    }

    /**
     * Use a recovery code
     */
    public function useRecoveryCode(string $code): bool
    {
        $recoveryCodes = $this->two_factor_recovery_codes ?? [];

        if (in_array($code, $recoveryCodes)) {
            // Remove used code
            $updatedCodes = array_values(array_diff($recoveryCodes, [$code]));
            $this->update(['two_factor_recovery_codes' => $updatedCodes]);
            return true;
        }

        return false;
    }

    /**
     * Generate new recovery codes
     */
    public function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(bin2hex(random_bytes(5)));
        }

        $this->update(['two_factor_recovery_codes' => $codes]);
        return $codes;
    }

}


