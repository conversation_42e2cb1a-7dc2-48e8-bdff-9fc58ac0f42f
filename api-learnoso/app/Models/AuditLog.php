<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'action',
        'description',
        'user_id',
        'actor_id',
        'actor_type',
        'subject_type',
        'subject_id',
        'old_values',
        'new_values',
        'metadata',
        'ip_address',
        'user_agent',
        'request_method',
        'request_url',
        'request_data',
        'risk_level',
        'is_suspicious',
        'suspicious_reason',
        'session_id',
        'auth_method',
        'api_version',
        'source',
        'tags',
        'status',
        'requires_review',
        'reviewed_at',
        'reviewed_by'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'request_data' => 'array',
        'tags' => 'array',
        'is_suspicious' => 'boolean',
        'requires_review' => 'boolean',
        'reviewed_at' => 'datetime'
    ];

    // Risk level constants
    const RISK_LOW = 'low';
    const RISK_MEDIUM = 'medium';
    const RISK_HIGH = 'high';
    const RISK_CRITICAL = 'critical';

    // Status constants
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_PENDING = 'pending';
    const STATUS_CANCELLED = 'cancelled';

    // Event type constants
    const EVENT_LOGIN = 'login';
    const EVENT_LOGOUT = 'logout';
    const EVENT_PASSWORD_CHANGE = 'password_change';
    const EVENT_TWO_FA_ENABLE = '2fa_enable';
    const EVENT_TWO_FA_DISABLE = '2fa_disable';
    const EVENT_ACCOUNT_LOCKED = 'account_locked';
    const EVENT_ACCOUNT_UNLOCKED = 'account_unlocked';
    const EVENT_ROLE_CHANGED = 'role_changed';
    const EVENT_PAYMENT = 'payment';
    const EVENT_WITHDRAWAL = 'withdrawal';
    const EVENT_LESSON_SCHEDULED = 'lesson_scheduled';
    const EVENT_LESSON_CANCELLED = 'lesson_cancelled';
    const EVENT_DATA_EXPORT = 'data_export';
    const EVENT_ADMIN_ACTION = 'admin_action';
    const EVENT_API_ACCESS = 'api_access';
    const EVENT_SUSPICIOUS_ACTIVITY = 'suspicious_activity';

    // Action constants
    const ACTION_CREATED = 'created';
    const ACTION_UPDATED = 'updated';
    const ACTION_DELETED = 'deleted';
    const ACTION_ACCESSED = 'accessed';
    const ACTION_FAILED = 'failed';
    const ACTION_ATTEMPTED = 'attempted';

    /**
     * User who was affected by the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * User who performed the action (actor)
     */
    public function actor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'actor_id');
    }

    /**
     * User who reviewed this audit log
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get the subject model (polymorphic relationship)
     */
    public function subject(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, 'subject_type', 'subject_id');
    }

    /**
     * Scope for high risk events
     */
    public function scopeHighRisk($query)
    {
        return $query->whereIn('risk_level', [self::RISK_HIGH, self::RISK_CRITICAL]);
    }

    /**
     * Scope for suspicious activities
     */
    public function scopeSuspicious($query)
    {
        return $query->where('is_suspicious', true);
    }

    /**
     * Scope for events requiring review
     */
    public function scopeRequiresReview($query)
    {
        return $query->where('requires_review', true)->whereNull('reviewed_at');
    }

    /**
     * Scope for specific event types
     */
    public function scopeEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for specific actor
     */
    public function scopeByActor($query, $actorId)
    {
        return $query->where('actor_id', $actorId);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent events (last 24 hours)
     */
    public function scopeRecent($query)
    {
        return $query->where('created_at', '>=', now()->subDay());
    }

    /**
     * Mark as reviewed
     */
    public function markAsReviewed(User $reviewer, string $notes = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($notes) {
            $metadata['review_notes'] = $notes;
        }
        $metadata['reviewed_timestamp'] = now()->toISOString();

        return $this->update([
            'reviewed_at' => now(),
            'reviewed_by' => $reviewer->id,
            'requires_review' => false,
            'metadata' => $metadata
        ]);
    }

    /**
     * Mark as suspicious
     */
    public function markAsSuspicious(string $reason): bool
    {
        return $this->update([
            'is_suspicious' => true,
            'suspicious_reason' => $reason,
            'risk_level' => self::RISK_HIGH,
            'requires_review' => true
        ]);
    }

    /**
     * Get formatted description with context
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->description;

        if ($this->user) {
            $description .= " (User: {$this->user->email})";
        }

        if ($this->actor && $this->actor_id !== $this->user_id) {
            $description .= " (By: {$this->actor->email})";
        }

        return $description;
    }

    /**
     * Get risk level badge color
     */
    public function getRiskBadgeColorAttribute(): string
    {
        return match($this->risk_level) {
            self::RISK_LOW => 'green',
            self::RISK_MEDIUM => 'yellow',
            self::RISK_HIGH => 'orange',
            self::RISK_CRITICAL => 'red',
            default => 'gray'
        };
    }

    /**
     * Check if event happened recently (within last hour)
     */
    public function isRecent(): bool
    {
        return $this->created_at->isAfter(now()->subHour());
    }

    /**
     * Get sanitized request data (remove sensitive fields)
     */
    public function getSanitizedRequestDataAttribute(): array
    {
        $data = $this->request_data ?? [];

        // Remove sensitive fields
        $sensitiveFields = ['password', 'password_confirmation', 'token', 'secret', 'api_key'];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }
}
