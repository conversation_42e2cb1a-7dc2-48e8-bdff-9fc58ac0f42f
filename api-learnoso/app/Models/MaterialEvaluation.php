<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MaterialEvaluation extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'material_id',
        'questions',
        'assignments',
        'assignees',
        'title',
        'type',
        'due_date',
    ];

    protected $casts = [
        'questions' => 'array',
        'assignments' => 'array',
        'assignees' => 'array',
        'due_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function material(): BelongsTo
    {
        return $this->belongsTo(CourseMaterial::class, 'material_id');
    }

    public function submissions(): HasMany
    {
        return $this->hasMany(MaterialSubmission::class, 'evaluation_id');
    }

    public function getAssignees(string $list)
    {
        // Gets the list and seperates the ids in to array
        $list = str_replace(["[", "]"], "", $list);
        $ids = explode(",", $list);

        // Fetches and returns the users/assignees
        $assignees = Student::with(["user"])->whereIn("id", $ids)->get()->toArray();
        return $assignees;
    }
    public function assignees(): Attribute
    {
        return Attribute::make(
            get: fn (string $list) => $this->getAssignees($list),
        );
    }
}

