<?php

namespace App\Events;

use App\Models\Lesson;
use App\Models\LessonTracker;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LessonStatusUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $lesson;
    public $tracker;
    public $statusType;
    public $additionalData;

    /**
     * Create a new event instance.
     */
    public function __construct(Lesson $lesson, LessonTracker $tracker = null, string $statusType = 'general', array $additionalData = [])
    {
        $this->lesson = $lesson;
        $this->tracker = $tracker;
        $this->statusType = $statusType;
        $this->additionalData = $additionalData;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            // Private channel for the student
            new PrivateChannel('lesson.' . $this->lesson->id . '.student.' . $this->lesson->student_id),

            // Private channel for the tutor
            new PrivateChannel('lesson.' . $this->lesson->id . '.tutor.' . $this->lesson->tutor_id),

            // General lesson channel for both participants
            new PrivateChannel('lesson.' . $this->lesson->id),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $data = [
            'lesson_id' => $this->lesson->id,
            'status_type' => $this->statusType,
            'lesson_status' => $this->lesson->status,
            'lesson_data' => [
                'id' => $this->lesson->id,
                'starts_at' => $this->lesson->starts_at->toISOString(),
                'ends_at' => $this->lesson->ends_at?->toISOString(),
                'status' => $this->lesson->status,
                'confirmed' => $this->lesson->confirmed,
                'attended' => $this->lesson->attended,
            ],
            'participants' => [
                'student' => [
                    'id' => $this->lesson->student_id,
                    'name' => $this->lesson->student->first_name . ' ' . $this->lesson->student->last_name,
                ],
                'tutor' => [
                    'id' => $this->lesson->tutor_id,
                    'name' => $this->lesson->tutor->first_name . ' ' . $this->lesson->tutor->last_name,
                ],
            ],
            'timestamp' => now()->toISOString(),
        ];

        // Add tracker data if available
        if ($this->tracker) {
            $data['tracker'] = [
                'id' => $this->tracker->id,
                'status' => $this->tracker->status,
                'is_active' => $this->tracker->is_active,
                'is_on_break' => $this->tracker->isOnBreak(),
                'current_duration' => $this->tracker->getCurrentSessionDuration(),
                'formatted_duration' => $this->tracker->getFormattedTotalDuration(),
                'break_count' => $this->tracker->break_count,
                'session_started_at' => $this->tracker->session_started_at?->toISOString(),
                'billable_amount' => $this->tracker->billable_amount,
            ];
        }

        // Add any additional data
        if (!empty($this->additionalData)) {
            $data['additional'] = $this->additionalData;
        }

        return $data;
    }

    /**
     * Get the event name for broadcasting.
     */
    public function broadcastAs(): string
    {
        return 'lesson.status.updated';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        // Only broadcast if lesson exists and has participants
        return $this->lesson && $this->lesson->student_id && $this->lesson->tutor_id;
    }
}
