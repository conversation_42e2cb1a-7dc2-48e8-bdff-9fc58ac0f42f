<?php

namespace App\Events;

use App\Models\Message;
use App\Models\Conversation;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $conversation;

    /**
     * Create a new event instance.
     */
    public function __construct(Message $message)
    {
        $this->message = $message->load(['sender', 'replyToMessage']);
        $this->conversation = $message->conversation->load(['student', 'tutor']);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            // Private channel for the conversation
            new PrivateChannel('conversation.' . $this->conversation->id),

            // Private channels for each participant
            new PrivateChannel('user.' . $this->conversation->student_id . '.messages'),
            new PrivateChannel('user.' . $this->conversation->tutor_id . '.messages'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $data = [
            'message' => [
                'id' => $this->message->id,
                'conversation_id' => $this->message->conversation_id,
                'content' => $this->message->content,
                'type' => $this->message->type,
                'status' => $this->message->status,
                'created_at' => $this->message->created_at->toISOString(),
                'sender' => [
                    'id' => $this->message->sender_id,
                    'name' => $this->message->sender
                        ? $this->message->sender->first_name . ' ' . $this->message->sender->last_name
                        : 'System',
                    'avatar' => $this->message->sender?->profile_image_url,
                ],
                'reply_to' => $this->message->replyToMessage ? [
                    'id' => $this->message->replyToMessage->id,
                    'content' => $this->message->replyToMessage->getPreview(50),
                    'sender_name' => $this->message->replyToMessage->sender
                        ? $this->message->replyToMessage->sender->first_name . ' ' . $this->message->replyToMessage->sender->last_name
                        : 'System'
                ] : null,
                'attachment' => $this->message->hasAttachment() ? [
                    'file_name' => $this->message->file_name,
                    'file_type' => $this->message->file_type,
                    'file_size' => $this->message->getFormattedFileSize(),
                    'file_url' => $this->message->getFileUrl(),
                ] : null,
                'metadata' => $this->message->metadata,
            ],
            'conversation' => [
                'id' => $this->conversation->id,
                'type' => $this->conversation->type,
                'last_message_at' => $this->conversation->last_message_at->toISOString(),
                'student_unread_count' => $this->conversation->student_unread_count,
                'tutor_unread_count' => $this->conversation->tutor_unread_count,
                'participants' => [
                    'student' => [
                        'id' => $this->conversation->student->id,
                        'name' => $this->conversation->student->first_name . ' ' . $this->conversation->student->last_name,
                    ],
                    'tutor' => [
                        'id' => $this->conversation->tutor->id,
                        'name' => $this->conversation->tutor->first_name . ' ' . $this->conversation->tutor->last_name,
                    ],
                ],
            ],
            'timestamp' => now()->toISOString(),
        ];

        return $data;
    }

    /**
     * Get the event name for broadcasting.
     */
    public function broadcastAs(): string
    {
        return 'message.sent';
    }

    /**
     * Determine if this event should broadcast.
     */
    public function shouldBroadcast(): bool
    {
        return $this->message && $this->conversation &&
               $this->conversation->status === 'active';
    }
}
