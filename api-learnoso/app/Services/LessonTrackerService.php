<?php

namespace App\Services;

use App\Models\Lesson;
use App\Models\LessonTracker;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Services\CacheService;
use App\Services\RealTimeNotificationService;
use Illuminate\Support\Facades\Cache;

class LessonTrackerService
{
    protected CacheService $cacheService;
    protected RealTimeNotificationService $realTimeService;

    public function __construct(CacheService $cacheService, RealTimeNotificationService $realTimeService)
    {
        $this->cacheService = $cacheService;
        $this->realTimeService = $realTimeService;
    }

    /**
     * Initialize a lesson tracker for a lesson
     */
    public function initializeTracker(Lesson $lesson): LessonTracker
    {
        // Check if tracker already exists
        $existingTracker = LessonTracker::where('lesson_id', $lesson->id)->first();

        if ($existingTracker) {
            return $existingTracker;
        }

        // Create new tracker
        return LessonTracker::create([
            'lesson_id' => $lesson->id,
            'student_id' => $lesson->student_id,
            'tutor_id' => $lesson->tutor_id,
            'agora_channel' => $lesson->channel_name,
            'status' => 'not_started'
        ]);
    }

    /**
     * Start tracking a lesson session
     */
    public function startSession(int $lessonId, string $agoraChannel = null): LessonTracker
    {
        try {
            $lesson = Lesson::findOrFail($lessonId);
            $tracker = $this->initializeTracker($lesson);

            // Ensure no other active sessions for this user
            $this->deactivateUserSessions($lesson->student_id, $lesson->tutor_id);

            // Start the session
            $tracker->startSession();

            if ($agoraChannel) {
                $tracker->update(['agora_channel' => $agoraChannel]);
            }

            // Invalidate related caches
            $this->invalidateSessionCaches($lessonId, $lesson->student_id, $lesson->tutor_id);

            // Broadcast real-time status update
            $this->realTimeService->broadcastLessonSessionStarted($lesson, $tracker);

            Log::info("Lesson session started", [
                'lesson_id' => $lessonId,
                'tracker_id' => $tracker->id,
                'started_at' => $tracker->session_started_at
            ]);

            return $tracker;

        } catch (Exception $e) {
            Log::error("Failed to start lesson session", [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * End a lesson session
     */
    public function endSession(int $lessonId): LessonTracker
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lessonId)->firstOrFail();

            // Get student and tutor IDs before ending session
            $studentId = $tracker->student_id;
            $tutorId = $tracker->tutor_id;

            // End the session
            $tracker->endSession();

            // Invalidate related caches
            $this->invalidateSessionCaches($lessonId, $studentId, $tutorId);

            // Broadcast real-time status update
            $this->realTimeService->broadcastLessonSessionEnded($tracker->lesson, $tracker);

            Log::info("Lesson session ended", [
                'lesson_id' => $lessonId,
                'tracker_id' => $tracker->id,
                'total_duration' => $tracker->total_duration_seconds,
                'active_duration' => $tracker->active_duration_seconds,
                'billable_amount' => $tracker->billable_amount
            ]);

            // Update lesson metadata with final tracking data
            $this->updateLessonMetadata($tracker);

            return $tracker;

        } catch (Exception $e) {
            Log::error("Failed to end lesson session", [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Start a break in the lesson
     */
    public function startBreak(int $lessonId): LessonTracker
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lessonId)
                ->where('is_active', true)
                ->firstOrFail();

            $tracker->startBreak();

            // Broadcast real-time break update
            $this->realTimeService->broadcastLessonBreakStarted($tracker->lesson, $tracker);

            Log::info("Break started", [
                'lesson_id' => $lessonId,
                'tracker_id' => $tracker->id,
                'break_started_at' => $tracker->last_break_started_at
            ]);

            return $tracker;

        } catch (Exception $e) {
            Log::error("Failed to start break", [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * End a break in the lesson
     */
    public function endBreak(int $lessonId): LessonTracker
    {
        try {
            $tracker = LessonTracker::where('lesson_id', $lessonId)
                ->where('status', 'paused')
                ->firstOrFail();

            $tracker->endBreak();

            // Broadcast real-time break update
            $this->realTimeService->broadcastLessonBreakEnded($tracker->lesson, $tracker);

            Log::info("Break ended", [
                'lesson_id' => $lessonId,
                'tracker_id' => $tracker->id,
                'total_break_duration' => $tracker->break_duration_seconds,
                'break_count' => $tracker->break_count
            ]);

            return $tracker;

        } catch (Exception $e) {
            Log::error("Failed to end break", [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get real-time session status
     */
    public function getSessionStatus(int $lessonId): array
    {
        // Try to get from cache first for frequently accessed sessions
        $cacheKey = "session_status_{$lessonId}";
        $cached = $this->cacheService->remember($cacheKey, CacheService::SHORT_TTL, function() use ($lessonId) {
            $tracker = LessonTracker::where('lesson_id', $lessonId)->first();

            if (!$tracker) {
                return [
                    'status' => 'not_initialized',
                    'is_active' => false,
                    'current_duration' => 0,
                    'formatted_duration' => '00:00:00'
                ];
            }

            $currentDuration = $tracker->getCurrentSessionDuration();

            return [
                'status' => $tracker->status,
                'is_active' => $tracker->is_active,
                'is_on_break' => $tracker->isOnBreak(),
                'current_duration' => $currentDuration,
                'formatted_duration' => $this->formatDuration($currentDuration),
                'break_count' => $tracker->break_count,
                'total_break_duration' => $tracker->break_duration_seconds,
                'formatted_break_duration' => $tracker->getFormattedBreakDuration(),
                'session_started_at' => $tracker->session_started_at?->toISOString(),
                'last_break_started_at' => $tracker->last_break_started_at?->toISOString()
            ];
        });

        return $cached;
    }

    /**
     * Get comprehensive lesson statistics
     */
    public function getLessonStatistics(int $lessonId): array
    {
        $tracker = LessonTracker::with(['lesson', 'student', 'tutor'])
            ->where('lesson_id', $lessonId)
            ->first();

        if (!$tracker) {
            return ['error' => 'Lesson tracker not found'];
        }

        return [
            'lesson_id' => $lessonId,
            'status' => $tracker->status,
            'session_started_at' => $tracker->session_started_at?->toISOString(),
            'session_ended_at' => $tracker->session_ended_at?->toISOString(),
            'total_duration' => [
                'seconds' => $tracker->total_duration_seconds,
                'formatted' => $tracker->getFormattedTotalDuration()
            ],
            'active_duration' => [
                'seconds' => $tracker->active_duration_seconds,
                'formatted' => $tracker->getFormattedActiveDuration()
            ],
            'break_duration' => [
                'seconds' => $tracker->break_duration_seconds,
                'formatted' => $tracker->getFormattedBreakDuration()
            ],
            'break_count' => $tracker->break_count,
            'is_billable' => $tracker->is_billable,
            'billable_amount' => $tracker->billable_amount,
            'minimum_session_seconds' => $tracker->minimum_session_seconds,
            'participants' => [
                'student' => [
                    'id' => $tracker->student->id,
                    'name' => $tracker->student->first_name . ' ' . $tracker->student->last_name
                ],
                'tutor' => [
                    'id' => $tracker->tutor->id,
                    'name' => $tracker->tutor->first_name . ' ' . $tracker->tutor->last_name
                ]
            ]
        ];
    }

    /**
     * Get active sessions for a user (student or tutor)
     */
    public function getActiveSessionsForUser(int $userId): array
    {
        // Check cache first
        $cached = $this->cacheService->getUserSessions($userId);
        if ($cached !== null) {
            return $cached;
        }

        $activeSessions = LessonTracker::active()
            ->where(function($query) use ($userId) {
                $query->where('student_id', $userId)
                      ->orWhere('tutor_id', $userId);
            })
            ->with(['lesson', 'student', 'tutor'])
            ->get();

        $result = $activeSessions->map(function($tracker) use ($userId) {
            return [
                'lesson_id' => $tracker->lesson_id,
                'status' => $tracker->status,
                'current_duration' => $tracker->getCurrentSessionDuration(),
                'formatted_duration' => $this->formatDuration($tracker->getCurrentSessionDuration()),
                'is_on_break' => $tracker->isOnBreak(),
                'other_participant' => $tracker->student_id === $userId
                    ? $tracker->tutor->first_name . ' ' . $tracker->tutor->last_name
                    : $tracker->student->first_name . ' ' . $tracker->student->last_name
            ];
        })->toArray();

        // Cache the result
        $this->cacheService->cacheUserSessions($userId, $result);

        return $result;
    }

    /**
     * Auto-complete sessions that have exceeded maximum duration
     */
    public function autoCompleteExpiredSessions(): void
    {
        // Find sessions that have been active for more than 4 hours (configurable)
        $maxSessionHours = config('app.max_session_hours', 4);
        $cutoffTime = Carbon::now()->subHours($maxSessionHours);

        $expiredSessions = LessonTracker::active()
            ->where('session_started_at', '<', $cutoffTime)
            ->get();

        foreach ($expiredSessions as $tracker) {
            try {
                $tracker->endSession();

                Log::warning("Auto-completed expired session", [
                    'lesson_id' => $tracker->lesson_id,
                    'tracker_id' => $tracker->id,
                    'duration_hours' => $tracker->total_duration_seconds / 3600
                ]);
            } catch (Exception $e) {
                Log::error("Failed to auto-complete expired session", [
                    'lesson_id' => $tracker->lesson_id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Deactivate all sessions for specific users
     */
    private function deactivateUserSessions(int $studentId, int $tutorId): void
    {
        LessonTracker::active()
            ->where(function($query) use ($studentId, $tutorId) {
                $query->where('student_id', $studentId)
                      ->orWhere('tutor_id', $tutorId);
            })
            ->update([
                'is_active' => false,
                'status' => 'cancelled'
            ]);
    }

    /**
     * Update lesson metadata with tracking information
     */
    private function updateLessonMetadata(LessonTracker $tracker): void
    {
        $lesson = $tracker->lesson;
        $currentMetadata = $lesson->metadata ?? [];

        $trackingData = [
            'tracking' => [
                'total_duration' => $tracker->getFormattedTotalDuration(),
                'active_duration' => $tracker->getFormattedActiveDuration(),
                'break_duration' => $tracker->getFormattedBreakDuration(),
                'break_count' => $tracker->break_count,
                'session_started_at' => $tracker->session_started_at?->toISOString(),
                'session_ended_at' => $tracker->session_ended_at?->toISOString(),
                'billable_amount' => $tracker->billable_amount
            ]
        ];

        $lesson->updateMetadata($trackingData);
    }

    /**
     * Format duration in seconds to HH:MM:SS
     */
    private function formatDuration(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $remainingSeconds = $seconds % 60;

        return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
    }

    /**
     * Invalidate caches related to a session.
     */
    private function invalidateSessionCaches(int $lessonId, int $studentId, int $tutorId): void
    {
        // Invalidate session status cache
        Cache::forget("session_status_{$lessonId}");

        // Invalidate user sessions cache
        $this->cacheService->getUserSessions($studentId); // Clear student cache
        $this->cacheService->getUserSessions($tutorId);   // Clear tutor cache

        Cache::forget(CacheService::USER_SESSIONS_PREFIX . $studentId);
        Cache::forget(CacheService::USER_SESSIONS_PREFIX . $tutorId);
    }
}
