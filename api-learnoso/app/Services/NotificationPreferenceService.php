<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Log;

class NotificationPreferenceService
{
    /**
     * Check if user allows real-time chat notifications
     */
    public function shouldSendChatNotification(User $user, string $notificationType): bool
    {
        $settings = $user->getNotificationSettings();
        $chatSettings = $settings['real_time_chat'] ?? [];

        // Map notification types to settings
        $settingKey = match($notificationType) {
            'new_message' => 'new_messages',
            'typing_indicator' => 'typing_indicators',
            'online_status' => 'online_status',
            'message_sound' => 'message_sounds',
            'desktop_notification' => 'desktop_notifications',
            'conversation_notification' => 'conversation_notifications',
            default => null
        };

        if (!$settingKey) {
            Log::warning('Unknown chat notification type', [
                'user_id' => $user->id,
                'notification_type' => $notificationType
            ]);
            return false;
        }

        return $chatSettings[$settingKey] ?? true;
    }

    /**
     * Check if user allows browser notifications
     */
    public function shouldSendBrowserNotification(User $user, string $notificationType): bool
    {
        $settings = $user->getNotificationSettings();
        $browserSettings = $settings['browser_notifications'] ?? [];

        // Check if browser notifications are enabled globally
        if (!($browserSettings['enabled'] ?? true)) {
            return false;
        }

        $settingKey = match($notificationType) {
            'chat_message' => 'chat_messages',
            'lesson_update' => 'lesson_updates',
            'system_alert' => 'system_alerts',
            default => null
        };

        if (!$settingKey) {
            return false;
        }

        return $browserSettings[$settingKey] ?? true;
    }

    /**
     * Check if user allows mobile push notifications
     */
    public function shouldSendMobilePush(User $user, string $notificationType): bool
    {
        $settings = $user->getNotificationSettings();
        $mobileSettings = $settings['mobile_push'] ?? [];

        // Check if mobile push is enabled globally
        if (!($mobileSettings['enabled'] ?? true)) {
            return false;
        }

        $settingKey = match($notificationType) {
            'chat_message' => 'chat_messages',
            'lesson_reminder' => 'lesson_reminders',
            'payment_update' => 'payment_updates',
            default => null
        };

        if (!$settingKey) {
            return false;
        }

        return $mobileSettings[$settingKey] ?? true;
    }

    /**
     * Check if user allows email notifications for missed messages
     */
    public function shouldSendEmailForMissedMessages(User $user): bool
    {
        $settings = $user->getNotificationSettings();
        $chatSettings = $settings['real_time_chat'] ?? [];

        return $chatSettings['email_for_missed_messages'] ?? false;
    }

    /**
     * Get user's sound preference for chat messages
     */
    public function shouldPlayMessageSound(User $user): bool
    {
        return $this->shouldSendChatNotification($user, 'message_sound');
    }

    /**
     * Check if user wants to see typing indicators
     */
    public function shouldShowTypingIndicators(User $user): bool
    {
        return $this->shouldSendChatNotification($user, 'typing_indicator');
    }

    /**
     * Check if user wants to see online status
     */
    public function shouldShowOnlineStatus(User $user): bool
    {
        return $this->shouldSendChatNotification($user, 'online_status');
    }

    /**
     * Get notification preferences for a specific notification type
     */
    public function getNotificationChannels(User $user, string $notificationType): array
    {
        $channels = [];

        // Check each channel type
        if ($this->shouldSendBrowserNotification($user, $notificationType)) {
            $channels[] = 'browser';
        }

        if ($this->shouldSendMobilePush($user, $notificationType)) {
            $channels[] = 'mobile_push';
        }

        // Check email settings
        $settings = $user->getNotificationSettings();
        if ($settings['email'] ?? true) {
            $channels[] = 'email';
        }

        // Check SMS settings
        if ($settings['sms'] ?? false) {
            $channels[] = 'sms';
        }

        return $channels;
    }

    /**
     * Check if user has enabled a specific notification type
     */
    public function isNotificationTypeEnabled(User $user, string $notificationType): bool
    {
        $settings = $user->getNotificationSettings();
        $notificationTypes = $settings['notification_types'] ?? [];

        return $notificationTypes[$notificationType] ?? true;
    }

    /**
     * Get all disabled notification types for user
     */
    public function getDisabledNotificationTypes(User $user): array
    {
        $settings = $user->getNotificationSettings();
        $notificationTypes = $settings['notification_types'] ?? [];

        return array_keys(array_filter($notificationTypes, function($enabled) {
            return !$enabled;
        }));
    }

    /**
     * Check if user should receive real-time updates
     */
    public function shouldReceiveRealTimeUpdates(User $user): bool
    {
        $settings = $user->getNotificationSettings();

        // If any real-time feature is enabled, return true
        $chatSettings = $settings['real_time_chat'] ?? [];

        return ($chatSettings['new_messages'] ?? true) ||
               ($chatSettings['typing_indicators'] ?? true) ||
               ($chatSettings['online_status'] ?? true) ||
               ($chatSettings['conversation_notifications'] ?? true);
    }
}
