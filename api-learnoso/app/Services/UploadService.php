<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadService
{
    /**
     * Handle file upload and return the file URL.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param string|null $disk
     * @return string
     */
    public function uploadFile(UploadedFile $file, string $directory, ?string $disk = 'public'): string
    {
        // Generate a unique file name to avoid overwriting existing files
        $fileName = Str::uuid()->toString() . '.' . $file->getClientOriginalExtension();

        // Store the file in the specified directory on the specified disk
        $filePath = $file->storeAs($directory, $fileName, $disk);

        // Return the URL to access the file
        return Storage::disk($disk)->url($filePath);
    }

    /**
     * Delete a file from the storage.
     *
     * @param string $path
     * @param string|null $disk
     * @return bool
     */
    public function deleteFile(string $path, ?string $disk = 'public'): bool
    {
        // Remove the file from the storage
        return Storage::disk($disk)->delete($path);
    }

    /**
     * Check if a file exists in storage.
     *
     * @param string $path
     * @param string|null $disk
     * @return bool
     */
    public function fileExists(string $path, ?string $disk = 'public'): bool
    {
        // Check if the file exists on the disk
        return Storage::disk($disk)->exists($path);
    }
}
