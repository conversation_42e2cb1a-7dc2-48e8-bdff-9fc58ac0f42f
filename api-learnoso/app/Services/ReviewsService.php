<?php

namespace App\Services;

use App\Repositories\Contracts\ReviewsRepositoryInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReviewsService
{
    protected $ReviewsRepository;

    public function __construct(ReviewsRepositoryInterface $ReviewsRepository)
    {
        $this->ReviewsRepository = $ReviewsRepository;
    }


    public function getReviewsByTutorId($tutorId)
    {
        return $this->ReviewsRepository->getReviewsByTutorId($tutorId);
    }

    public function getReviewsByStudentId($studentId)
    {
        return $this->ReviewsRepository->getReviewsByStudentId($studentId);
    }

    public function createReview(array $reviewData)
    {

        if ($this->ReviewsRepository->hasStudentReviewedTutor($reviewData['student_id'], $reviewData['tutor_id'])) {
            throw new \Exception('You have already reviewed this tutor');
        }

        // check that student_id is same as the authenticated user
        if ($reviewData['student_id'] !== Auth::user()->student->id) {
            throw new \Exception('You are not authorized to review this tutor');
        }

        return $this->ReviewsRepository->createReview($reviewData);
    }

    public function updateReview($reviewId, array $data)
    {
        return $this->ReviewsRepository->updateReview($reviewId, $data);
    }

    public function deleteReview($reviewId)
    {
        return $this->ReviewsRepository->deleteReview($reviewId);
    }

    public function find($id)
    {

        return $this->ReviewsRepository->find($id);
    }

    public function all(Request $request)
    {
        return $this->ReviewsRepository->all($request);
    }
}
