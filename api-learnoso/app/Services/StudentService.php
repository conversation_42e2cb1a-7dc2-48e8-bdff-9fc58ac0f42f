<?php

namespace App\Services;

use App\Models\Student;
use App\Models\User;
use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\CourseRepositoryInterface;
use Illuminate\Support\Facades\Log;

class StudentService extends UserService
{
    protected $studentRepository;
    protected $courseRepository;

    public function __construct(
        StudentRepositoryInterface $studentRepository,
        CourseRepositoryInterface $courseRepository
    ) {
        $this->studentRepository = $studentRepository;
        $this->courseRepository = $courseRepository;
    }

    public function onboardStudent(array $data)
    {
        try {
            // Create or update student
            $student = $this->studentRepository->onboardStudent($data);

            // Attach courses if provided
            if (isset($data['courses_of_preference']) && is_array($data['courses_of_preference'])) {
                $this->attachCoursesToStudent($student, $data['courses_of_preference']);
            }
            return $student;
        } catch (\Exception $e) {
            Log::error('Failed to onboard student: ' . $e->getMessage());
            throw $e;
        }
    }

    public function attachCoursesToStudent(Student $student, array $courseIds)
    {
        $user = $this->studentRepository->find($student->id)->user;

        foreach ($courseIds as $courseId) {
            if ($this->courseRepository->find($courseId)) {
                // Check if the course is already attached
                if (!$user->courses->contains($courseId)) {
                    $user->courses()->attach($courseId);
                }
            }
        }
    }

    public function getAllStudents(){
        $users = $this->studentRepository->getStudents();
        return $users;
    }

}
