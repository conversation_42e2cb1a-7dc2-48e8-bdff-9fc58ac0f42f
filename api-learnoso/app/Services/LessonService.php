<?php

namespace App\Services;

use App\Models\User;
use App\Models\Lesson;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Repositories\Eloquent\LessonRepository;
use Illuminate\Support\Facades\Auth;
use App\Models\SystemSetting;

class LessonService
{
    protected $lessonRepository;

    public function __construct(LessonRepository $lessonRepository)
    {
        $this->lessonRepository = $lessonRepository;
    }

    public function scheduleLesson($student, $tutor, array $lessonData)
    {
        try {
            $courseCost = $this->calculateCourseCost(
                $tutor->tutor,
                $lessonData['starts_at'],
                $lessonData['ends_at'],
                $lessonData['duration'] ?? null
            );

            if (!$this->hasEnoughMoneyInWallet($student, $courseCost)) {
                throw new \Exception('Student does not have enough money in the wallet to pay for the lesson');
            }

            $systemUser = getSystemUser();

            return $this->lessonRepository->transaction(function () use ($lessonData, $student, $courseCost, $systemUser, $tutor) {
                $lesson = $this->lessonRepository->createLesson($lessonData);

                if(!$lesson->course_id){
                    $lesson->course_id = $lessonData['course_id'];
                    $lesson->save();
                }
                // Sync the tutor-student relationship
                $tutor->tutor->students()->syncWithoutDetaching($student->student->id);

                $metadata = [
                    'lesson_id' => $lesson->id,
                    'description' => 'Lesson payment',
                    'user' => Auth::user(),
                ];

                $this->transferToSystemWallet($systemUser, $student, $courseCost, $metadata);
                Log::info('Transaction committed successfully, student linked to tutor');

                return $lesson;
            });
        } catch (\Exception $e) {
            Log::error('Error scheduling lesson: ' . $e->getMessage());
            throw $e;
        }
    }


    public function rescheduleLesson($tutor, Lesson $lesson, array $rescheduleData)
    {
        try {
            return $this->lessonRepository->transaction(function () use ($tutor, $lesson, $rescheduleData) {
                $newCost = $this->calculateCourseCost(
                    $tutor->tutor,
                    $rescheduleData['starts_at'],
                    $rescheduleData['ends_at'],
                    $rescheduleData['duration'] ?? null
                );
                $oldCost = $this->calculateCourseCost(
                    $tutor->tutor,
                    $lesson->starts_at,
                    $lesson->ends_at,
                    $lesson->duration
                );
                $payDifference = $newCost - $oldCost;

                if ($payDifference > 0) {
                    // Calculate commission for the additional amount
                    $commissionRate = SystemSetting::getCommissionRate();
                    $minCommission = SystemSetting::getMinimumCommission();
                    $maxCommission = SystemSetting::getMaximumCommission();

                    $additionalCommission = $payDifference * $commissionRate;
                    $additionalCommission = max($minCommission, min($maxCommission, $additionalCommission));

                    $amountToTransfer = $payDifference - $additionalCommission;

                    // Log commission details for reschedule
                    Log::info('Lesson reschedule payment processed', [
                        'lesson_id' => $lesson->id,
                        'additional_cost' => $payDifference,
                        'commission_rate' => $commissionRate,
                        'commission_amount' => $additionalCommission,
                        'amount_to_tutor' => $amountToTransfer
                    ]);

                    $metadata = [
                        'lesson_id' => $lesson->id,
                        'description' => 'Lesson reschedule payment',
                        'commission_rate' => $commissionRate,
                        'commission_amount' => $additionalCommission
                    ];

                    $this->transferToSystemWallet(getSystemUser(), $lesson->student, $payDifference, $metadata);
                }

                return $this->lessonRepository->updateLesson($lesson, $rescheduleData);
            });
        } catch (\Exception $e) {
            Log::error('Error rescheduling lesson: ' . $e->getMessage());
            return false;
        }
    }

    public function confirmLesson(Lesson $lesson, $duration = null)
    {
        try {
            $tutor = $lesson->tutor;
            $metadata = [
                'lesson_id' => $lesson->id,
                'description' => 'Lesson payment',
                'duration' => $duration
            ];

            $cost = $this->calculateCourseCost(
                $tutor->tutor,
                $lesson->starts_at,
                $lesson->ends_at,
                $duration
            );
            if (!$this->hasEnoughMoneyInWallet(getSystemUser(), $cost)) {
                throw new \Exception('System user does not have enough money in the wallet to pay for the lesson');
            }

            // Calculate commission using system settings
            $commissionRate = SystemSetting::getCommissionRate();
            $minCommission = SystemSetting::getMinimumCommission();
            $maxCommission = SystemSetting::getMaximumCommission();

            $commission = $cost * $commissionRate;
            
            // Ensure commission is within min/max bounds
            $commission = max($minCommission, min($maxCommission, $commission));

            $amountToTransfer = $cost - $commission;

            // Log commission details
            Log::info('Lesson payment processed', [
                'lesson_id' => $lesson->id,
                'total_cost' => $cost,
                'commission_rate' => $commissionRate,
                'commission_amount' => $commission,
                'amount_to_tutor' => $amountToTransfer,
                'duration' => $duration
            ]);

            $this->transferFromSystemWallet($tutor, $amountToTransfer, $metadata);
            $lesson->attended = true;
            $lesson->confirmed = true;
            $lesson->confirmed_at = now();

            $this->lessonRepository->updateLesson($lesson, [
                'attended' => true,
                'confirmed' => true,
                'confirmed_at' => now(),
                'duration' => $duration
            ]);
        } catch (\Exception $e) {
            Log::error('Error confirming lesson: ' . $e->getMessage());
            throw $e;
        }
    }



    public function cancelLesson(Lesson $lesson, $cancellationReason)
    {
        try {
            return $this->lessonRepository->transaction(function () use ($lesson, $cancellationReason) {
                $refundAmount = $this->calculateRefundAmount($lesson);

                $lesson->student->wallet->deposit($refundAmount, [
                    'lesson_id' => $lesson->id,
                    'description' => 'Lesson cancellation refund',
                ]);

                $this->lessonRepository->updateLesson($lesson, [
                    'cancellation_reason' => $cancellationReason,
                ]);

                $this->lessonRepository->deleteLesson($lesson);

                return true;
            });
        } catch (\Exception $e) {
            Log::error('Error canceling lesson: ' . $e->getMessage());
            return false;
        }
    }

    // Utility methods
    private function hasEnoughMoneyInWallet(User $user, $amount)
    {
        return (float) $user->wallet->balance >= $amount;
    }

    private function transferToSystemWallet(User $systemUser, User $fromUser, $amount, $metadata)
    {
        $fromUser->wallet->transfer($systemUser, $amount, $metadata);
    }

    private function transferFromSystemWallet(User $user, $amount, $metadata)
    {
        $systemUser = getSystemUser();
        $systemUser->wallet->transfer($user, $amount, $metadata);
    }

    private function calculateRoundedDuration($startsAt, $endsAt)
    {
        $durationInHours = (strtotime($endsAt) - strtotime($startsAt)) / 3600;
        return round($durationInHours * 60 / 15) * 15 / 60;
    }

    private function calculateCourseCost($tutorProfile, $startsAt, $endsAt, $duration = null)
    {
        if (is_object($tutorProfile) && is_numeric($tutorProfile->price)) {
            $chargePerHour = $tutorProfile->price;

            // Use provided duration if available, otherwise calculate from start/end times
            $durationInHours = $duration ? ($duration / 60) : $this->calculateRoundedDuration($startsAt, $endsAt);

            $timeIntervals = [
                15 => 0.25,
                30 => 0.5,
                45 => 0.75,
                60 => 1,
            ];

            foreach ($timeIntervals as $interval => $percentage) {
                if ($durationInHours >= ($interval / 60) && $durationInHours < (($interval + 15) / 60)) {
                    return $chargePerHour * $percentage;
                }
            }
        }

        Log::error('Error in calculateCourseCost: Tutor profile or price is missing.');
        return 0;
    }

    private function calculateRefundAmount(Lesson $lesson)
    {
        $originalCost = $this->calculateCourseCost($lesson->tutor->tutor, $lesson->starts_at, $lesson->ends_at);
        return $originalCost * 0.5; // 50% refund
    }
}
