<?php

namespace App\Services;

use App\Events\MessageSent;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Models\Lesson;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Event;
use Illuminate\Pagination\LengthAwarePaginator;

class ChatService
{
    protected CacheService $cacheService;

    public function __construct(CacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Start or get conversation between two users
     */
    public function getOrCreateConversation(int $userId1, int $userId2, string $type = 'general', int $lessonId = null): Conversation
    {
        // Ensure consistent ordering (student_id, tutor_id)
        $student = User::find($userId1);
        $tutor = User::find($userId2);

        // Determine who is student and who is tutor
        if ($student->hasRole('student') && $tutor->hasRole('tutor')) {
            $studentId = $userId1;
            $tutorId = $userId2;
        } elseif ($student->hasRole('tutor') && $tutor->hasRole('student')) {
            $studentId = $userId2;
            $tutorId = $userId1;
        } else {
            // If both have same role, use ID ordering for consistency
            $studentId = min($userId1, $userId2);
            $tutorId = max($userId1, $userId2);
        }

        $conversation = Conversation::findOrCreateBetween($studentId, $tutorId, $type, $lessonId);

        // Clear cache for both users
        $this->invalidateUserConversationCache($studentId);
        $this->invalidateUserConversationCache($tutorId);

        return $conversation;
    }

    /**
     * Send a text message
     */
    public function sendMessage(int $conversationId, int $senderId, string $content, int $replyToMessageId = null): Message
    {
        try {
            return DB::transaction(function() use ($conversationId, $senderId, $content, $replyToMessageId) {
                $conversation = Conversation::findOrFail($conversationId);

                // Verify sender is participant
                if (!$conversation->isParticipant($senderId)) {
                    throw new \Exception('User is not a participant in this conversation');
                }

                // Create message
                $message = Message::create([
                    'conversation_id' => $conversationId,
                    'sender_id' => $senderId,
                    'content' => $content,
                    'type' => 'text',
                    'status' => 'sent',
                    'reply_to_message_id' => $replyToMessageId
                ]);

                // Update conversation
                $conversation->updateOnNewMessage($message);

                // Broadcast real-time message
                Event::dispatch(new MessageSent($message));

                // Clear cache
                $this->invalidateConversationCache($conversationId);
                $this->invalidateUserConversationCache($conversation->student_id);
                $this->invalidateUserConversationCache($conversation->tutor_id);

                Log::info('Message sent', [
                    'message_id' => $message->id,
                    'conversation_id' => $conversationId,
                    'sender_id' => $senderId
                ]);

                return $message->load(['sender', 'replyToMessage']);
            });
        } catch (\Exception $e) {
            Log::error('Failed to send message', [
                'conversation_id' => $conversationId,
                'sender_id' => $senderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send a message with file attachment
     */
    public function sendMessageWithFile(int $conversationId, int $senderId, string $content, UploadedFile $file): Message
    {
        try {
            return DB::transaction(function() use ($conversationId, $senderId, $content, $file) {
                $conversation = Conversation::findOrFail($conversationId);

                // Verify sender is participant
                if (!$conversation->isParticipant($senderId)) {
                    throw new \Exception('User is not a participant in this conversation');
                }

                // Store file
                $filePath = $file->store('chat-attachments', 'public');
                $fileType = $file->getMimeType();

                // Determine message type based on file
                $messageType = str_starts_with($fileType, 'image/') ? 'image' : 'file';

                // Create message
                $message = Message::create([
                    'conversation_id' => $conversationId,
                    'sender_id' => $senderId,
                    'content' => $content ?: $file->getClientOriginalName(),
                    'type' => $messageType,
                    'file_path' => $filePath,
                    'file_name' => $file->getClientOriginalName(),
                    'file_type' => $fileType,
                    'file_size' => $file->getSize(),
                    'status' => 'sent'
                ]);

                // Update conversation
                $conversation->updateOnNewMessage($message);

                // Broadcast real-time message
                Event::dispatch(new MessageSent($message));

                // Clear cache
                $this->invalidateConversationCache($conversationId);
                $this->invalidateUserConversationCache($conversation->student_id);
                $this->invalidateUserConversationCache($conversation->tutor_id);

                Log::info('Message with file sent', [
                    'message_id' => $message->id,
                    'conversation_id' => $conversationId,
                    'sender_id' => $senderId,
                    'file_type' => $fileType
                ]);

                return $message->load(['sender']);
            });
        } catch (\Exception $e) {
            Log::error('Failed to send message with file', [
                'conversation_id' => $conversationId,
                'sender_id' => $senderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Send a lesson link message
     */
    public function sendLessonLink(int $conversationId, int $senderId, Lesson $lesson): Message
    {
        $content = "📅 Lesson scheduled: " . $lesson->starts_at->format('M d, Y @ H:i');

        $metadata = [
            'lesson_id' => $lesson->id,
            'lesson_title' => $lesson->course->name ?? 'Lesson',
            'lesson_start' => $lesson->starts_at->toISOString(),
            'lesson_end' => $lesson->ends_at?->toISOString(),
            'join_url' => "/lessons/{$lesson->id}/join"
        ];

        try {
            return DB::transaction(function() use ($conversationId, $senderId, $content, $metadata) {
                $conversation = Conversation::findOrFail($conversationId);

                $message = Message::create([
                    'conversation_id' => $conversationId,
                    'sender_id' => $senderId,
                    'content' => $content,
                    'type' => 'lesson_link',
                    'metadata' => $metadata,
                    'status' => 'sent'
                ]);

                $conversation->updateOnNewMessage($message);
                Event::dispatch(new MessageSent($message));

                return $message->load(['sender']);
            });
        } catch (\Exception $e) {
            Log::error('Failed to send lesson link', [
                'conversation_id' => $conversationId,
                'lesson_id' => $lesson->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get conversation messages with pagination
     */
    public function getConversationMessages(int $conversationId, int $userId, int $page = 1, int $perPage = 50): LengthAwarePaginator
    {
        $conversation = Conversation::findOrFail($conversationId);

        // Verify user is participant
        if (!$conversation->isParticipant($userId)) {
            throw new \Exception('User is not a participant in this conversation');
        }

        // Get messages with pagination
        $messages = Message::with(['sender', 'replyToMessage.sender'])
            ->where('conversation_id', $conversationId)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return $messages;
    }

    /**
     * Get user's conversations list
     */
    public function getUserConversations(int $userId, int $page = 1, int $perPage = 20): LengthAwarePaginator
    {
        // Try cache first
        $cacheKey = "user_conversations_{$userId}_page_{$page}";
        $conversations = $this->cacheService->remember($cacheKey, CacheService::SHORT_TTL, function() use ($userId, $page, $perPage) {
            return Conversation::forUser($userId)
                ->active()
                ->with(['student', 'tutor', 'lesson'])
                ->withCount(['messages'])
                ->orderBy('last_message_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);
        });

        return $conversations;
    }

    /**
     * Mark conversation as read for user
     */
    public function markConversationAsRead(int $conversationId, int $userId): bool
    {
        try {
            $conversation = Conversation::findOrFail($conversationId);

            if (!$conversation->isParticipant($userId)) {
                return false;
            }

            $result = $conversation->markAsReadForUser($userId);

            if ($result) {
                // Mark unread messages as read
                Message::where('conversation_id', $conversationId)
                    ->where('sender_id', '!=', $userId)
                    ->where('status', '!=', 'read')
                    ->update([
                        'status' => 'read',
                        'read_at' => now()
                    ]);

                // Clear cache
                $this->invalidateConversationCache($conversationId);
                $this->invalidateUserConversationCache($userId);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to mark conversation as read', [
                'conversation_id' => $conversationId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get unread message count for user
     */
    public function getUnreadMessageCount(int $userId): int
    {
        // Try cache first
        $cacheKey = "user_unread_count_{$userId}";
        return $this->cacheService->remember($cacheKey, CacheService::SHORT_TTL, function() use ($userId) {
            return Conversation::forUser($userId)
                ->active()
                ->sum(function($conversation) use ($userId) {
                    return $conversation->getUnreadCountForUser($userId);
                });
        });
    }

    /**
     * Search messages in conversation
     */
    public function searchMessages(int $conversationId, int $userId, string $query, int $page = 1): LengthAwarePaginator
    {
        $conversation = Conversation::findOrFail($conversationId);

        if (!$conversation->isParticipant($userId)) {
            throw new \Exception('User is not a participant in this conversation');
        }

        return Message::with(['sender'])
            ->where('conversation_id', $conversationId)
            ->where('content', 'LIKE', "%{$query}%")
            ->orderBy('created_at', 'desc')
            ->paginate(20, ['*'], 'page', $page);
    }

    /**
     * Delete a message (soft delete)
     */
    public function deleteMessage(int $messageId, int $userId): bool
    {
        try {
            $message = Message::findOrFail($messageId);
            $conversation = $message->conversation;

            // Only sender or admin can delete
            if ($message->sender_id !== $userId) {
                return false;
            }

            $message->delete();

            // If this was the last message, update conversation
            if ($conversation->last_message_by === $message->sender_id) {
                $lastMessage = Message::where('conversation_id', $conversation->id)
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($lastMessage) {
                    $conversation->update([
                        'last_message_at' => $lastMessage->created_at,
                        'last_message_by' => $lastMessage->sender_id
                    ]);
                }
            }

            // Clear cache
            $this->invalidateConversationCache($conversation->id);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to delete message', [
                'message_id' => $messageId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Cache invalidation methods
     */
    private function invalidateConversationCache(int $conversationId): void
    {
        $this->cacheService->invalidateByPattern("conversation_{$conversationId}_*");
    }

    private function invalidateUserConversationCache(int $userId): void
    {
        $this->cacheService->invalidateByPattern("user_conversations_{$userId}_*");
        $this->cacheService->invalidateByPattern("user_unread_count_{$userId}");
    }
}
