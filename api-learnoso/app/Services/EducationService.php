<?php

namespace App\Services;

use \App\Repositories\Contracts\EducationRepositoryInterface;
use App\Repositories\Eloquent\TutorRepository;
use App\Services\UploadService;
use Illuminate\Http\UploadedFile;

class EducationService
{
    protected $educationRepository;
    protected $uploadService;

    protected $tutorRepository;

    protected $tutorService;

    public function __construct(
        EducationRepositoryInterface $educationRepository,
        UploadService $uploadService,
        TutorRepository $tutorRepository,
        TutorService $tutorService
    ) {
        $this->educationRepository = $educationRepository;
        $this->uploadService = $uploadService;
        $this->tutorRepository = $tutorRepository;
        $this->tutorService = $tutorService;
    }

    /**
     * Stores multiple education records with optional certificate file uploads.
     *
     * @param array $educationData
     */
    public function storeEducation(array $educationData)
    {
        // Check if the user is a tutor
        $tutor = $this->tutorRepository->findByUserId($educationData['user_id']);

        // Check if there's a certificate file to upload
        if (isset($educationData['attachment']) && $educationData['attachment'] instanceof UploadedFile) {
            // Upload the file and get the URL
            $educationData['url'] = $this->uploadService->uploadFile($educationData['attachment'], 'certificates');
        }

        // Save the education record using the repository
        $this->educationRepository->create($educationData);

        $this->tutorService->setOnboardingStep($tutor->id, 3);
        $tutor->onboardingStatus->refresh();

        return $tutor->load('educations');

    }

}
