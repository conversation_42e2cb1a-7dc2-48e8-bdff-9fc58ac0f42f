<?php

namespace App\Services;

use App\Mail\CustomEmailVerification;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Console\Scheduling\Event;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Spatie\Permission\Models\Role;
use App\Services\AccountLockoutService;
use App\Services\TwoFactorAuthService;
use App\Services\AuditLogService;

class UserService
{
    protected $userRepository;
    protected $accountLockoutService;
    protected $twoFactorService;
    protected $auditService;

    public function __construct(
        UserRepositoryInterface $userRepository,
        AccountLockoutService $accountLockoutService,
        TwoFactorAuthService $twoFactorService,
        AuditLogService $auditService
    ) {
        $this->userRepository = $userRepository;
        $this->accountLockoutService = $accountLockoutService;
        $this->twoFactorService = $twoFactorService;
        $this->auditService = $auditService;
    }

    public function find($id)
    {
        return $this->userRepository->find($id);
    }

    public function registerUser(array $data)
    {
        $data['password'] = Hash::make($data['password']);
        $user = $this->userRepository->create($data);

        // Send verification email
        Mail::to($user->email)->send(new CustomEmailVerification($user));

        // Log user registration
        $this->auditService->logAuth('register', $user, 'success', [
            'email' => $user->email,
            'country' => $user->country
        ]);

        return $user;
    }

    public function assignRole($user, $roleName)
    {
        $role = Role::firstOrCreate(
            ['name' => $roleName],
            ['guard_name' => 'web']
        );
        $user->assignRole($role);
    }

    public function attemptLogin(array $credentials, string $ipAddress = '127.0.0.1', string $twoFactorCode = null)
    {
        $email = $credentials['email'];

        // Check if account can attempt login (not locked)
        $canLogin = $this->accountLockoutService->canAttemptLogin($email);

        if (!$canLogin['can_login']) {
            return [
                'success' => false,
                'user' => null,
                'lockout' => true,
                'message' => $canLogin['message'],
                'remaining_time' => $canLogin['remaining_time'] ?? 0
            ];
        }

        $user = $this->userRepository->findByEmail($email);

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            // Handle failed login attempt
            $failedResult = $this->accountLockoutService->handleFailedLogin($email, $ipAddress);

            return [
                'success' => false,
                'user' => null,
                'lockout' => $failedResult['lockout'],
                'message' => $failedResult['message'],
                'remaining_attempts' => $failedResult['remaining_attempts'] ?? 0,
                'remaining_time' => $failedResult['remaining_time'] ?? 0
            ];
        }

        // Check if 2FA is required
        if ($user->hasTwoFactorEnabled()) {
            if (!$twoFactorCode) {
                return [
                    'success' => false,
                    'user' => $user,
                    'requires_2fa' => true,
                    'message' => 'Two-factor authentication code required'
                ];
            }

            // Verify 2FA code
            if (!$this->twoFactorService->verifyCode($user, $twoFactorCode)) {
                // Don't count 2FA failures as regular failed attempts
                return [
                    'success' => false,
                    'user' => $user,
                    'requires_2fa' => true,
                    'invalid_2fa' => true,
                    'message' => 'Invalid two-factor authentication code'
                ];
            }
        }

        // Handle successful login
        $this->accountLockoutService->handleSuccessfulLogin($user, $ipAddress);

        return [
            'success' => true,
            'user' => $user->load('onboardingStatus'),
            'lockout' => false,
            'requires_2fa' => false,
            'message' => 'Login successful'
        ];
    }

    public function generateToken($user)
    {
        return $user->createToken('authToken')->plainTextToken;
    }

    public function resendEmailVerificationLink(string $email)
    {
        $user = $this->userRepository->findByEmail($email);

        if (!$user) {
            throw new \Exception('User not found');
        }

        if ($user->hasVerifiedEmail()) {
            throw new \Exception('Email already verified');
        }

        $user->sendEmailVerificationNotification();
    }

    public function all($filters = [])
    {
        return $this->userRepository->all($filters);
    }

}


