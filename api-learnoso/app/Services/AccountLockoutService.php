<?php

namespace App\Services;

use App\Models\User;
use App\Events\AccountLocked;
use App\Events\AccountUnlocked;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Carbon\Carbon;

class AccountLockoutService
{
    protected const CACHE_PREFIX = 'account_lockout:';

    // Configurable lockout rules
    protected array $lockoutRules = [
        'max_attempts' => 5,
        'lockout_duration_minutes' => 15,
        'progressive_lockout' => true,
        'reset_attempts_after_hours' => 24,
        'admin_notification_threshold' => 10
    ];

    public function __construct()
    {
        // Load configuration from system settings
        $this->lockoutRules = array_merge($this->lockoutRules, config('auth.lockout', []));
    }

    /**
     * Handle failed login attempt
     */
    public function handleFailedLogin(string $email, string $ipAddress): array
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            // Still track IP-based attempts even for non-existent users
            $this->trackIpFailedAttempt($ipAddress);
            return [
                'success' => false,
                'message' => 'Invalid credentials',
                'lockout' => false
            ];
        }

        // Check if account is already locked
        if ($user->isAccountLocked()) {
            Log::warning('Login attempt on locked account', [
                'user_id' => $user->id,
                'email' => $email,
                'ip' => $ipAddress,
                'remaining_time' => $user->getRemainingLockoutTime()
            ]);

            return [
                'success' => false,
                'message' => 'Account is temporarily locked due to multiple failed login attempts',
                'lockout' => true,
                'remaining_time' => $user->getRemainingLockoutTime()
            ];
        }

        // Record the failed attempt
        $user->recordFailedLoginAttempt($ipAddress);

        // Check if we should lock the account
        if ($user->hasExceededMaxFailedAttempts($this->lockoutRules['max_attempts'])) {
            $lockoutDuration = $this->calculateLockoutDuration($user);
            $user->lockAccount($lockoutDuration, 'Multiple failed login attempts');

            // Dispatch account locked event
            Event::dispatch(new AccountLocked($user, $lockoutDuration));

            Log::warning('Account locked due to failed login attempts', [
                'user_id' => $user->id,
                'email' => $email,
                'ip' => $ipAddress,
                'failed_attempts' => $user->failed_login_attempts,
                'lockout_duration' => $lockoutDuration
            ]);

            return [
                'success' => false,
                'message' => "Account locked for {$lockoutDuration} minutes due to multiple failed login attempts",
                'lockout' => true,
                'remaining_time' => $lockoutDuration
            ];
        }

        $remainingAttempts = $this->lockoutRules['max_attempts'] - $user->failed_login_attempts;

        Log::info('Failed login attempt recorded', [
            'user_id' => $user->id,
            'email' => $email,
            'ip' => $ipAddress,
            'failed_attempts' => $user->failed_login_attempts,
            'remaining_attempts' => $remainingAttempts
        ]);

        return [
            'success' => false,
            'message' => "Invalid credentials. {$remainingAttempts} attempts remaining before account lockout",
            'lockout' => false,
            'remaining_attempts' => $remainingAttempts
        ];
    }

    /**
     * Handle successful login
     */
    public function handleSuccessfulLogin(User $user, string $ipAddress): void
    {
        $wasLocked = $user->isAccountLocked();

        // Reset failed attempts and unlock account
        $user->recordSuccessfulLogin($ipAddress);

        if ($wasLocked) {
            Event::dispatch(new AccountUnlocked($user));

            Log::info('Account unlocked after successful login', [
                'user_id' => $user->id,
                'email' => $user->email,
                'ip' => $ipAddress
            ]);
        }

        Log::info('Successful login recorded', [
            'user_id' => $user->id,
            'email' => $user->email,
            'ip' => $ipAddress
        ]);
    }

    /**
     * Check if user can attempt login
     */
    public function canAttemptLogin(string $email): array
    {
        $user = User::where('email', $email)->first();

        if (!$user) {
            return ['can_login' => true, 'message' => null];
        }

        if ($user->isAccountLocked()) {
            $remainingTime = $user->getRemainingLockoutTime();

            return [
                'can_login' => false,
                'message' => "Account is locked for {$remainingTime} minutes",
                'remaining_time' => $remainingTime,
                'lockout_reason' => $user->lockout_reason
            ];
        }

        return ['can_login' => true, 'message' => null];
    }

    /**
     * Manually unlock account (admin action)
     */
    public function unlockAccount(User $user, string $adminReason = 'Manually unlocked by administrator'): bool
    {
        try {
            $user->resetFailedLoginAttempts();

            Event::dispatch(new AccountUnlocked($user));

            Log::info('Account manually unlocked', [
                'user_id' => $user->id,
                'email' => $user->email,
                'reason' => $adminReason
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to unlock account', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get lockout statistics for admin dashboard
     */
    public function getLockoutStatistics(): array
    {
        $lockedUsers = User::where('account_locked_until', '>', now())->count();
        $usersWithFailedAttempts = User::where('failed_login_attempts', '>', 0)->count();
        $recentLockouts = User::where('account_locked_until', '>', now()->subDay())->count();

        return [
            'currently_locked' => $lockedUsers,
            'users_with_failed_attempts' => $usersWithFailedAttempts,
            'recent_lockouts_24h' => $recentLockouts,
            'lockout_rules' => $this->lockoutRules
        ];
    }

    /**
     * Calculate progressive lockout duration
     */
    private function calculateLockoutDuration(User $user): int
    {
        if (!$this->lockoutRules['progressive_lockout']) {
            return $this->lockoutRules['lockout_duration_minutes'];
        }

        // Progressive lockout: increase duration based on previous lockouts
        $baseDuration = $this->lockoutRules['lockout_duration_minutes'];
        $recentLockouts = $this->getRecentLockoutCount($user);

        // Double the duration for each recent lockout (max 8 hours)
        $multiplier = min(pow(2, $recentLockouts), 32);

        return min($baseDuration * $multiplier, 480); // Max 8 hours
    }

    /**
     * Get count of recent lockouts for progressive penalty
     */
    private function getRecentLockoutCount(User $user): int
    {
        $cacheKey = self::CACHE_PREFIX . 'recent_lockouts:' . $user->id;

        $recentLockouts = Cache::get($cacheKey, 0);

        // Increment and store for 24 hours
        Cache::put($cacheKey, $recentLockouts + 1, now()->addHours(24));

        return $recentLockouts;
    }

    /**
     * Track IP-based failed attempts (for non-existent users)
     */
    private function trackIpFailedAttempt(string $ipAddress): void
    {
        $cacheKey = self::CACHE_PREFIX . 'ip:' . $ipAddress;
        $attempts = Cache::get($cacheKey, 0) + 1;

        Cache::put($cacheKey, $attempts, now()->addHours(1));

        // Log suspicious activity from same IP
        if ($attempts > 10) {
            Log::warning('Suspicious login activity from IP', [
                'ip' => $ipAddress,
                'attempts' => $attempts
            ]);
        }
    }

    /**
     * Cleanup expired lockouts (for scheduled cleanup)
     */
    public function cleanupExpiredLockouts(): int
    {
        $cleaned = User::where('account_locked_until', '<', now())
            ->where('account_locked_until', '!=', null)
            ->update([
                'account_locked_until' => null,
                'lockout_reason' => null
            ]);

        if ($cleaned > 0) {
            Log::info('Cleaned up expired lockouts', ['count' => $cleaned]);
        }

        return $cleaned;
    }
}
