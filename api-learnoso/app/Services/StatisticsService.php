<?php

namespace App\Services;

use App\Repositories\Contracts\WalletRepositoryInterface;
use Bavix\Wallet\Models\Transaction;
use Carbon\Carbon;

class StatisticsService
{
    protected $walletRepository;

    public function __construct(WalletRepositoryInterface $walletRepository)
    {
        $this->walletRepository = $walletRepository;
    }

    public static function getTotalRevenue(): float
    {
        return Transaction::where('type', 'deposit')->sum('amount');
    }

    public function getFinancialSummary(): array
    {
        return [
            'total_revenue' => abs(self::getTotalRevenue()),
            'total_expenses' => abs(Transaction::where('type', 'withdraw')->sum('amount')),
            'total_transfers' => Transaction::where('type', 'transfer')->sum('amount'),
            'current_system_balance' => getSystemUser()->wallet->balance,
        ];
    }


    public function getMonthlyTransactions(array $filters = []): array
    {
        $query = Transaction::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(amount) as total')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc');

        // Apply filters
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->get()->toArray();
    }

    public function getDailyTransactions(array $filters = []): array
    {
        $query = Transaction::selectRaw('DATE(created_at) as date, SUM(amount) as total')
            ->groupBy('date')
            ->orderBy('date', 'desc');

        // Apply filters
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->get()->toArray();
    }

    public function getMonthlyProfits(array $filters = []): array
    {
        $query = Transaction::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month,
            SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN type = "withdraw" THEN amount ELSE 0 END) as total_expense,
            (SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) -
            SUM(CASE WHEN type = "withdraw" THEN amount ELSE 0 END)) as profit')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc');

        // Apply filters
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->get()->toArray();
    }

    public function getChartData(array $filters = []): array
    {
        $query = Transaction::selectRaw('YEAR(created_at) as year, MONTH(created_at) as month,
            SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) as total_money,
            (SUM(CASE WHEN type = "deposit" THEN amount ELSE 0 END) -
            SUM(CASE WHEN type = "withdraw" THEN amount ELSE 0 END)) as profit')
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc');

        // Apply filters
        if (!empty($filters['start_date']) && !empty($filters['end_date'])) {
            $query->whereBetween('created_at', [$filters['start_date'], $filters['end_date']]);
        }

        if (!empty($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        return $query->get()->map(function ($item) {
            return [
                'month' => Carbon::create($item['year'], $item['month'])->format('M'),
                'total' => (float) $item['total_money'],
                'profit' => (float) $item['profit'],
            ];
        })->toArray();
    }

    public function getAdditionalStats(): array
    {
        return [
            'total_lessons_booked' => Transaction::where('meta->category', 'Lesson payment')->count(),
            'total_wallet_topups' => Transaction::where('type', 'deposit')->count(),
            'total_internal_transfers' => Transaction::where('type', 'transfer')->count(),
        ];
    }
}
