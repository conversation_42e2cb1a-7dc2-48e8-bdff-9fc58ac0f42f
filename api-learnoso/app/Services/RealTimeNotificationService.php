<?php

namespace App\Services;

use App\Events\LessonStatusUpdated;
use App\Events\NotificationReceived;
use App\Models\User;
use App\Models\Lesson;
use App\Models\LessonTracker;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Event;

class RealTimeNotificationService
{
    /**
     * Send a real-time lesson status update
     */
    public function broadcastLessonStatusUpdate(
        Lesson $lesson,
        LessonTracker $tracker = null,
        string $statusType = 'general',
        array $additionalData = []
    ): bool {
        try {
            Event::dispatch(new LessonStatusUpdated($lesson, $tracker, $statusType, $additionalData));

            Log::info('Lesson status update broadcasted', [
                'lesson_id' => $lesson->id,
                'status_type' => $statusType,
                'lesson_status' => $lesson->status,
                'tracker_status' => $tracker?->status
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast lesson status update', [
                'lesson_id' => $lesson->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send a real-time notification to a user
     */
    public function sendNotificationToUser(User $user, array $notification): bool
    {
        try {
            // Add default notification structure if missing
            $notification = array_merge([
                'id' => uniqid(),
                'type' => 'info',
                'title' => 'Notification',
                'message' => '',
                'created_at' => now()->toISOString(),
                'read' => false
            ], $notification);

            Event::dispatch(new NotificationReceived($user, $notification));

            Log::info('Real-time notification sent', [
                'user_id' => $user->id,
                'notification_type' => $notification['type'],
                'notification_title' => $notification['title']
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send real-time notification', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Broadcast lesson session started
     */
    public function broadcastLessonSessionStarted(Lesson $lesson, LessonTracker $tracker): bool
    {
        return $this->broadcastLessonStatusUpdate(
            $lesson,
            $tracker,
            'session_started',
            [
                'message' => 'Lesson session has started',
                'action' => 'session_started'
            ]
        );
    }

    /**
     * Broadcast lesson session ended
     */
    public function broadcastLessonSessionEnded(Lesson $lesson, LessonTracker $tracker): bool
    {
        return $this->broadcastLessonStatusUpdate(
            $lesson,
            $tracker,
            'session_ended',
            [
                'message' => 'Lesson session has ended',
                'action' => 'session_ended',
                'session_summary' => [
                    'total_duration' => $tracker->getFormattedTotalDuration(),
                    'active_duration' => $tracker->getFormattedActiveDuration(),
                    'break_count' => $tracker->break_count,
                    'billable_amount' => $tracker->billable_amount
                ]
            ]
        );
    }

    /**
     * Broadcast lesson break started
     */
    public function broadcastLessonBreakStarted(Lesson $lesson, LessonTracker $tracker): bool
    {
        return $this->broadcastLessonStatusUpdate(
            $lesson,
            $tracker,
            'break_started',
            [
                'message' => 'Lesson break has started',
                'action' => 'break_started'
            ]
        );
    }

    /**
     * Broadcast lesson break ended
     */
    public function broadcastLessonBreakEnded(Lesson $lesson, LessonTracker $tracker): bool
    {
        return $this->broadcastLessonStatusUpdate(
            $lesson,
            $tracker,
            'break_ended',
            [
                'message' => 'Lesson break has ended',
                'action' => 'break_ended'
            ]
        );
    }

    /**
     * Send lesson reminder notification
     */
    public function sendLessonReminder(Lesson $lesson, int $minutesUntilStart = 15): bool
    {
        $student = $lesson->student;
        $tutor = $lesson->tutor;

        $notification = [
            'type' => 'lesson_reminder',
            'title' => 'Lesson Reminder',
            'message' => "Your lesson starts in {$minutesUntilStart} minutes",
            'data' => [
                'lesson_id' => $lesson->id,
                'starts_at' => $lesson->starts_at->toISOString(),
                'minutes_until_start' => $minutesUntilStart,
                'other_participant' => [
                    'student_view' => $tutor->first_name . ' ' . $tutor->last_name,
                    'tutor_view' => $student->first_name . ' ' . $student->last_name,
                ]
            ],
            'actions' => [
                [
                    'label' => 'Join Lesson',
                    'action' => 'join_lesson',
                    'url' => "/lessons/{$lesson->id}/join"
                ]
            ]
        ];

        // Send to both student and tutor
        $studentResult = $this->sendNotificationToUser($student, array_merge($notification, [
            'message' => "Your lesson with {$tutor->first_name} starts in {$minutesUntilStart} minutes"
        ]));

        $tutorResult = $this->sendNotificationToUser($tutor, array_merge($notification, [
            'message' => "Your lesson with {$student->first_name} starts in {$minutesUntilStart} minutes"
        ]));

        return $studentResult && $tutorResult;
    }

    /**
     * Send lesson cancelled notification
     */
    public function sendLessonCancelledNotification(Lesson $lesson, string $reason = ''): bool
    {
        $student = $lesson->student;
        $tutor = $lesson->tutor;

        $notification = [
            'type' => 'lesson_cancelled',
            'title' => 'Lesson Cancelled',
            'data' => [
                'lesson_id' => $lesson->id,
                'reason' => $reason,
                'original_start_time' => $lesson->starts_at->toISOString(),
            ]
        ];

        // Send to both participants
        $studentResult = $this->sendNotificationToUser($student, array_merge($notification, [
            'message' => "Your lesson with {$tutor->first_name} has been cancelled" . ($reason ? ": {$reason}" : '')
        ]));

        $tutorResult = $this->sendNotificationToUser($tutor, array_merge($notification, [
            'message' => "Your lesson with {$student->first_name} has been cancelled" . ($reason ? ": {$reason}" : '')
        ]));

        return $studentResult && $tutorResult;
    }

    /**
     * Send payment received notification
     */
    public function sendPaymentReceivedNotification(User $user, float $amount, string $description = ''): bool
    {
        $notification = [
            'type' => 'payment_received',
            'title' => 'Payment Received',
            'message' => "You received a payment of ${$amount}" . ($description ? " for {$description}" : ''),
            'data' => [
                'amount' => $amount,
                'description' => $description,
                'currency' => 'USD' // TODO: Make this configurable
            ],
            'actions' => [
                [
                    'label' => 'View Wallet',
                    'action' => 'view_wallet',
                    'url' => '/wallet'
                ]
            ]
        ];

        return $this->sendNotificationToUser($user, $notification);
    }

    /**
     * Send system maintenance notification
     */
    public function broadcastSystemMaintenance(string $message, \DateTime $scheduledTime = null): bool
    {
        $notification = [
            'type' => 'system_maintenance',
            'title' => 'System Maintenance',
            'message' => $message,
            'data' => [
                'scheduled_time' => $scheduledTime?->format('c'),
                'maintenance_type' => 'planned'
            ]
        ];

        try {
            // Get all active users (you might want to implement this differently)
            $activeUsers = User::where('updated_at', '>=', now()->subHours(24))->get();

            foreach ($activeUsers as $user) {
                $this->sendNotificationToUser($user, $notification);
            }

            Log::info('System maintenance notification broadcasted', [
                'message' => $message,
                'user_count' => $activeUsers->count()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to broadcast system maintenance', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get notification count for user
     */
    public function getUnreadNotificationCount(User $user): int
    {
        // This would typically query a notifications table
        // For now, return 0 as placeholder
        return 0;
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead(User $user, string $notificationId): bool
    {
        // Implement notification read status
        // This would typically update a notifications table

        Log::info('Notification marked as read', [
            'user_id' => $user->id,
            'notification_id' => $notificationId
        ]);

        return true;
    }
}
