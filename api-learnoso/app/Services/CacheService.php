<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CacheService
{
    // Cache TTL constants (in seconds)
    const SHORT_TTL = 300;     // 5 minutes
    const MEDIUM_TTL = 1800;   // 30 minutes
    const LONG_TTL = 3600;     // 1 hour
    const DAILY_TTL = 86400;   // 24 hours

    // Cache key prefixes
    const LESSON_TRACKER_PREFIX = 'lesson_tracker:';
    const USER_SESSIONS_PREFIX = 'user_sessions:';
    const TUTOR_STATS_PREFIX = 'tutor_stats:';
    const API_RESPONSE_PREFIX = 'api_response:';
    const COURSE_DATA_PREFIX = 'course_data:';
    const USER_PROFILE_PREFIX = 'user_profile:';

    /**
     * Cache lesson tracker data
     */
    public function cacheLessonTracker(int $lessonId, array $data, int $ttl = self::SHORT_TTL): bool
    {
        try {
            $key = self::LESSON_TRACKER_PREFIX . $lessonId;
            return Cache::put($key, $data, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache lesson tracker data', [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached lesson tracker data
     */
    public function getLessonTracker(int $lessonId): ?array
    {
        try {
            $key = self::LESSON_TRACKER_PREFIX . $lessonId;
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached lesson tracker data', [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Invalidate lesson tracker cache
     */
    public function invalidateLessonTracker(int $lessonId): bool
    {
        try {
            $key = self::LESSON_TRACKER_PREFIX . $lessonId;
            return Cache::forget($key);
        } catch (\Exception $e) {
            Log::error('Failed to invalidate lesson tracker cache', [
                'lesson_id' => $lessonId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Cache user active sessions
     */
    public function cacheUserSessions(int $userId, array $sessions, int $ttl = self::SHORT_TTL): bool
    {
        try {
            $key = self::USER_SESSIONS_PREFIX . $userId;
            return Cache::put($key, $sessions, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache user sessions', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached user sessions
     */
    public function getUserSessions(int $userId): ?array
    {
        try {
            $key = self::USER_SESSIONS_PREFIX . $userId;
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached user sessions', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache tutor statistics
     */
    public function cacheTutorStats(int $tutorId, array $stats, int $ttl = self::MEDIUM_TTL): bool
    {
        try {
            $key = self::TUTOR_STATS_PREFIX . $tutorId;
            return Cache::put($key, $stats, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache tutor stats', [
                'tutor_id' => $tutorId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached tutor statistics
     */
    public function getTutorStats(int $tutorId): ?array
    {
        try {
            $key = self::TUTOR_STATS_PREFIX . $tutorId;
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached tutor stats', [
                'tutor_id' => $tutorId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache API response
     */
    public function cacheApiResponse(string $endpoint, array $params, array $response, int $ttl = self::MEDIUM_TTL): bool
    {
        try {
            $key = self::API_RESPONSE_PREFIX . md5($endpoint . serialize($params));
            return Cache::put($key, $response, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache API response', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached API response
     */
    public function getApiResponse(string $endpoint, array $params = []): ?array
    {
        try {
            $key = self::API_RESPONSE_PREFIX . md5($endpoint . serialize($params));
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached API response', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache course data
     */
    public function cacheCourseData(int $courseId, array $data, int $ttl = self::LONG_TTL): bool
    {
        try {
            $key = self::COURSE_DATA_PREFIX . $courseId;
            return Cache::put($key, $data, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache course data', [
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached course data
     */
    public function getCourseData(int $courseId): ?array
    {
        try {
            $key = self::COURSE_DATA_PREFIX . $courseId;
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached course data', [
                'course_id' => $courseId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Cache user profile data
     */
    public function cacheUserProfile(int $userId, array $profile, int $ttl = self::MEDIUM_TTL): bool
    {
        try {
            $key = self::USER_PROFILE_PREFIX . $userId;
            return Cache::put($key, $profile, $ttl);
        } catch (\Exception $e) {
            Log::error('Failed to cache user profile', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cached user profile
     */
    public function getUserProfile(int $userId): ?array
    {
        try {
            $key = self::USER_PROFILE_PREFIX . $userId;
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error('Failed to get cached user profile', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Invalidate multiple cache keys by pattern
     */
    public function invalidateByPattern(string $pattern): int
    {
        try {
            $keys = Redis::keys($pattern);
            if (empty($keys)) {
                return 0;
            }

            $deleted = 0;
            foreach ($keys as $key) {
                if (Cache::forget($key)) {
                    $deleted++;
                }
            }

            Log::info('Cache invalidated by pattern', [
                'pattern' => $pattern,
                'deleted_count' => $deleted
            ]);

            return $deleted;
        } catch (\Exception $e) {
            Log::error('Failed to invalidate cache by pattern', [
                'pattern' => $pattern,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Invalidate all user-related cache
     */
    public function invalidateUserCache(int $userId): bool
    {
        try {
            $patterns = [
                self::USER_SESSIONS_PREFIX . $userId,
                self::USER_PROFILE_PREFIX . $userId,
                self::TUTOR_STATS_PREFIX . $userId,
            ];

            $totalDeleted = 0;
            foreach ($patterns as $pattern) {
                if (Cache::forget($pattern)) {
                    $totalDeleted++;
                }
            }

            Log::info('User cache invalidated', [
                'user_id' => $userId,
                'deleted_count' => $totalDeleted
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to invalidate user cache', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        try {
            $redis = Redis::connection();
            $info = $redis->info();

            return [
                'redis_version' => $info['redis_version'] ?? 'unknown',
                'used_memory' => $info['used_memory_human'] ?? 'unknown',
                'connected_clients' => $info['connected_clients'] ?? 0,
                'total_commands_processed' => $info['total_commands_processed'] ?? 0,
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate($info),
                'uptime_in_seconds' => $info['uptime_in_seconds'] ?? 0
            ];
        } catch (\Exception $e) {
            Log::error('Failed to get cache stats', [
                'error' => $e->getMessage()
            ]);
            return ['error' => 'Failed to retrieve cache statistics'];
        }
    }

    /**
     * Calculate cache hit rate
     */
    private function calculateHitRate(array $info): string
    {
        $hits = (int)($info['keyspace_hits'] ?? 0);
        $misses = (int)($info['keyspace_misses'] ?? 0);
        $total = $hits + $misses;

        if ($total === 0) {
            return '0%';
        }

        $hitRate = ($hits / $total) * 100;
        return number_format($hitRate, 2) . '%';
    }

    /**
     * Warm up cache with frequently accessed data
     */
    public function warmUpCache(): array
    {
        $warmedUp = [];

        try {
            // Warm up system settings
            $this->warmUpSystemSettings();
            $warmedUp[] = 'system_settings';

            // Warm up active courses
            $this->warmUpActiveCourses();
            $warmedUp[] = 'active_courses';

            // Warm up languages
            $this->warmUpLanguages();
            $warmedUp[] = 'languages';

            Log::info('Cache warm-up completed', [
                'warmed_up' => $warmedUp
            ]);

        } catch (\Exception $e) {
            Log::error('Cache warm-up failed', [
                'error' => $e->getMessage(),
                'warmed_up' => $warmedUp
            ]);
        }

        return $warmedUp;
    }

    /**
     * Warm up system settings cache
     */
    private function warmUpSystemSettings(): void
    {
        $settings = \App\Models\SystemSetting::all()->toArray();
        Cache::put('system_settings', $settings, self::DAILY_TTL);
    }

    /**
     * Warm up active courses cache
     */
    private function warmUpActiveCourses(): void
    {
        $courses = \App\Models\Course::active()->get()->toArray();
        Cache::put('active_courses', $courses, self::LONG_TTL);
    }

    /**
     * Warm up languages cache
     */
    private function warmUpLanguages(): void
    {
        $languages = \App\Models\Language::all()->toArray();
        Cache::put('languages', $languages, self::DAILY_TTL);
    }

    /**
     * Clear all application cache
     */
    public function clearAllCache(): bool
    {
        try {
            Cache::flush();
            Log::info('All cache cleared successfully');
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to clear all cache', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Cache with tags (if supported)
     */
    public function cacheWithTags(array $tags, string $key, $value, int $ttl = self::MEDIUM_TTL): bool
    {
        try {
            return Cache::tags($tags)->put($key, $value, $ttl);
        } catch (\Exception $e) {
            // Fallback to regular caching if tags are not supported
            return Cache::put($key, $value, $ttl);
        }
    }

    /**
     * Remember cached value with callback
     */
    public function remember(string $key, int $ttl, callable $callback)
    {
        try {
            return Cache::remember($key, $ttl, $callback);
        } catch (\Exception $e) {
            Log::error('Cache remember failed', [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            // Execute callback directly if cache fails
            return $callback();
        }
    }
}
