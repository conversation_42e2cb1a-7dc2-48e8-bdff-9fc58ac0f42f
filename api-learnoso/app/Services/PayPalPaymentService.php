<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use Bavix\Wallet\Models\Wallet;

class PayPalPaymentService
{
    protected $provider;

    public function __construct()
    {
        // Initialize PayPal provider with credentials
        $this->provider = new PayPalClient();
        $this->provider->setApiCredentials(config('paypal'));
    }

    // Create PayPal order
    public function createOrder($data)
    {
        try {
            $this->provider->getAccessToken();

            $user = Auth::user();

            $order = $this->provider->createOrder([
                "intent" => "CAPTURE",
                "application_context" => [
                    "return_url" => env('PAYPAL_SUCCESS_PAYMENT_REDIRECT_URL'),
                    "cancel_url" => env('PAYPAL_FAILURE_PAYMENT_REDIRECT_URL'),
                ],
                "purchase_units" => [
                    0 => [
                        "amount" => [
                            "currency_code" => $data['currency'],
                            "value" => $data['total'],
                        ],
                        "reference_id" => encrypt($user->id),
                    ],
                ],
            ]);

            return $order;
        } catch (Exception $e) {
            Log::error('PayPal Create Order Error: ' . $e->getMessage());
            throw new Exception('Failed to create PayPal order. Please try again later.');
        }
    }

    // Capture the PayPal order
    public function captureOrder($token)
    {
        try {
            $this->provider->getAccessToken();
            return $this->provider->capturePaymentOrder($token);
        } catch (Exception $e) {
            Log::error('PayPal Capture Order Error: ' . $e->getMessage());
            throw new Exception('Failed to capture PayPal order. Please try again later.');
        }
    }

    // Withdraw funds to a PayPal account
    public function withdrawFunds($recipientEmail, $amount, $currency = 'USD')
    {
        try {
            $user = Auth::user();
            $wallet = $user->wallet;

            if ($wallet->balance < $amount) {
                throw new Exception('Insufficient balance in your wallet.');
            }


            $this->provider->getAccessToken();

            $payout = $this->provider->createBatchPayout([
                'sender_batch_header' => [
                    'email_subject' => 'You have a payment',
                    'email_message' => 'You have received a payment from our platform.',
                ],
                'items' => [
                    [
                        'recipient_type' => 'EMAIL',
                        'receiver' => $recipientEmail,
                        'amount' => [
                            'value' => $amount,
                            'currency' => $currency,
                        ],
                        'note' => 'Thank you for using our service!',
                        'sender_item_id' => uniqid(),
                    ],
                ],
            ]);

            // Deduct amount from wallet
            $wallet->withdraw($amount);

            return $payout;
        } catch (Exception $e) {
            Log::error('PayPal Payout Error: ' . $e->getMessage());
            throw new Exception('Failed to process PayPal payout. Please try again later.');
        }
    }

    // Check payout status
    public function checkPayoutStatus($batchId)
    {
        try {
            $this->provider->getAccessToken();
            $payoutStatus = $this->provider->showBatchPayoutDetails($batchId);

            return $payoutStatus;
        } catch (Exception $e) {
            Log::error('PayPal Payout Status Check Error: ' . $e->getMessage());
            throw new Exception('Failed to check PayPal payout status. Please try again later.');
        }
    }
}
