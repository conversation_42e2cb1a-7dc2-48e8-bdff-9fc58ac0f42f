<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;

class ExchangeService
{
    protected static $apiUrl = 'https://api.apilayer.com/currency_data/convert';
    protected static $apiKey = '7jFgk9bmSfMYiOfAbKuNq5xuWIWuDFK2';

    public static function convertCurrency(string $from, string $to, float $amount)
    {
        try {
            $url = sprintf('%s?to=%s&from=%s&amount=%s', self::$apiUrl, strtoupper($to), strtoupper($from), $amount);

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_HTTPHEADER => [
                    "Content-Type: text/plain",
                    "apikey: " . self::$apiKey,
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "GET",
            ]);

            $response = curl_exec($curl);
            curl_close($curl);

            $data = json_decode($response, true);

            if (isset($data['success']) && $data['success']) {
                Log::info('Currency conversion data', $data);
                return [
                    'amount' => $data['result'],
                    'from' => $data['query']['from'],
                    'to' => $data['query']['to'],
                    'rate' => $data['info']['quote'],
                    'timestamp' => $data['info']['timestamp']
                ];
            }

            throw new Exception($data['error']['info'] ?? 'Failed to fetch conversion data');
        } catch (Exception $e) {
            throw new Exception('Error converting currency: ' . $e->getMessage());
        }
    }

}
