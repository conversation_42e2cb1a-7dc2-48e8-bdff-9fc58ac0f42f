<?php

namespace App\Services;

use App\Repositories\Contracts\TutorRepositoryInterface;
use App\Repositories\Contracts\CourseRepositoryInterface;
use App\Models\OnboardingProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TutorService
{
    protected TutorRepositoryInterface $tutorRepository;
    protected $courseRepository;

    protected $uploadService;

    protected $userService;

    public function __construct(
        TutorRepositoryInterface $tutorRepository,
        CourseRepositoryInterface $courseRepository,
        UserService $userService,
        UploadService $uploadService
    ) {
        $this->tutorRepository = $tutorRepository;
        $this->courseRepository = $courseRepository;
        $this->userService = $userService;
        $this->uploadService = $uploadService;
    }

    /**
     * Create or update tutor's associated courses.
     *
     * @param int $userId
     * @param array $courseIds
     * @return mixed
     * @throws \Exception
     */
    public function onboardTutorWithCourses($userId, array $courseIds)
    {
        try {
            // Find the tutor (User with a role 'tutor')
            $tutor = $this->tutorRepository->findByUserId($userId);

            if (!$tutor) {
                throw new \Exception("Tutor not found.");
            }

            // Ensure all courses exist before proceeding
            foreach ($courseIds as $courseId) {
                $course = $this->courseRepository->find($courseId);

                if (!$course) {
                    throw new \Exception("Course with ID $courseId not found.");
                }
            }

            // Sync the tutor's courses (attach new and detach old ones)
            $tutor->courses()->sync($courseIds);

            // Set the onboarding progress to step 1 if it's a new tutor or proceed with the next step.
            $this->setOnboardingStep($tutor->id, 1);

            $this->userService->assignRole($tutor, 'tutor');

            return $tutor->load('courses');
        } catch (\Exception $e) {
            Log::error('Failed to onboard/update tutor with courses: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Set the current onboarding step for the tutor.
     *
     * @param int $userId
     * @param int $step
     * @param array|null $data
     * @return mixed
     * @throws \Exception
     */
    public function setOnboardingStep($userId, $step, array $data = null)
    {
        try {
            // Find existing onboarding progress or create new
            $progress = OnboardingProgress::firstOrNew(['user_id' => $userId]);

            // Set the current step and optional data
            $progress->current_step = $step;
            $progress->onboarding_type = 'tutor';
            if ($data) {
                $progress->data = json_encode($data); // Store any additional step data
            }

            $progress->save();

            return $progress;
        } catch (\Exception $e) {
            Log::error('Failed to update onboarding step: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Set the tutor's description and motivation.
     *
     * @param int $userId
     * @param string $description
     * @param string $motivation
     * @return mixed
     * @throws \Exception
     */
    public function setTutorDescriptionAndMotivation($userId, $description, $motivation, $primary_language_id = null)
    {
        try {
            // Find the tutor (User with a role 'tutor')
            $user = $this->tutorRepository->findByUserId($userId);

            if (!$user->tutor) {
                // Create a new tutor entry
                $tutor = $this->tutorRepository->create([
                    'user_id' => $userId,
                    'short_description' => $description,
                    'motivation_to_students' => $motivation
                ]);

            } else {
                $user->tutor->update([
                    'short_description' => $description,
                    'motivation_to_students' => $motivation
                ]);

            }

            // check if the user already has role tutor, if not assign it
            if (!$user->hasRole('tutor')) {
                $this->userService->assignRole($user, 'tutor');
            }

            // Set the onboarding progress to step 1 if it's a new tutor or proceed with the next step.
            $this->setOnboardingStep($user->id, 2);

            // Sync the tutor's languages
            $user->languages()->sync($user->languages->pluck('id')->toArray());

            // Handle primary language setting
            if (isset($primary_language_id)) {
                // Unset the current primary language, if any
                $user->languages()->updateExistingPivot(
                    $user->languages()->wherePivot('is_primary', true)->first(),
                    ['is_primary' => false]
                );

                // Set the selected language as primary
                $user->languages()->updateExistingPivot($primary_language_id, ['is_primary' => true]);
            }

            // Reload the user's languages and pivot data (including 'is_primary')
            $user->load([
                'languages' => function ($query) {
                    $query->select('languages.*')->withPivot('is_primary');
                },
                'onboardingStatus'
            ]);

            return $user;
        } catch (\Exception $e) {
            Log::error('Failed to set tutor description and motivation: ' . $e->getMessage());
            throw $e;
        }
    }


    public function setTutorProfileAndVideoUrl($request)
    {
        try {
            // Find the tutor (User with a role 'tutor')
            $user = $this->tutorRepository->findByUserId($request->user()->id);

            if(isset($request['profile_picture'])){
                $data['profile_picture'] = $this->uploadService->uploadFile(
                    $request->profile_picture,
                    'profile_pictures',
                    'public'
                );

            }
            if (!$user->tutor) {

                // Create a new tutor entry
                $tutor = $this->tutorRepository->create([
                    'user_id' => $request->user()->id,
                    'profile_picture' => $data['profile_picture'] ?? null,
                    'video_url' => $request->video_url
                ]);

            } else {
                $user->tutor->update([
                    'video_url' => $request->video_url,
                    'profile_picture' => $data['profile_picture'] ?? null
                ]);
            }

            // Set the onboarding progress to step 1 if it's a new tutor or proceed with the next step.
            $this->setOnboardingStep($user->id, 4);

            $user->load('tutor', 'onboardingStatus', 'educations');

            if (!$user->hasRole('tutor')) {
                $this->userService->assignRole($user, 'tutor');
            }


            return $user;
        } catch (\Exception $e) {
            Log::error('Failed to set tutor profile and video url: ' . $e->getMessage());
            throw $e;
        }
    }

    public function updatePriceAndAvailability($data)
    {
        try {
            // Find the tutor (User with a role 'tutor')
            $user = $this->tutorRepository->findByUserId($data['user_id']);
            if (!$user->tutor) {
                throw new \Exception("Tutor not found.");
            }

            $user->tutor->update([
                'price' => $data['price'],
                'currency' => $data['currency'],
                'availability' => json_encode($data['availability']),
            ]);

            // Set the onboarding progress to step 1 if it's a new tutor or proceed with the next step.
            $this->setOnboardingStep($user->id, 5);

            $user->load('tutor', 'onboardingStatus', 'educations');

            if (!$user->hasRole('tutor')) {
                $this->userService->assignRole($user, 'tutor');
            }

            $user->switchRole('tutor');

            return $user;
        }

        catch (\Exception $e) {
            Log::error('Failed to update price and availability: ' . $e->getMessage());
            throw $e;
        }
    }

    public function getFullTutorProfile($userid){
        $user = $this->tutorRepository->findByUserId($userid);
        $user->load('tutor', 'courses', 'onboardingStatus', 'educations');
        return $user;
    }


    public function getTutorDashboard($tutorId): array|null{
        $stats = $this->tutorRepository->getTutorDashboardStats($tutorId);
        return $stats;
    }

    // Get all tutors
    public function getAllTutors($request){
        return $this->tutorRepository->getTutors($request);
    }


    public function getTutorApplications($request){
        return $this->tutorRepository->getTutorApplications($request);
    }

    public function getTutorApplicationStats(){
        return $this->tutorRepository->getTutorApplicationStats();
    }

    public function getTutorById($id)
    {
        return $this->tutorRepository->findByUserId($id);
    }

}
