<?php

namespace App\Services;

use App\Models\Lesson;
use App\Models\User;
use App\Models\Tutor;
use App\Models\Student;
use App\Models\Course;
use App\Models\CourseMaterial;
use App\Models\LessonTracker;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class QueryOptimizationService
{
    /**
     * Get lessons with all necessary relationships
     */
    public function getLessonsWithRelations(array $filters = []): Builder
    {
        return Lesson::with([
            'student:id,first_name,last_name,email',
            'tutor:id,first_name,last_name,email',
            'tutor.tutor:id,user_id,price,profile_status',
            'course:id,name',
            'tracker:id,lesson_id,status,is_active,total_duration_seconds,active_duration_seconds,billable_amount'
        ])
        ->when(isset($filters['student_id']), function($query) use ($filters) {
            return $query->where('student_id', $filters['student_id']);
        })
        ->when(isset($filters['tutor_id']), function($query) use ($filters) {
            return $query->where('tutor_id', $filters['tutor_id']);
        })
        ->when(isset($filters['status']), function($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })
        ->when(isset($filters['date_from']), function($query) use ($filters) {
            return $query->where('starts_at', '>=', $filters['date_from']);
        })
        ->when(isset($filters['date_to']), function($query) use ($filters) {
            return $query->where('starts_at', '<=', $filters['date_to']);
        });
    }

    /**
     * Get tutors with optimized relationships
     */
    public function getTutorsWithRelations(array $filters = []): Builder
    {
        return User::with([
            'tutor:id,user_id,price,profile_status,description,motivation,timezone',
            'tutor.courses:id,name',
            'tutor.languages:id,name,code',
            'tutor.educations:id,tutor_id,degree,field_of_study,institution,graduation_year',
            'tutorReviews:id,tutor_id,student_id,rating,comment,created_at',
            'tutorReviews.student:id,first_name,last_name'
        ])
        ->whereHas('tutor')
        ->when(isset($filters['course_id']), function($query) use ($filters) {
            return $query->whereHas('tutor.courses', function($q) use ($filters) {
                $q->where('courses.id', $filters['course_id']);
            });
        })
        ->when(isset($filters['min_price']), function($query) use ($filters) {
            return $query->whereHas('tutor', function($q) use ($filters) {
                $q->where('price', '>=', $filters['min_price']);
            });
        })
        ->when(isset($filters['max_price']), function($query) use ($filters) {
            return $query->whereHas('tutor', function($q) use ($filters) {
                $q->where('price', '<=', $filters['max_price']);
            });
        })
        ->when(isset($filters['language_id']), function($query) use ($filters) {
            return $query->whereHas('tutor.languages', function($q) use ($filters) {
                $q->where('languages.id', $filters['language_id']);
            });
        });
    }

    /**
     * Get students with optimized relationships
     */
    public function getStudentsWithRelations(array $filters = []): Builder
    {
        return User::with([
            'student:id,user_id,created_at',
            'student.courses:id,name',
            'student.languages:id,name,code',
            'lessons:id,student_id,tutor_id,course_id,starts_at,status',
            'lessons.tutor:id,first_name,last_name',
            'lessons.course:id,name'
        ])
        ->whereHas('student')
        ->when(isset($filters['course_id']), function($query) use ($filters) {
            return $query->whereHas('student.courses', function($q) use ($filters) {
                $q->where('courses.id', $filters['course_id']);
            });
        });
    }

    /**
     * Get course materials with optimized relationships
     */
    public function getCourseMaterialsWithRelations(array $filters = []): Builder
    {
        return CourseMaterial::with([
            'course:id,name',
            'tutor:id,first_name,last_name',
            'tutor.tutor:id,user_id,profile_status',
            'prerequisites:id,material_id,prerequisite_material_id',
            'prerequisites.prerequisiteMaterial:id,title,type',
            'curricula:id,material_id,title,description,duration_minutes',
            'evaluations:id,material_id,title,type,max_score',
            'submissions:id,material_id,student_id,status,score',
            'reviews:id,material_id,student_id,rating,content'
        ])
        ->when(isset($filters['course_id']), function($query) use ($filters) {
            return $query->where('course_id', $filters['course_id']);
        })
        ->when(isset($filters['tutor_id']), function($query) use ($filters) {
            return $query->where('tutor_id', $filters['tutor_id']);
        })
        ->when(isset($filters['type']), function($query) use ($filters) {
            return $query->where('type', $filters['type']);
        })
        ->when(isset($filters['difficulty_level']), function($query) use ($filters) {
            return $query->where('difficulty_level', $filters['difficulty_level']);
        });
    }

    /**
     * Get lesson trackers with optimized relationships
     */
    public function getLessonTrackersWithRelations(array $filters = []): Builder
    {
        return LessonTracker::with([
            'lesson:id,student_id,tutor_id,course_id,starts_at,ends_at,status',
            'lesson.course:id,name',
            'student:id,first_name,last_name,email',
            'tutor:id,first_name,last_name,email',
            'tutor.tutor:id,user_id,price'
        ])
        ->when(isset($filters['student_id']), function($query) use ($filters) {
            return $query->where('student_id', $filters['student_id']);
        })
        ->when(isset($filters['tutor_id']), function($query) use ($filters) {
            return $query->where('tutor_id', $filters['tutor_id']);
        })
        ->when(isset($filters['status']), function($query) use ($filters) {
            return $query->where('status', $filters['status']);
        })
        ->when(isset($filters['is_active']), function($query) use ($filters) {
            return $query->where('is_active', $filters['is_active']);
        });
    }

    /**
     * Get user profile with all relationships
     */
    public function getUserProfileWithRelations(int $userId): ?User
    {
        return User::with([
            'student:id,user_id,created_at',
            'student.courses:id,name',
            'student.languages:id,name,code',
            'tutor:id,user_id,price,profile_status,description,motivation,timezone',
            'tutor.courses:id,name',
            'tutor.languages:id,name,code',
            'tutor.educations:id,tutor_id,degree,field_of_study,institution,graduation_year',
            'roles:id,name',
            'permissions:id,name'
        ])->find($userId);
    }

    /**
     * Get tutor dashboard data with optimized queries
     */
    public function getTutorDashboardData(int $tutorId): array
    {
        $tutor = User::with([
            'tutor:id,user_id,price,profile_status',
            'tutorReviews:id,tutor_id,rating',
            'lessons' => function($query) {
                $query->select('id,tutor_id,student_id,course_id,starts_at,status,confirmed,attended')
                      ->with([
                          'student:id,first_name,last_name',
                          'course:id,name',
                          'tracker:id,lesson_id,status,billable_amount'
                      ])
                      ->where('starts_at', '>=', now()->subDays(30));
            }
        ])->find($tutorId);

        if (!$tutor || !$tutor->tutor) {
            return [];
        }

        $lessons = $tutor->lessons;
        $reviews = $tutor->tutorReviews;

        return [
            'total_lessons' => $lessons->count(),
            'completed_lessons' => $lessons->where('status', 'completed')->count(),
            'upcoming_lessons' => $lessons->where('starts_at', '>', now())->count(),
            'total_earnings' => $lessons->sum(function($lesson) {
                return $lesson->tracker ? $lesson->tracker->billable_amount : 0;
            }),
            'average_rating' => $reviews->avg('rating') ?? 0,
            'total_reviews' => $reviews->count(),
            'recent_lessons' => $lessons->sortByDesc('starts_at')->take(5)->values(),
            'profile_completion' => $this->calculateProfileCompletion($tutor->tutor)
        ];
    }

    /**
     * Get student dashboard data with optimized queries
     */
    public function getStudentDashboardData(int $studentId): array
    {
        $student = User::with([
            'student:id,user_id,created_at',
            'lessons' => function($query) {
                $query->select('id,student_id,tutor_id,course_id,starts_at,status,confirmed,attended')
                      ->with([
                          'tutor:id,first_name,last_name',
                          'course:id,name',
                          'tracker:id,lesson_id,status,total_duration_seconds'
                      ])
                      ->where('starts_at', '>=', now()->subDays(30));
            }
        ])->find($studentId);

        if (!$student || !$student->student) {
            return [];
        }

        $lessons = $student->lessons;

        return [
            'total_lessons' => $lessons->count(),
            'completed_lessons' => $lessons->where('status', 'completed')->count(),
            'upcoming_lessons' => $lessons->where('starts_at', '>', now())->count(),
            'total_learning_time' => $lessons->sum(function($lesson) {
                return $lesson->tracker ? $lesson->tracker->total_duration_seconds : 0;
            }),
            'recent_lessons' => $lessons->sortByDesc('starts_at')->take(5)->values(),
            'favorite_tutors' => $this->getFavoriteTutors($studentId)
        ];
    }

    /**
     * Get analytics data with optimized queries
     */
    public function getAnalyticsData(array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? now()->subDays(30);
        $endDate = $dateRange['end'] ?? now();

        // Use raw queries for analytics to avoid loading models
        $lessonStats = DB::table('lessons')
            ->leftJoin('lesson_trackers', 'lessons.id', '=', 'lesson_trackers.lesson_id')
            ->whereBetween('lessons.created_at', [$startDate, $endDate])
            ->selectRaw('
                COUNT(*) as total_lessons,
                COUNT(CASE WHEN lessons.status = "completed" THEN 1 END) as completed_lessons,
                COUNT(CASE WHEN lessons.status = "cancelled" THEN 1 END) as cancelled_lessons,
                AVG(lesson_trackers.total_duration_seconds) as avg_duration,
                SUM(lesson_trackers.billable_amount) as total_revenue
            ')
            ->first();

        $userStats = DB::table('users')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('
                COUNT(*) as total_users,
                COUNT(CASE WHEN EXISTS(SELECT 1 FROM tutors WHERE tutors.user_id = users.id) THEN 1 END) as total_tutors,
                COUNT(CASE WHEN EXISTS(SELECT 1 FROM students WHERE students.user_id = users.id) THEN 1 END) as total_students
            ')
            ->first();

        return [
            'lessons' => [
                'total' => $lessonStats->total_lessons ?? 0,
                'completed' => $lessonStats->completed_lessons ?? 0,
                'cancelled' => $lessonStats->cancelled_lessons ?? 0,
                'completion_rate' => $lessonStats->total_lessons > 0
                    ? round(($lessonStats->completed_lessons / $lessonStats->total_lessons) * 100, 2)
                    : 0,
                'avg_duration_minutes' => round(($lessonStats->avg_duration ?? 0) / 60, 2),
                'total_revenue' => $lessonStats->total_revenue ?? 0
            ],
            'users' => [
                'total' => $userStats->total_users ?? 0,
                'tutors' => $userStats->total_tutors ?? 0,
                'students' => $userStats->total_students ?? 0
            ]
        ];
    }

    /**
     * Calculate profile completion percentage
     */
    private function calculateProfileCompletion($tutor): int
    {
        $fields = [
            'description' => !empty($tutor->description),
            'motivation' => !empty($tutor->motivation),
            'price' => $tutor->price > 0,
            'timezone' => !empty($tutor->timezone),
            'profile_status' => $tutor->profile_status === 'approved'
        ];

        $completed = array_sum($fields);
        return round(($completed / count($fields)) * 100);
    }

    /**
     * Get favorite tutors for a student
     */
    private function getFavoriteTutors(int $studentId): Collection
    {
        return DB::table('lessons')
            ->select('tutor_id', DB::raw('COUNT(*) as lesson_count'))
            ->where('student_id', $studentId)
            ->where('status', 'completed')
            ->groupBy('tutor_id')
            ->orderByDesc('lesson_count')
            ->limit(5)
            ->get()
            ->pluck('tutor_id')
            ->map(function($tutorId) {
                return User::with('tutor:id,user_id')
                    ->select('id', 'first_name', 'last_name')
                    ->find($tutorId);
            })
            ->filter();
    }

    /**
     * Preload commonly accessed data for caching
     */
    public function preloadCommonData(): array
    {
        $data = [];

        // Preload active courses
        $data['courses'] = Course::select('id', 'name', 'created_at')
            ->with(['tutors:id,user_id', 'students:id,user_id'])
            ->get();

        // Preload languages
        $data['languages'] = \App\Models\Language::select('id', 'name', 'code')->get();

        // Preload system settings
        $data['settings'] = \App\Models\SystemSetting::select('key', 'value')->get();

        return $data;
    }
}
