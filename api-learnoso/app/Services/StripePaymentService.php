<?php

namespace App\Services;

use App\Models\StripeConnectedAccount;
use App\Enums\SupportCurrency;
use App\Models\User;
use App\Services\ExchangeService;
use Exception;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Account;
use Stripe\Payout;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Stripe\AccountLink;
use Stripe\Balance;
use App\Models\SystemSetting;

class StripePaymentService
{
    public function __construct()
    {
        Stripe::setApiKey(config('stripe.secret'));
    }

    public function handle(Request $request)
    {
        try {
            $description = $request->input('description');
            $currency = $request->input('currency');
            $totalPrice = $request->input('total');
            $user = $request->user();

            $systemCurrency = getSystemSetting('system_currency', 'USD');
            $convertedAmount = ($currency !== $systemCurrency)
                ? ExchangeService::convertCurrency($currency, $systemCurrency, $totalPrice)['amount']
                : $totalPrice;

            $currencyConversionFactors = [
                SupportCurrency::USD => 100,
                SupportCurrency::EUR => 100,
                SupportCurrency::GBP => 100,
            ];

            $conversionFactor = $currencyConversionFactors[$currency] ?? 1;
            $totalPriceInCents = $totalPrice * $conversionFactor;

            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price_data' => [
                            'currency' => $currency,
                            'product_data' => ['name' => $description],
                            'unit_amount' => $totalPriceInCents,
                        ],
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'payment',
                'success_url' => config('stripe.success_url'),
                'cancel_url' => config('stripe.failure_url'),
                'metadata' => [
                    'description' => $description,
                    'amount' => $totalPrice,
                ],
            ]);

            $user->deposit($convertedAmount, [
                'type' => 'stripe_topup_payment',
                'user' => $user,
                'currency' => $systemCurrency,
            ], true);

            return ['checkout_url' => $session->url];
        } catch (Exception $e) {
            Log::error('Stripe Payment Error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function withdrawFunds(array $data, $user)
    {
        try {
            $amount = $data['amount'];
            $currency = $data['currency'];
            $destination = $data['destination'];

            // Get withdrawal fee from system settings
            $withdrawalFee = SystemSetting::getWithdrawalFee();
            $minimumWithdrawalAmount = SystemSetting::getMinimumWithdrawalAmount();

            // Check minimum withdrawal amount
            if ($amount < $minimumWithdrawalAmount) {
                throw new Exception("Minimum withdrawal amount is " . $minimumWithdrawalAmount);
            }

            // Calculate fee
            $fee = $amount * $withdrawalFee;
            $netAmount = $amount - $fee;

            // Create Stripe payout
            $stripePayout = Payout::create([
                'amount' => $netAmount * 100, // Convert to cents
                'currency' => $currency,
                'destination' => $destination,
                'metadata' => [
                    'user_id' => $user->id,
                    'withdrawal_fee' => $fee,
                    'original_amount' => $amount
                ]
            ]);

            // Deduct from user's wallet
            $user->wallet->withdraw($amount, [
                'type' => 'stripe_withdrawal',
                'currency' => $currency,
                'destination' => $destination,
                'withdrawal_fee' => $fee
            ]);

            return $stripePayout;
        } catch (Exception $e) {
            Log::error("Stripe Withdrawal Error: " . $e->getMessage());
            throw new Exception("Stripe Payout Failed: " . $e->getMessage());
        }
    }

    public function getPlatformBalance()
    {
        try {
            // Retrieve your platform's balance
            $balance = Balance::retrieve();

            return $balance;
        } catch (Exception $e) {
            Log::error('Error retrieving Stripe platform balance: ' . $e->getMessage());
            throw new Exception('Failed to retrieve Stripe platform balance.');
        }
    }

    public function getOrLinkConnectedAccount($user, $recipient)
    {
        // Check if the user already has a connected account
        $connectedAccount = StripeConnectedAccount::where('user_id', $user->id)->first();

        if ($connectedAccount) {
            return $connectedAccount; // Return the connected account if it exists
        }


        return $this->startStripeOauthLinkingProcess($user, $recipient);
    }

    public function startStripeOauthLinkingProcess($user, $recipient)
    {
        try {

            // connect the account

            $account = Account::create([
                'type' => 'express',
                'country' => 'US',
                'email' => $user->email,
                'capabilities' => [
                    'card_payments' => ['requested' => true],
                    'transfers' => ['requested' => true],
                ],
            ]);

            StripeConnectedAccount::create([
                'user_id' => $user->id,
                'account_id' => $account->id,
            ]);


            $accountLink = AccountLink::create([
                'account' => $account->id,
                'failure_url' => config('stripe.failure_url'),
                'success_url' => route('stripe.callback', [
                    'account_id' => $account->id,
                    'user_id' => encrypt($user->id),
                ]),
                'type' => 'account_onboarding',
            ]);


            // Redirect the user to complete the OAuth process
            return $accountLink->url;
        } catch (Exception $e) {
            Log::error('Stripe OAuth Linking Error: ' . $e->getMessage());
            return response()->json(['error' => 'Error initiating Stripe OAuth process: ' . $e->getMessage()], 500);
        }
    }

    public function handleStripeAccountLinkCallback(Request $request)
    {
        try {
            $userId = decrypt($request->input('user_id'));
            $accountId = $request->input('account_id');

            $user = User::find($userId);

            if (!$user) {
                throw new Exception('User not found.');
            }


            if (empty($accountId)) {
                throw new Exception('Account ID is missing in callback.');
            }

            // Store the connected account
            StripeConnectedAccount::create([
                'user_id' => $user->id,
                'account_id' => $accountId,
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Stripe Account Link Callback Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to link Stripe account.'], 500);
        }
    }
}
