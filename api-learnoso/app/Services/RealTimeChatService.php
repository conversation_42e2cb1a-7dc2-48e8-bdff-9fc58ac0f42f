<?php

namespace App\Services;

use App\Events\UserTyping;
use App\Events\UserOnlineStatus;
use App\Events\MessageSent;
use App\Models\User;
use App\Models\Conversation;
use App\Models\Message;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Services\NotificationPreferenceService;

class RealTimeChatService
{
    protected const TYPING_TIMEOUT = 3; // seconds
    protected const ONLINE_TIMEOUT = 300; // 5 minutes
    protected const TYPING_CACHE_PREFIX = 'typing:';
    protected const ONLINE_CACHE_PREFIX = 'online:';

    protected NotificationPreferenceService $preferenceService;

    public function __construct(NotificationPreferenceService $preferenceService)
    {
        $this->preferenceService = $preferenceService;
    }

    /**
     * Handle user typing in conversation
     */
    public function handleUserTyping(int $userId, int $conversationId, bool $isTyping = true): bool
    {
        try {
            $user = User::findOrFail($userId);
            $conversation = Conversation::findOrFail($conversationId);

            // Verify user is participant
            if (!$conversation->isParticipant($userId)) {
                return false;
            }

            // Check if user has enabled typing indicators
            if (!$this->preferenceService->shouldShowTypingIndicators($user)) {
                Log::info('Typing indicators disabled for user', [
                    'user_id' => $userId,
                    'conversation_id' => $conversationId
                ]);
                return true; // Return true but don't broadcast
            }

            $cacheKey = self::TYPING_CACHE_PREFIX . $conversationId . ':' . $userId;

            if ($isTyping) {
                // Set typing status with timeout
                Cache::put($cacheKey, true, self::TYPING_TIMEOUT);

                // Broadcast typing event only if other participant wants to see it
                $this->broadcastTypingEventIfAllowed($user, $conversation, true);

                // Auto-remove typing status after timeout
                $this->scheduleTypingTimeout($userId, $conversationId);
            } else {
                // Remove typing status
                Cache::forget($cacheKey);

                // Broadcast stop typing event
                $this->broadcastTypingEventIfAllowed($user, $conversation, false);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to handle user typing', [
                'user_id' => $userId,
                'conversation_id' => $conversationId,
                'is_typing' => $isTyping,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get currently typing users in conversation
     */
    public function getTypingUsers(int $conversationId): array
    {
        $conversation = Conversation::findOrFail($conversationId);
        $typingUsers = [];

        foreach ([$conversation->student_id, $conversation->tutor_id] as $userId) {
            $cacheKey = self::TYPING_CACHE_PREFIX . $conversationId . ':' . $userId;

            if (Cache::has($cacheKey)) {
                $user = User::find($userId);
                if ($user) {
                    $typingUsers[] = [
                        'id' => $user->id,
                        'name' => $user->first_name . ' ' . $user->last_name,
                        'avatar' => $user->profile_image_url,
                    ];
                }
            }
        }

        return $typingUsers;
    }

    /**
     * Update user online status
     */
    public function updateUserOnlineStatus(int $userId, bool $isOnline = true): bool
    {
        try {
            $user = User::findOrFail($userId);
            $cacheKey = self::ONLINE_CACHE_PREFIX . $userId;
            $now = Carbon::now();

            if ($isOnline) {
                // Mark user as online
                Cache::put($cacheKey, [
                    'status' => 'online',
                    'last_seen' => $now->toISOString(),
                ], self::ONLINE_TIMEOUT);

                // Broadcast online status
                Event::dispatch(new UserOnlineStatus($user, true, $now));

                // Update user's last activity in their conversations
                $this->updateUserActivityInConversations($userId);
            } else {
                // Mark user as offline
                $lastSeen = Cache::get($cacheKey)['last_seen'] ?? $now->toISOString();
                Cache::forget($cacheKey);

                // Broadcast offline status
                Event::dispatch(new UserOnlineStatus($user, false, Carbon::parse($lastSeen)));
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to update user online status', [
                'user_id' => $userId,
                'is_online' => $isOnline,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get online status for users
     */
    public function getUsersOnlineStatus(array $userIds): array
    {
        $onlineStatuses = [];

        foreach ($userIds as $userId) {
            $cacheKey = self::ONLINE_CACHE_PREFIX . $userId;
            $status = Cache::get($cacheKey);

            $onlineStatuses[$userId] = [
                'is_online' => $status !== null,
                'last_seen' => $status['last_seen'] ?? null,
                'status' => $status['status'] ?? 'offline'
            ];
        }

        return $onlineStatuses;
    }

    /**
     * Get all online users
     */
    public function getOnlineUsers(): array
    {
        $onlineUsers = [];
        $cachePrefix = self::ONLINE_CACHE_PREFIX;

        // Get all cache keys with online prefix
        $keys = Cache::getRedis()->keys($cachePrefix . '*');

        foreach ($keys as $key) {
            $userId = str_replace($cachePrefix, '', $key);
            $status = Cache::get($key);

            if ($status && $status['status'] === 'online') {
                $user = User::find($userId);
                if ($user) {
                    $onlineUsers[] = [
                        'id' => $user->id,
                        'name' => $user->first_name . ' ' . $user->last_name,
                        'avatar' => $user->profile_image_url,
                        'role' => $user->hasRole('tutor') ? 'tutor' : 'student',
                        'last_seen' => $status['last_seen'],
                    ];
                }
            }
        }

        return $onlineUsers;
    }

    /**
     * Handle message delivery confirmation
     */
    public function confirmMessageDelivery(int $messageId, int $userId): bool
    {
        try {
            $message = Message::findOrFail($messageId);

            // Only the recipient can confirm delivery
            if ($message->sender_id === $userId) {
                return false;
            }

            // Check if user is participant in the conversation
            if (!$message->conversation->isParticipant($userId)) {
                return false;
            }

            $message->markAsDelivered();

            // Optionally broadcast delivery confirmation back to sender
            // This could be used for read receipts
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to confirm message delivery', [
                'message_id' => $messageId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Handle message read confirmation
     */
    public function confirmMessageRead(int $messageId, int $userId): bool
    {
        try {
            $message = Message::findOrFail($messageId);

            // Only the recipient can confirm read
            if ($message->sender_id === $userId) {
                return false;
            }

            // Check if user is participant in the conversation
            if (!$message->conversation->isParticipant($userId)) {
                return false;
            }

            $message->markAsRead();

            // Broadcast read confirmation for real-time read receipts
            $this->broadcastMessageStatusUpdate($message, 'read');

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to confirm message read', [
                'message_id' => $messageId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get conversation participants' online status
     */
    public function getConversationParticipantsStatus(int $conversationId): array
    {
        $conversation = Conversation::with(['student', 'tutor'])->findOrFail($conversationId);

        $participantIds = [$conversation->student_id, $conversation->tutor_id];
        $onlineStatuses = $this->getUsersOnlineStatus($participantIds);
        $typingUsers = $this->getTypingUsers($conversationId);

        return [
            'online_status' => $onlineStatuses,
            'typing_users' => $typingUsers,
            'participants' => [
                'student' => [
                    'id' => $conversation->student->id,
                    'name' => $conversation->student->first_name . ' ' . $conversation->student->last_name,
                    'avatar' => $conversation->student->profile_image_url,
                ],
                'tutor' => [
                    'id' => $conversation->tutor->id,
                    'name' => $conversation->tutor->first_name . ' ' . $conversation->tutor->last_name,
                    'avatar' => $conversation->tutor->profile_image_url,
                ],
            ]
        ];
    }

    /**
     * Clean up inactive typing indicators
     */
    public function cleanupInactiveTypingIndicators(): int
    {
        $cleanedCount = 0;
        $typingKeys = Cache::getRedis()->keys(self::TYPING_CACHE_PREFIX . '*');

        foreach ($typingKeys as $key) {
            if (!Cache::has($key)) {
                $cleanedCount++;
            }
        }

        return $cleanedCount;
    }

    /**
     * Private helper methods
     */
    private function scheduleTypingTimeout(int $userId, int $conversationId): void
    {
        // This would typically be handled by a queue job or Redis expiration
        // For now, we rely on cache expiration
    }

    private function updateUserActivityInConversations(int $userId): void
    {
        // Update last activity timestamp for user's conversations
        Conversation::where('student_id', $userId)
            ->orWhere('tutor_id', $userId)
            ->update(['updated_at' => Carbon::now()]);
    }

    private function broadcastTypingEventIfAllowed(User $user, Conversation $conversation, bool $isTyping): void
    {
        // Determine if the other participant wants to see typing indicators
        $otherParticipantId = $conversation->student_id === $user->id ? $conversation->tutor_id : $conversation->student_id;
        $otherParticipant = User::find($otherParticipantId);

        if ($otherParticipant && $this->preferenceService->shouldShowTypingIndicators($otherParticipant)) {
            Event::dispatch(new UserTyping($user, $conversation, $isTyping));
        }
    }

    private function broadcastMessageStatusUpdate(Message $message, string $status): void
    {
        // Could broadcast message status updates for read receipts
        // This is optional functionality
    }
}
