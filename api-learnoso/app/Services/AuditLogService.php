<?php

namespace App\Services;

use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AuditLogService
{
    protected Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Log a security-related event
     */
    public function logSecurityEvent(
        string $eventType,
        string $action,
        string $description,
        User $user = null,
        string $riskLevel = AuditLog::RISK_MEDIUM,
        array $metadata = []
    ): AuditLog {
        return $this->log([
            'event_type' => $eventType,
            'action' => $action,
            'description' => $description,
            'user_id' => $user?->id,
            'risk_level' => $riskLevel,
            'metadata' => $metadata,
            'tags' => ['security'],
            'requires_review' => in_array($riskLevel, [AuditLog::RISK_HIGH, AuditLog::RISK_CRITICAL])
        ]);
    }

    /**
     * Log user authentication events
     */
    public function logAuth(
        string $action,
        User $user = null,
        string $status = AuditLog::STATUS_SUCCESS,
        array $metadata = []
    ): AuditLog {
        $eventType = match ($action) {
            'login' => AuditLog::EVENT_LOGIN,
            'logout' => AuditLog::EVENT_LOGOUT,
            '2fa_enabled' => AuditLog::EVENT_TWO_FA_ENABLE,
            '2fa_disabled' => AuditLog::EVENT_TWO_FA_DISABLE,
            'account_locked' => AuditLog::EVENT_ACCOUNT_LOCKED,
            'account_unlocked' => AuditLog::EVENT_ACCOUNT_UNLOCKED,
            default => 'auth_' . $action
        };

        $description = $this->generateAuthDescription($action, $user, $status);
        $riskLevel = $this->determineAuthRiskLevel($action, $status, $metadata);

        return $this->log([
            'event_type' => $eventType,
            'action' => $action,
            'description' => $description,
            'user_id' => $user?->id,
            'status' => $status,
            'risk_level' => $riskLevel,
            'auth_method' => $metadata['auth_method'] ?? 'password',
            'metadata' => $metadata,
            'tags' => ['auth', 'security'],
            'requires_review' => $status === AuditLog::STATUS_FAILED || $riskLevel === AuditLog::RISK_HIGH
        ]);
    }

    /**
     * Log payment-related events
     */
    public function logPayment(
        string $action,
        User $user,
        float $amount,
        string $currency,
        string $status = AuditLog::STATUS_SUCCESS,
        array $metadata = []
    ): AuditLog {
        $description = sprintf(
            '%s payment of %s %s',
            ucfirst($action),
            $currency,
            number_format($amount, 2)
        );

        return $this->log([
            'event_type' => $action === 'withdrawal' ? AuditLog::EVENT_WITHDRAWAL : AuditLog::EVENT_PAYMENT,
            'action' => $action,
            'description' => $description,
            'user_id' => $user->id,
            'status' => $status,
            'risk_level' => $this->determinePaymentRiskLevel($amount, $metadata),
            'metadata' => array_merge($metadata, [
                'amount' => $amount,
                'currency' => $currency
            ]),
            'tags' => ['payment', 'financial'],
            'requires_review' => $amount > 1000 || $status === AuditLog::STATUS_FAILED
        ]);
    }

    /**
     * Log data access and modifications
     */
    public function logDataAccess(
        string $action,
        Model $subject,
        User $user = null,
        array $oldValues = [],
        array $newValues = [],
        array $metadata = []
    ): AuditLog {
        $subjectType = get_class($subject);
        $description = sprintf(
            '%s %s (ID: %s)',
            ucfirst($action),
            class_basename($subjectType),
            $subject->getKey()
        );

        return $this->log([
            'event_type' => 'data_access',
            'action' => $action,
            'description' => $description,
            'user_id' => $user?->id,
            'subject_type' => $subjectType,
            'subject_id' => $subject->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'risk_level' => $this->determineDataRiskLevel($action, $subjectType),
            'metadata' => $metadata,
            'tags' => ['data', 'crud']
        ]);
    }

    /**
     * Log admin actions
     */
    public function logAdminAction(
        string $action,
        string $description,
        User $admin,
        User $targetUser = null,
        array $metadata = []
    ): AuditLog {
        return $this->log([
            'event_type' => AuditLog::EVENT_ADMIN_ACTION,
            'action' => $action,
            'description' => $description,
            'user_id' => $targetUser?->id,
            'actor_id' => $admin->id,
            'actor_type' => 'admin',
            'risk_level' => AuditLog::RISK_HIGH,
            'metadata' => $metadata,
            'tags' => ['admin', 'privileged'],
            'requires_review' => true
        ]);
    }

    /**
     * Log suspicious activity
     */
    public function logSuspiciousActivity(
        string $description,
        string $reason,
        User $user = null,
        array $metadata = []
    ): AuditLog {
        return $this->log([
            'event_type' => AuditLog::EVENT_SUSPICIOUS_ACTIVITY,
            'action' => AuditLog::ACTION_ACCESSED,
            'description' => $description,
            'user_id' => $user?->id,
            'risk_level' => AuditLog::RISK_CRITICAL,
            'is_suspicious' => true,
            'suspicious_reason' => $reason,
            'metadata' => $metadata,
            'tags' => ['security', 'suspicious'],
            'requires_review' => true
        ]);
    }

    /**
     * Log API access
     */
    public function logApiAccess(
        string $endpoint,
        string $method,
        User $user = null,
        int $statusCode = 200,
        array $metadata = []
    ): AuditLog {
        $status = $statusCode >= 200 && $statusCode < 300 ? AuditLog::STATUS_SUCCESS : AuditLog::STATUS_FAILED;

        return $this->log([
            'event_type' => AuditLog::EVENT_API_ACCESS,
            'action' => $method,
            'description' => "API access: {$method} {$endpoint}",
            'user_id' => $user?->id,
            'status' => $status,
            'risk_level' => $statusCode >= 400 ? AuditLog::RISK_MEDIUM : AuditLog::RISK_LOW,
            'metadata' => array_merge($metadata, [
                'endpoint' => $endpoint,
                'status_code' => $statusCode
            ]),
            'tags' => ['api'],
            'source' => 'api'
        ]);
    }

    /**
     * Core logging method
     */
    protected function log(array $data): AuditLog
    {
        try {
            // Add request context
            $data = array_merge($data, $this->getRequestContext());

            // Set default actor if not specified
            if (!isset($data['actor_id']) && Auth::check()) {
                $data['actor_id'] = Auth::id();
                $data['actor_type'] = 'user';
            }

            // Add session context
            if (session()->getId()) {
                $data['session_id'] = session()->getId();
            }

            // Sanitize request data
            if (isset($data['request_data'])) {
                $data['request_data'] = $this->sanitizeRequestData($data['request_data']);
            }

            return AuditLog::create($data);
        } catch (\Exception $e) {
            // Log to regular log if audit logging fails
            Log::error('Failed to create audit log', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            // Still create a basic audit log
            return AuditLog::create([
                'event_type' => 'audit_log_error',
                'action' => 'failed',
                'description' => 'Failed to create audit log: ' . $e->getMessage(),
                'risk_level' => AuditLog::RISK_MEDIUM,
                'metadata' => ['original_data' => $data]
            ]);
        }
    }

    /**
     * Get request context information
     */
    protected function getRequestContext(): array
    {
        if (!$this->request) {
            return [];
        }

        return [
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
            'request_method' => $this->request->method(),
            'request_url' => $this->request->fullUrl(),
            'request_data' => $this->request->except(['password', 'password_confirmation', '_token']),
            'source' => $this->detectSource()
        ];
    }

    /**
     * Detect the source of the request
     */
    protected function detectSource(): string
    {
        if (!$this->request) {
            return 'system';
        }

        $userAgent = $this->request->userAgent();

        if (Str::contains($userAgent, ['Mobile', 'Android', 'iPhone'])) {
            return 'mobile';
        }

        if ($this->request->is('api/*')) {
            return 'api';
        }

        return 'web';
    }

    /**
     * Sanitize sensitive data from request
     */
    protected function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = [
            'password', 'password_confirmation', 'current_password', 'new_password',
            'token', 'api_key', 'secret', 'two_factor_code', 'recovery_code'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }

        return $data;
    }

    /**
     * Generate authentication event description
     */
    protected function generateAuthDescription(string $action, ?User $user, string $status): string
    {
        $userName = $user ? $user->email : 'Unknown user';
        $statusText = $status === AuditLog::STATUS_SUCCESS ? 'successful' : 'failed';

        return match ($action) {
            'login' => "User {$statusText} login attempt: {$userName}",
            'logout' => "User logged out: {$userName}",
            '2fa_enabled' => "Two-factor authentication enabled: {$userName}",
            '2fa_disabled' => "Two-factor authentication disabled: {$userName}",
            'account_locked' => "Account locked due to failed attempts: {$userName}",
            'account_unlocked' => "Account unlocked: {$userName}",
            default => "Authentication action '{$action}': {$userName}"
        };
    }

    /**
     * Determine risk level for authentication events
     */
    protected function determineAuthRiskLevel(string $action, string $status, array $metadata): string
    {
        if ($status === AuditLog::STATUS_FAILED) {
            return AuditLog::RISK_MEDIUM;
        }

        return match ($action) {
            'account_locked', 'account_unlocked' => AuditLog::RISK_HIGH,
            '2fa_disabled' => AuditLog::RISK_MEDIUM,
            'login' => isset($metadata['failed_attempts']) && $metadata['failed_attempts'] > 3
                ? AuditLog::RISK_MEDIUM
                : AuditLog::RISK_LOW,
            default => AuditLog::RISK_LOW
        };
    }

    /**
     * Determine risk level for payment events
     */
    protected function determinePaymentRiskLevel(float $amount, array $metadata): string
    {
        if ($amount > 10000) {
            return AuditLog::RISK_CRITICAL;
        }

        if ($amount > 1000) {
            return AuditLog::RISK_HIGH;
        }

        if ($amount > 100) {
            return AuditLog::RISK_MEDIUM;
        }

        return AuditLog::RISK_LOW;
    }

    /**
     * Determine risk level for data access events
     */
    protected function determineDataRiskLevel(string $action, string $subjectType): string
    {
        $highRiskModels = ['User', 'Payment', 'Withdrawal', 'StripeConnectedAccount'];
        $highRiskActions = ['deleted', 'updated'];

        if (in_array(class_basename($subjectType), $highRiskModels) && in_array($action, $highRiskActions)) {
            return AuditLog::RISK_HIGH;
        }

        if (in_array($action, $highRiskActions)) {
            return AuditLog::RISK_MEDIUM;
        }

        return AuditLog::RISK_LOW;
    }

    /**
     * Get audit statistics
     */
    public function getStatistics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_events' => AuditLog::where('created_at', '>=', $startDate)->count(),
            'high_risk_events' => AuditLog::highRisk()->where('created_at', '>=', $startDate)->count(),
            'suspicious_events' => AuditLog::suspicious()->where('created_at', '>=', $startDate)->count(),
            'events_requiring_review' => AuditLog::requiresReview()->count(),
            'recent_events' => AuditLog::recent()->count(),
            'events_by_type' => AuditLog::where('created_at', '>=', $startDate)
                ->selectRaw('event_type, COUNT(*) as count')
                ->groupBy('event_type')
                ->pluck('count', 'event_type')
                ->toArray(),
            'events_by_risk' => AuditLog::where('created_at', '>=', $startDate)
                ->selectRaw('risk_level, COUNT(*) as count')
                ->groupBy('risk_level')
                ->pluck('count', 'risk_level')
                ->toArray()
        ];
    }
}
