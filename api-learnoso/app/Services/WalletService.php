<?php

    namespace App\Services;

    use App\Repositories\Contracts\WalletRepositoryInterface;
    use App\Http\Resources\WalletResource;
    use App\Http\Resources\WalletTransactionResource;
    use App\Models\User;

    class WalletService
    {
        protected $walletRepository;

        public function __construct(WalletRepositoryInterface $walletRepository)
        {
            $this->walletRepository = $walletRepository;
        }

        public function getWallet(User $user)
        {
            $wallet = $this->walletRepository->getWallet($user);
            return new WalletResource($wallet);
        }

        public function getTransactions(User $user, array $data)
        {
            $transactions = $this->walletRepository->getTransactions($user, $data);
            return WalletTransactionResource::collection($transactions);
        }

        public function getAllTransactions(array $filters)
        {
            $transactions = $this->walletRepository->getAllTransactions($filters);
            return $transactions;
        }
}
