<?php

namespace App\Services;

use App\Models\User;
use PragmaRX\Google2FA\Google2FA;
use PragmaRX\Google2FA\Exceptions\Google2FAException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;

class TwoFactorAuthService
{
    protected Google2FA $google2fa;
    protected const BACKUP_CODES_COUNT = 8;
    protected const QR_CODE_SIZE = 200;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Generate a new secret key for 2FA
     */
    public function generateSecretKey(): string
    {
        try {
            return $this->google2fa->generateSecretKey();
        } catch (Google2FAException $e) {
            Log::error('Failed to generate 2FA secret', ['error' => $e->getMessage()]);
            throw new \Exception('Failed to generate 2FA secret');
        }
    }

    /**
     * Generate QR code URL for authenticator apps
     */
    public function generateQrCodeUrl(User $user, string $secret): string
    {
        $appName = config('app.name', 'Learnoso');
        $userEmail = $user->email;

        return $this->google2fa->getQRCodeUrl(
            $appName,
            $userEmail,
            $secret
        );
    }

    /**
     * Generate QR code as inline SVG
     */
    public function generateQrCodeSvg(User $user, string $secret): string
    {
        try {
            $qrCodeUrl = $this->generateQrCodeUrl($user, $secret);

            // Use a QR code generation library (you may need to install one)
            // For now, return the URL - frontend can generate QR code
            return $qrCodeUrl;
        } catch (\Exception $e) {
            Log::error('Failed to generate QR code', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to generate QR code');
        }
    }

    /**
     * Verify a 2FA code
     */
    public function verifyCode(User $user, string $code): bool
    {
        try {
            $secret = $user->getTwoFactorSecret();

            if (!$secret) {
                return false;
            }

            // Check if it's a recovery code first
            if ($this->isRecoveryCode($code)) {
                return $user->useRecoveryCode($code);
            }

            // Verify TOTP code with window tolerance
            $timestamp = $this->google2fa->verifyKeyNewer(
                $secret,
                $code,
                $this->getLastVerifiedTimestamp($user->id)
            );

            if ($timestamp !== false) {
                $this->updateLastVerifiedTimestamp($user->id, $timestamp);
                return true;
            }

            return false;
        } catch (Google2FAException $e) {
            Log::error('Failed to verify 2FA code', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Setup 2FA for a user
     */
    public function setupTwoFactor(User $user): array
    {
        try {
            $secret = $this->generateSecretKey();
            $recoveryCodes = $this->generateRecoveryCodes();
            $qrCodeUrl = $this->generateQrCodeUrl($user, $secret);

            // Store secret temporarily (not confirmed yet)
            $user->update([
                'two_factor_secret' => encrypt($secret),
                'two_factor_recovery_codes' => $recoveryCodes,
                'two_factor_enabled' => false, // Not enabled until confirmed
                'two_factor_confirmed_at' => null
            ]);

            Log::info('2FA setup initiated', ['user_id' => $user->id]);

            return [
                'secret' => $secret,
                'qr_code_url' => $qrCodeUrl,
                'recovery_codes' => $recoveryCodes,
                'manual_entry_key' => $secret
            ];
        } catch (\Exception $e) {
            Log::error('Failed to setup 2FA', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception('Failed to setup two-factor authentication');
        }
    }

    /**
     * Confirm and enable 2FA
     */
    public function confirmTwoFactor(User $user, string $verificationCode): bool
    {
        try {
            $secret = $user->getTwoFactorSecret();

            if (!$secret) {
                return false;
            }

            // Verify the confirmation code
            if ($this->google2fa->verifyKey($secret, $verificationCode)) {
                $user->update([
                    'two_factor_enabled' => true,
                    'two_factor_confirmed_at' => now()
                ]);

                Log::info('2FA confirmed and enabled', ['user_id' => $user->id]);
                return true;
            }

            Log::warning('Invalid 2FA confirmation code', ['user_id' => $user->id]);
            return false;
        } catch (Google2FAException $e) {
            Log::error('Failed to confirm 2FA', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Disable 2FA for a user
     */
    public function disableTwoFactor(User $user, string $currentPassword): bool
    {
        try {
            // Verify current password before disabling
            if (!Hash::check($currentPassword, $user->password)) {
                Log::warning('Invalid password for 2FA disable', ['user_id' => $user->id]);
                return false;
            }

            $user->disableTwoFactor();
            $this->clearVerificationTimestamp($user->id);

            Log::info('2FA disabled', ['user_id' => $user->id]);
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to disable 2FA', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate new recovery codes
     */
    public function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < self::BACKUP_CODES_COUNT; $i++) {
            $codes[] = strtoupper(substr(str_replace(['+', '/', '='], '', base64_encode(random_bytes(6))), 0, 8));
        }
        return $codes;
    }

    /**
     * Regenerate recovery codes for a user
     */
    public function regenerateRecoveryCodes(User $user): array
    {
        $newCodes = $this->generateRecoveryCodes();
        $user->update(['two_factor_recovery_codes' => $newCodes]);

        Log::info('2FA recovery codes regenerated', ['user_id' => $user->id]);
        return $newCodes;
    }

    /**
     * Check if code looks like a recovery code
     */
    private function isRecoveryCode(string $code): bool
    {
        return strlen($code) === 8 && ctype_alnum($code);
    }

    /**
     * Get last verified timestamp for user
     */
    private function getLastVerifiedTimestamp(int $userId): int
    {
        return Cache::get("2fa_last_verified_{$userId}", 0);
    }

    /**
     * Update last verified timestamp
     */
    private function updateLastVerifiedTimestamp(int $userId, int $timestamp): void
    {
        Cache::put("2fa_last_verified_{$userId}", $timestamp, now()->addHours(1));
    }

    /**
     * Clear verification timestamp
     */
    private function clearVerificationTimestamp(int $userId): void
    {
        Cache::forget("2fa_last_verified_{$userId}");
    }

    /**
     * Get 2FA statistics for admin dashboard
     */
    public function getTwoFactorStatistics(): array
    {
        $totalUsers = User::count();
        $enabledUsers = User::where('two_factor_enabled', true)->count();
        $pendingUsers = User::whereNotNull('two_factor_secret')
            ->whereNull('two_factor_confirmed_at')
            ->count();
        $recentlyEnabled = User::where('two_factor_confirmed_at', '>', now()->subDays(7))->count();

        return [
            'total_users' => $totalUsers,
            'enabled_users' => $enabledUsers,
            'pending_users' => $pendingUsers,
            'recently_enabled' => $recentlyEnabled,
            'adoption_rate' => $totalUsers > 0 ? round(($enabledUsers / $totalUsers) * 100, 1) : 0
        ];
    }

    /**
     * Check if user needs 2FA verification
     */
    public function requiresTwoFactorChallenge(User $user): bool
    {
        return $user->hasTwoFactorEnabled();
    }

    /**
     * Generate backup authentication method
     */
    public function generateBackupMethod(User $user): array
    {
        return [
            'recovery_codes' => $user->two_factor_recovery_codes ?? [],
            'remaining_codes' => count($user->two_factor_recovery_codes ?? [])
        ];
    }
}
