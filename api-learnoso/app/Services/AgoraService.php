<?php

namespace App\Services;

use App\Agora\Sample\RtmTokenBuilder2Sample;
use App\Agora\Src\RtcTokenBuilder;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Log;

class AgoraService
{
    public function generateRtcToken($channel, $userId, $duration) {

        $appId = config('agora.app_id');
        $appCertificate = config('agora.app_certificate');

        $role = 0;
        $expireTimeInSeconds = $duration;
        $currentTimestamp = (new DateTime("now", new DateTimeZone('UTC')))->getTimestamp();
        $privilegeExpiredTs = $currentTimestamp + $expireTimeInSeconds;

        if(!$appId) {
            Log::error("App id is missing");
            return;
        }

        if(!$appCertificate) {
            Log::error("App Certificate is missing");
            return;
        }

        $token = RtcTokenBuilder::buildTokenWithUid($appId, $appCertificate, $channel, $userId, $role, $privilegeExpiredTs);

        return $token;
    }

    public function generateRtmTokek(){
        $stdClass = new RtmTokenBuilder2Sample();

        $token = $stdClass->main();
        return $token;
    }
}
