# System Settings API Documentation

## Base URL
All endpoints are prefixed with `/api/v1`

## Authentication
All endpoints require authentication using a Bearer token in the Authorization header:
```
Authorization: Bearer <your_token>
```

## System Settings

### Get All System Settings
Retrieves all system settings. Only accessible by administrators.

```http
GET /settings
```

#### Response
```json
{
    "success": true,
    "message": "System settings retrieved successfully",
    "data": {
        "settings": {
            "system_currency": "USD",
            "timezone": "UTC",
            "default_locale": "en",
            "lesson_commission_rate": 0.10,
            "minimum_commission_amount": 1.00,
            "maximum_commission_amount": 50.00,
            "withdrawal_fee": 0.02,
            "minimum_withdrawal_amount": 10.00
        }
    }
}
```

### Update System Settings
Updates system settings. Only accessible by administrators.

```http
PUT /settings
```

#### Request Body
```json
{
    "settings": {
        "system_currency": "USD",
        "timezone": "UTC",
        "default_locale": "en",
        "lesson_commission_rate": 0.10,
        "minimum_commission_amount": 1.00,
        "maximum_commission_amount": 50.00,
        "withdrawal_fee": 0.02,
        "minimum_withdrawal_amount": 10.00
    }
}
```

#### Response
```json
{
    "success": true,
    "message": "System settings updated successfully",
    "data": {
        "settings": {
            // Updated settings
        }
    }
}
```

### Get Commission Settings
Retrieves public commission settings. Accessible by all authenticated users.

```http
GET /settings/commission
```

#### Response
```json
{
    "success": true,
    "message": "Commission settings retrieved successfully",
    "data": {
        "lesson_commission_rate": 0.10,
        "minimum_commission_amount": 1.00,
        "maximum_commission_amount": 50.00
    }
}
```

## Notification Settings

### Get User Notification Settings
Retrieves the current user's notification preferences.

```http
GET /notification-settings
```

#### Response
```json
{
    "success": true,
    "message": "Notification settings retrieved successfully",
    "data": {
        "email": true,
        "push": true,
        "sms": false
    }
}
```

### Update User Notification Settings
Updates the current user's notification preferences.

```http
PUT /notification-settings
```

#### Request Body
```json
{
    "email": true,
    "push": true,
    "sms": false
}
```

#### Response
```json
{
    "success": true,
    "message": "Notification settings updated successfully",
    "data": {
        "email": true,
        "push": true,
        "sms": false
    }
}
```

## Error Responses

### Validation Error
```json
{
    "success": false,
    "errors": {
        "field_name": [
            "Error message"
        ]
    }
}
```

### Unauthorized Error
```json
{
    "success": false,
    "message": "Unauthorized"
}
```

### Forbidden Error
```json
{
    "success": false,
    "message": "Only administrators can view system settings"
}
```

## System Settings Reference

### Available Settings

| Setting Key | Type | Default | Description |
|-------------|------|---------|-------------|
| system_currency | string | USD | Default currency for the system |
| timezone | string | UTC | Default timezone |
| default_locale | string | en | Default language locale |
| lesson_commission_rate | float | 0.10 | Commission rate for lessons (10%) |
| minimum_commission_amount | float | 1.00 | Minimum commission amount |
| maximum_commission_amount | float | 50.00 | Maximum commission amount |
| withdrawal_fee | float | 0.02 | Fee for withdrawals (2%) |
| minimum_withdrawal_amount | float | 10.00 | Minimum amount for withdrawals |

### Notification Settings Reference

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| email | boolean | true | Enable/disable email notifications |
| push | boolean | true | Enable/disable push notifications |
| sms | boolean | false | Enable/disable SMS notifications |

## Notes

1. All monetary values are stored as decimals (e.g., 0.10 for 10%)
2. System settings can only be modified by administrators
3. Notification settings are user-specific and can be modified by the user themselves
4. The commission settings endpoint is public but read-only 
