# System Settings API Documentation

## Overview
This document outlines the API endpoints for managing system settings, including commission rates, withdrawal fees, and notification preferences.

## Base URL
```
/api/v1
```

## Authentication
All endpoints require authentication using a Bearer token. Include the token in the Authorization header:
```
Authorization: Bearer <your_token>
```

## Endpoints

### Get All System Settings
Retrieves all current system settings.

```http
GET /system-settings
```

**Response**
```json
{
    "data": {
        "settings": {
            "lesson_commission_rate": 0.10,
            "minimum_commission_amount": 1.00,
            "maximum_commission_amount": 50.00,
            "withdrawal_fee": 0.02,
            "minimum_withdrawal_amount": 10.00,
            "notification_settings": {
                "email": true,
                "push": true,
                "sms": false
            }
        }
    }
}
```

### Update Commission Settings
Updates the commission-related settings.

```http
PUT /system-settings/commission
```

**Request Body**
```json
{
    "lesson_commission_rate": 0.10,        // Required, between 0 and 1
    "minimum_commission_amount": 1.00,     // Required, minimum 0
    "maximum_commission_amount": 50.00     // Required, minimum 0
}
```

**Response**
```json
{
    "message": "Commission settings updated successfully",
    "data": {
        "lesson_commission_rate": 0.10,
        "minimum_commission_amount": 1.00,
        "maximum_commission_amount": 50.00
    }
}
```

### Update Withdrawal Settings
Updates the withdrawal-related settings.

```http
PUT /system-settings/withdrawal
```

**Request Body**
```json
{
    "withdrawal_fee": 0.02,               // Required, between 0 and 1
    "minimum_withdrawal_amount": 10.00    // Required, minimum 0
}
```

**Response**
```json
{
    "message": "Withdrawal settings updated successfully",
    "data": {
        "withdrawal_fee": 0.02,
        "minimum_withdrawal_amount": 10.00
    }
}
```

### Update Notification Settings
Updates the default notification preferences.

```http
PUT /system-settings/notifications
```

**Request Body**
```json
{
    "email": true,    // Required, boolean
    "push": true,     // Required, boolean
    "sms": false      // Required, boolean
}
```

**Response**
```json
{
    "message": "Notification settings updated successfully",
    "data": {
        "email": true,
        "push": true,
        "sms": false
    }
}
```

## Error Responses

### Validation Error
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "lesson_commission_rate": [
            "The lesson commission rate must be between 0 and 1."
        ]
    }
}
```

### Unauthorized Error
```json
{
    "message": "Unauthenticated."
}
```

### Forbidden Error
```json
{
    "message": "This action is unauthorized."
}
```

## Notes
- All endpoints require admin privileges
- All monetary values are in the system's default currency
- Commission rates and withdrawal fees are stored as decimals (e.g., 0.10 for 10%)
- Minimum amounts are stored as positive numbers 
