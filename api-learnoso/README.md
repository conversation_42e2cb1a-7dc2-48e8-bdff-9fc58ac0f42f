# Learnoso v2

Learnoso is an online learning platform designed to connect students with tutors, offering features like authentication, onboarding, class scheduling, and online classes. 

## Features

- **Authentication**: Secure login and registration for users.
- **Onboarding**: Students and tutors can sign up and create profiles tailored to their needs.
- **Class Scheduling**: Flexible scheduling system for planning classes.
- **Online Classes**: Real-time online classes between students and tutors.
- **Calendar Scheduling**: Integrated calendar for managing class appointments and availability.
- **Course Management**: Ability to manage courses, including preferences and available times.
- **Notifications**: Email notifications for various activities such as course updates and schedule reminders.
- **User Roles**: Different roles such as student, tutor, admin with assigned permissions.
- **Role Management**: Seamless role assignment for users during onboarding.
- **Course Preferences**: Students can select and manage their courses of preference.
- **Availability Times**: Students and tutors can set their availability times for scheduling.
- **Profile Customization**: Users can customize their profiles, including reasons for learning and other preferences.

## Requirements

- PHP 8.0 or higher
- Laravel 11.x
- MySQL or other supported database systems

## Installation

1. Clone the repository:

   ```bash
   git clone https://github.com/your-username/learnoso.git
   ```

2. Install dependencies:
    ```bash
    composer install
    ```
3. Set up the environment file:
    ```bash
    cp .env.example .env
    ```
4. Generate application key:
    ```
    php artisan key:generate
    ```

5. Configure database settings in the `.env` file.
    ```
    cp .env.example .env
    ```
    ```
    DB_CONNECTION=mysql
    DB_HOST=127.0.0.1
    DB_PORT=3306
    DB_DATABASE=learnoso
    ```
6. Run migrations and seed db
    ```
    php artisan migrate
    php artisan db:seed
    ```
7. Run the development server:
    ```
    php artisan serve
    ```
8. Open your web browser and navigate to `http://localhost:8000` to access the server

## License

This project is not OpenSource,  it availabel under  [MIT License](https://opensource.org/licenses/MIT)

