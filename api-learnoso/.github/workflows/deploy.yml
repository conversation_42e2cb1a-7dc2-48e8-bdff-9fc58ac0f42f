name: Deploy <PERSON><PERSON><PERSON> App to DO

on:
  push:
    branches:
      - main

env:
  PROJECT_PATH: /webapps/deploys/learnosov2

jobs:
  deploy:
    name: Rsync Laravel to Server
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v3

      - name: 🔐 Set up SSH Agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.DO_SSH_KEY }}

      - name: 🧾 Add Server to known_hosts
        run: |
          ssh-keyscan -H ${{ secrets.DO_HOST }} >> ~/.ssh/known_hosts

      - name: 🚚 Rsync Files to Server
        run: |
          rsync -azP --delete \
            -e "ssh -o StrictHostKeyChecking=yes" \
            --exclude='.git/' \
            --exclude='node_modules/' \
            --exclude='storage/' \
            --exclude='vendor/' \
            --exclude='.env' \
            ./ ${{ secrets.DO_USERNAME }}@${{ secrets.DO_HOST }}:$PROJECT_PATH

      - name: ⚙️ SSH & Deploy Laravel
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.DO_USERNAME }}@${{ secrets.DO_HOST }} << EOF
            echo "📁 Navigating to project directory..."
            cd $PROJECT_PATH

            echo "🔐 Copying .env file if not exists..."
            cp -n .env.example .env

            echo "📦 Installing Composer dependencies..."
            composer install --no-interaction --prefer-dist --optimize-autoloader

            echo "🔧 Running Laravel optimizations..."
            php artisan config:clear
            php artisan cache:clear
            php artisan route:clear
            php artisan view:clear

            php artisan config:cache
            php artisan route:cache
            php artisan view:cache

            echo "🧬 Running migrations..."
            php artisan migrate --force

            echo "✅ Deployment completed successfully!"
          EOF
