/**
 * Real-Time Chat JavaScript Implementation
 * This demonstrates how the frontend should handle WebSocket events
 * for typing indicators, online status, and live messaging
 */

class RealTimeChat {
    constructor(config) {
        this.config = config;
        this.pusher = null;
        this.currentUser = config.user;
        this.currentConversationId = null;
        this.typingTimeout = null;
        this.onlineUsers = new Set();

        this.init();
    }

    /**
     * Initialize Pusher connection and event listeners
     */
    init() {
        // Initialize Pusher with client events enabled
        this.pusher = new Pusher(this.config.pusher.key, {
            cluster: this.config.pusher.cluster,
            encrypted: true,
            authEndpoint: '/api/broadcasting/auth',
            auth: {
                headers: {
                    'Authorization': `Bearer ${this.config.auth.token}`,
                    'Accept': 'application/json'
                }
            }
        });

        // Setup global event listeners
        this.setupGlobalEventListeners();

        console.log('Real-time chat initialized');
    }

    /**
     * Join a conversation channel
     */
    joinConversation(conversationId) {
        if (this.currentConversationId) {
            this.leaveConversation();
        }

        this.currentConversationId = conversationId;

        // Subscribe to conversation channel
        const conversationChannel = this.pusher.subscribe(`private-conversation.${conversationId}`);

        // Listen for new messages
        conversationChannel.bind('message.sent', (data) => {
            this.handleNewMessage(data);
        });

        // Listen for typing indicators (both server and client events)
        conversationChannel.bind('user.typing', (data) => {
            this.handleUserTyping(data);
        });

        // Listen for client typing events (emitted by other users)
        conversationChannel.bind('client-typing', (data) => {
            this.handleUserTyping(data);
        });

        console.log(`Joined conversation ${conversationId}`);
    }

    /**
     * Leave current conversation
     */
    leaveConversation() {
        if (this.currentConversationId) {
            this.pusher.unsubscribe(`private-conversation.${this.currentConversationId}`);
            this.currentConversationId = null;
            console.log('Left conversation');
        }
    }

    /**
     * Setup global event listeners (user status, notifications)
     */
    setupGlobalEventListeners() {
        // User's personal message channel
        const userChannel = this.pusher.subscribe(`private-user.${this.currentUser.id}.messages`);
        userChannel.bind('message.sent', (data) => {
            this.handleGlobalMessage(data);
        });

        // User's status channel
        const statusChannel = this.pusher.subscribe(`private-user.${this.currentUser.id}.status`);
        statusChannel.bind('user.status.updated', (data) => {
            this.handleUserStatusUpdate(data);
        });

        // Listen for client status updates
        statusChannel.bind('client-status-update', (data) => {
            this.handleUserStatusUpdate(data);
        });

        // Global presence channel for online users
        const presenceChannel = this.pusher.subscribe('presence-chat.online-users');

        presenceChannel.bind('pusher:subscription_succeeded', (members) => {
            this.handlePresenceSubscriptionSucceeded(members);
        });

        presenceChannel.bind('pusher:member_added', (member) => {
            this.handleUserJoined(member);
        });

        presenceChannel.bind('pusher:member_removed', (member) => {
            this.handleUserLeft(member);
        });
    }

    /**
     * Send typing indicator
     */
    startTyping(conversationId) {
        if (!this.currentConversationId || this.currentConversationId !== conversationId) {
            return;
        }

        // Clear existing timeout
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        // Emit typing event via the service (not HTTP request)
        this.emitTypingEvent(conversationId, true);

        // Auto-stop typing after 3 seconds
        this.typingTimeout = setTimeout(() => {
            this.stopTyping(conversationId);
        }, 3000);
    }

    /**
     * Stop typing indicator
     */
    stopTyping(conversationId) {
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }

        this.emitTypingEvent(conversationId, false);
    }

    /**
     * Emit typing event through client events (INDUSTRY STANDARD)
     */
    emitTypingEvent(conversationId, isTyping) {
        if (!this.currentConversationId) {
            return;
        }

        // Get the conversation channel
        const channel = this.pusher.channel(`private-conversation.${conversationId}`);

        if (channel) {
            // Emit client event directly through WebSocket
            channel.trigger('client-typing', {
                user: {
                    id: this.currentUser.id,
                    name: this.currentUser.name,
                    avatar: this.currentUser.avatar
                },
                conversation_id: conversationId,
                is_typing: isTyping,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Update user online status through client events
     */
    setOnlineStatus(isOnline) {
        // Emit presence event through client events
        const statusChannel = this.pusher.channel(`private-user.${this.currentUser.id}.status`);

        if (statusChannel) {
            statusChannel.trigger('client-status-update', {
                user: {
                    id: this.currentUser.id,
                    name: this.currentUser.name,
                    avatar: this.currentUser.avatar,
                    role: this.currentUser.role
                },
                is_online: isOnline,
                timestamp: new Date().toISOString()
            });
        }

        // Also update through presence channel
        const presenceChannel = this.pusher.channel('presence-chat.online-users');
        if (presenceChannel && !isOnline) {
            // Trigger offline event
            presenceChannel.trigger('client-user-offline', {
                user_id: this.currentUser.id,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * Event Handlers
     */

    handleNewMessage(data) {
        console.log('New message received:', data);

        // Update UI with new message
        this.appendMessageToChat(data.message);

        // Update conversation list
        this.updateConversationList(data.conversation);

        // Mark as delivered if we're the recipient
        if (data.message.sender.id !== this.currentUser.id) {
            this.markMessageAsDelivered(data.message.id);
        }
    }

    handleUserTyping(data) {
        console.log('User typing:', data);

        // Don't show typing indicator for our own typing
        if (data.user.id === this.currentUser.id) {
            return;
        }

        if (data.is_typing) {
            this.showTypingIndicator(data.user);
        } else {
            this.hideTypingIndicator(data.user);
        }
    }

    handleGlobalMessage(data) {
        // Handle messages from conversations we're not currently viewing
        if (!this.currentConversationId ||
            data.conversation.id !== this.currentConversationId) {

            this.showNotification(data);
            this.updateUnreadCount(data.conversation.id);
        }
    }

    handleUserStatusUpdate(data) {
        console.log('User status updated:', data);

        if (data.is_online) {
            this.onlineUsers.add(data.user.id);
        } else {
            this.onlineUsers.delete(data.user.id);
        }

        this.updateUserStatusInUI(data.user, data.is_online, data.last_seen);
    }

    handlePresenceSubscriptionSucceeded(members) {
        console.log('Online users:', members);
        this.onlineUsers.clear();

        members.each((member) => {
            this.onlineUsers.add(member.id);
        });

        this.updateOnlineUsersList();
    }

    handleUserJoined(member) {
        console.log('User joined:', member);
        this.onlineUsers.add(member.id);
        this.updateOnlineUsersList();
    }

    handleUserLeft(member) {
        console.log('User left:', member);
        this.onlineUsers.delete(member.id);
        this.updateOnlineUsersList();
    }

    /**
     * UI Update Methods (to be implemented by your frontend)
     */

    appendMessageToChat(message) {
        // Implement: Add message to chat window
        console.log('TODO: Append message to chat UI', message);
    }

    updateConversationList(conversation) {
        // Implement: Update conversation in sidebar
        console.log('TODO: Update conversation list', conversation);
    }

    showTypingIndicator(user) {
        // Implement: Show "User is typing..." indicator
        console.log(`TODO: Show typing indicator for ${user.name}`);
    }

    hideTypingIndicator(user) {
        // Implement: Hide typing indicator
        console.log(`TODO: Hide typing indicator for ${user.name}`);
    }

    updateUserStatusInUI(user, isOnline, lastSeen) {
        // Implement: Update user's online/offline status in UI
        console.log(`TODO: Update ${user.name} status: ${isOnline ? 'online' : 'offline'}`);
    }

    updateOnlineUsersList() {
        // Implement: Update list of online users
        console.log('TODO: Update online users list', Array.from(this.onlineUsers));
    }

    showNotification(data) {
        // Implement: Show browser/in-app notification
        console.log('TODO: Show notification', data);
    }

    updateUnreadCount(conversationId) {
        // Implement: Update unread message count
        console.log('TODO: Update unread count for conversation', conversationId);
    }

    markMessageAsDelivered(messageId) {
        // This could call your service to mark message as delivered
        if (window.chatService) {
            window.chatService.confirmMessageDelivery(messageId, this.currentUser.id);
        }
    }

    markMessageAsRead(messageId) {
        // This could call your service to mark message as read
        if (window.chatService) {
            window.chatService.confirmMessageRead(messageId, this.currentUser.id);
        }
    }

    /**
     * Lifecycle methods
     */

    disconnect() {
        if (this.pusher) {
            this.leaveConversation();
            this.pusher.disconnect();
        }
    }

    // Auto-update online status based on page visibility
    setupPresenceDetection() {
        // Mark as online when page becomes visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.setOnlineStatus(false);
            } else {
                this.setOnlineStatus(true);
            }
        });

        // Mark as online on page load
        window.addEventListener('load', () => {
            this.setOnlineStatus(true);
        });

        // Mark as offline on page unload
        window.addEventListener('beforeunload', () => {
            this.setOnlineStatus(false);
        });
    }
}

/**
 * Usage Example:
 *
 * const chatConfig = {
 *     user: { id: 123, name: 'John Doe' },
 *     pusher: {
 *         key: 'your-pusher-key',
 *         cluster: 'us2'
 *     },
 *     auth: {
 *         token: 'user-auth-token'
 *     }
 * };
 *
 * const chat = new RealTimeChat(chatConfig);
 * chat.setupPresenceDetection();
 *
 * // When user opens a conversation
 * chat.joinConversation(456);
 *
 * // When user starts typing
 * document.getElementById('message-input').addEventListener('input', () => {
 *     chat.startTyping(456);
 * });
 *
 * // When user stops typing (on blur or submit)
 * document.getElementById('message-input').addEventListener('blur', () => {
 *     chat.stopTyping(456);
 * });
 */

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealTimeChat;
} else {
    window.RealTimeChat = RealTimeChat;
}
