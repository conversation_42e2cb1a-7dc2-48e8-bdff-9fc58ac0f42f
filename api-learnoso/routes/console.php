<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

Artisan::command('setup:architecture', function () {
    $this->call(\App\Console\Commands\SetupArchitecture::class);
});

Artisan::command('setup:feature {name}', function ($name) {
    $this->call(\App\Console\Commands\MakeFeature::class, ['name' => $name]);
});
