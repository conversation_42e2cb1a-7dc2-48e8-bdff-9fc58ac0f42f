<?php

use App\Http\Controllers\Api\Admin\DailyTransactionsController;
use App\Http\Controllers\Api\Admin\FinancialSummaryController;
use App\Http\Controllers\Api\Admin\GeneralDashboardController;
use App\Http\Controllers\Api\Admin\ManageCoursesController;
use App\Http\Controllers\Api\Admin\ManageTutorProfileController;
use App\Http\Controllers\Api\Admin\MonthlyProfitsController;
use App\Http\Controllers\Api\Admin\GetAllTransactionsController;
use App\Http\Controllers\Api\Admin\GetUsersController;
use App\Http\Controllers\Api\Admin\GetUserSummaryController;
use App\Http\Controllers\Api\Admin\TutorApplicationController;
use App\Http\Controllers\Api\Auth\AuthStatusController;
use App\Http\Controllers\Api\Auth\EmailVerificationApiController;
use App\Http\Controllers\Api\Auth\LoginApiController;
use App\Http\Controllers\Api\Auth\LogoutApiController;
use App\Http\Controllers\Api\Auth\RegisterApiController;
use App\Http\Controllers\Api\Auth\ResendEmailVerificationController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\CourseMaterialController;
use App\Http\Controllers\Api\LanguageController;
use App\Http\Controllers\Api\MaterialReviewsController;
use App\Http\Controllers\Api\Student\OnboardStudentController;
use App\Http\Controllers\Api\Tutor\GetFullProfileController;
use App\Http\Controllers\Api\Tutor\PriceAndAvailabilityController;
use App\Http\Controllers\Api\Tutor\TutorCourseApiController;
use App\Http\Controllers\Api\Tutor\TutorProfileAndVideoUrlController;
use App\Http\Controllers\Api\Tutor\UpdateTutorDescriptionAndMotivationController;
use App\Http\Controllers\Api\Tutor\StoreEducationController;
use App\Http\Controllers\Api\Payments\TopupController;
use App\Http\Controllers\Api\Payments\WalletController;
use App\Http\Controllers\Api\Lesson\ScheduleLessonController;
use App\Http\Controllers\Api\CurrencyController;
use App\Http\Controllers\Api\MaterialEvaluationController;
use App\Http\Controllers\Api\MaterialSubmissionController;
use App\Http\Controllers\Api\Payments\WithdrawalController;
use App\Http\Controllers\Api\PrerequisiteController;
use App\Http\Controllers\Api\Tutor\TutorDashboardStatsController;
use App\Http\Controllers\Api\CurriculumController;
use App\Http\Controllers\CommentsController;
use App\Http\Controllers\GetAllStudentsController;
use App\Http\Controllers\GetAllTutorsController;
use App\Http\Controllers\Lesson\ConfirmLessonController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\PayPalController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\TimezoneController;
use App\Http\Controllers\ViewTutorController;
use App\Http\Controllers\Api\SystemSettingsController;
use App\Http\Controllers\Api\NotificationSettingsController;
use App\Http\Controllers\Api\Lesson\UpdateMeetingMetaDataController;
use App\Http\Controllers\Api\LessonTrackerController;
use App\Http\Controllers\Api\AgoraWebhookController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\RealTimeController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\PasswordStrengthController;
use App\Http\Controllers\Api\TwoFactorAuthController;
use App\Http\Controllers\Api\AuditLogController;

Route::get('', function (){
    return redirect('https://www.learnoso.com');
});

Route::post('register', RegisterApiController::class)->middleware(['throttle:auth', 'audit']);
Route::get('/verify-email/{id}/{hash}', EmailVerificationApiController::class)->name('verification.verify')->middleware(['throttle:auth', 'audit']);
Route::post('login', action: LoginApiController::class)->middleware(['throttle:auth', 'audit']);
Route::post('/email/verification/resend', ResendEmailVerificationController::class)->name('email.send')->middleware(['throttle:auth', 'audit']);

Route::get('auth-status', AuthStatusController::class);

// Password strength checking routes (public)
Route::group(['prefix' => 'password'], function () {
    Route::post('check-strength', [PasswordStrengthController::class, 'checkStrength']);
    Route::get('requirements', [PasswordStrengthController::class, 'getRequirements']);
});

Route::middleware('auth:sanctum')->group(function () {

    // update profile
    Route::put('profile', [ProfileController::class, 'updateProfile']);
    Route::post('profile-image', [ProfileController::class, 'updateProfileImage']);
    Route::post('change-password', [ProfileController::class, 'changePassword']);
    Route::get('profile', [ProfileController::class, 'getProfile']);

    Route::post('logout', LogoutApiController::class);

    Route::group(['prefix'=> 'student'], function () {
        Route::post('onboarding', OnboardStudentController::class);
    });

    Route::group(['prefix' => 'tutor/onboarding'],  function (){
        Route::post('tutor-courses', [TutorCourseApiController::class, '__invoke']);
        Route::post('description-and-motivation', UpdateTutorDescriptionAndMotivationController::class);
        Route::post('education-and-certification', [StoreEducationController::class, '__invoke']);
        Route::post('tutor-profile-url', TutorProfileAndVideoUrlController::class);
        Route::post('price-availability', PriceAndAvailabilityController::class);
        Route::post('get-all', GetFullProfileController::class);
    });

    Route::post('/payments/initiate', TopupController::class)->middleware(['throttle:payment', 'audit']);

    Route::group(['prefix' => 'wallet'], function () {
        Route::get('', [WalletController::class, 'getBalance']);
        Route::get('transactions', [WalletController::class, 'getTransactions']);
        Route::post('withdraw', [WithdrawalController::class, '__invoke'])->middleware(['throttle:payment', 'audit']);
    });

    Route::group(['prefix' => 'lessons'], function () {
        Route::post('schedule', [ScheduleLessonController::class, '__invoke']);
        Route::post('confirm', [ConfirmLessonController::class, '__invoke']);
        Route::post('regenerate-token', [ScheduleLessonController::class, 'regenerateAgoraToken']);

        // Lesson Tracker Routes
        Route::post('tracker/initialize', [LessonTrackerController::class, 'initializeTracker']);
        Route::post('tracker/start', [LessonTrackerController::class, 'startSession']);
        Route::post('tracker/end', [LessonTrackerController::class, 'endSession']);
        Route::post('tracker/break/start', [LessonTrackerController::class, 'startBreak']);
        Route::post('tracker/break/end', [LessonTrackerController::class, 'endBreak']);
        Route::get('tracker/status/{lessonId}', [LessonTrackerController::class, 'getSessionStatus']);
        Route::get('tracker/statistics/{lessonId}', [LessonTrackerController::class, 'getLessonStatistics']);
        Route::get('tracker/history/{lessonId}', [LessonTrackerController::class, 'getSessionHistory']);
        Route::get('tracker/active', [LessonTrackerController::class, 'getActiveSessions']);
        Route::post('tracker/metadata', [LessonTrackerController::class, 'updateSessionMetadata']);
    });

    // System Settings Routes (Admin only)
    Route::group(['prefix' => 'settings', 'middleware' => 'role:admin'], function () {
        Route::get('', [SystemSettingsController::class, 'index']);
        Route::put('', [SystemSettingsController::class, 'update']);
    });

    // Public Commission Settings Route
    Route::get('settings/commission', [SystemSettingsController::class, 'getCommissionSettings']);

    /**
     * Tutor Lessons Specific routes
     */
    Route::middleware('role:tutor')->group(function () {
        Route::get('/lessons/tutor-lessons', [LessonController::class, 'getTutorLessons']);
    });

    Route::middleware('role:student')->group(function () {
        Route::get('/lessons/student-lessons', [LessonController::class, 'getStudentLessons']);
    });

    Route::group(['prefix'=>'students'], function(){
        Route::get('', GetAllStudentsController::class);
    });

    Route::post('role/switch', [ProfileController::class, 'switchRole']);

    // Notification Settings Routes
    Route::group(['prefix' => 'notification-settings'], function () {
        Route::get('', [NotificationSettingsController::class, 'getSettings']);
        Route::put('', [NotificationSettingsController::class, 'updateSettings']);
    });

    // Two-Factor Authentication Routes
    Route::group(['prefix' => '2fa', 'middleware' => 'audit'], function () {
        Route::get('status', [TwoFactorAuthController::class, 'getStatus']);
        Route::post('setup', [TwoFactorAuthController::class, 'setup']);
        Route::post('confirm', [TwoFactorAuthController::class, 'confirm']);
        Route::post('verify', [TwoFactorAuthController::class, 'verify']);
        Route::post('disable', [TwoFactorAuthController::class, 'disable']);
        Route::get('recovery-codes', [TwoFactorAuthController::class, 'getRecoveryCodes']);
        Route::post('recovery-codes/regenerate', [TwoFactorAuthController::class, 'regenerateRecoveryCodes']);

        // Admin only routes
        Route::get('statistics', [TwoFactorAuthController::class, 'getStatistics'])->middleware('role:admin');
    });

    // Meeting metadata routes
    Route::put('lessons/{lessonId}/meeting-metadata', UpdateMeetingMetaDataController::class);

});

Route::group(['prefix' => 'courses'], function () {
    Route::get('', action: [CourseController::class, 'index']);
});

Route::group(['prefix' => 'languages'], function () {
    Route::get('', [LanguageController::class, 'index']);
    Route::get('search', [LanguageController::class, 'search']);
});

Route::get('/timezones', TimezoneController::class);

Route::prefix('currency')->group(function () {
    Route::get('/all', [CurrencyController::class, 'getAll']);
    Route::post('/convert', [CurrencyController::class, 'convert']);
    Route::post('/rate', [CurrencyController::class, 'exchangeRate']);
});

Route::get('/paypal/success', [PayPalController::class, 'successRedirect'])->name('paypal.success');
Route::get('/paypal/cancel', [PayPalController::class, 'failureRedirect'])->name('paypal.cancel');
// callback route for paypal
Route::post('/paypal/callback', [PayPalController::class, 'callback'])->name('paypal.callback');
// stripe callback route
Route::get('/stripe/callback', [StripeController::class, 'callback'])->name('stripe.callback');

Route::group(['prefix' => 'reviews'], function () {
    Route::get('tutor/{tutorId}', [ReviewController::class, 'getTutorReviews']);
    Route::get('', [ReviewController::class, 'all']);
    Route::get('{id}', [ReviewController::class, 'find']);
    Route::post('', [ReviewController::class, 'createReview'])->middleware('auth:sanctum', 'role:student');
    Route::put('{reviewId}', [ReviewController::class, 'updateReview'])->middleware('auth:sanctum', 'role:student');
    Route::delete('{reviewId}', [ReviewController::class, 'deleteReview'])->middleware('auth:sanctum', 'role:student');
});

Route::group(['prefix' => 'tutor'], function () {
    Route::get('', GetAllTutorsController::class);
    Route::get('{id}', ViewTutorController::class);
    Route::get('{tutorId}/courses', [TutorCourseApiController::class, 'getTutorCourses']);
    Route::get('{tutorId}/stats', [TutorDashboardStatsController::class, '__invoke'])->middleware('auth:sanctum', 'role:tutor|admin');
});

Route::prefix('course-material')->middleware(['auth:sanctum', 'role:tutor|admin'])->group(function () {
    // Course Material CRUD operations
    Route::group(['prefix' => "course"], function(){
        Route::get('', [CourseMaterialController::class, 'index']);
        Route::post('', [CourseMaterialController::class, 'store']);
        Route::get('{id}', [CourseMaterialController::class, 'show']);
        Route::put('{id}', [CourseMaterialController::class, 'update']);
        Route::patch('{id}', [CourseMaterialController::class, 'update']);
        Route::delete('{id}', [CourseMaterialController::class, 'destroy']);
    });

    //Course Material Prerequisite
    Route::get('/prerequisites', [PrerequisiteController::class, 'getPrerequisite']);
    Route::post('/prerequisites', [PrerequisiteController::class, 'createPrerequisite']);
    Route::patch('/prerequisites', [PrerequisiteController::class, 'updatePrerequisite']);
    Route::put('/prerequisites', [PrerequisiteController::class, 'updatePrerequisite']);

    // Course Material Curriculum
    Route::get('/curriculum', [CurriculumController::class, 'getCurriculum']);
    Route::get('/curriculum/all', [CurriculumController::class, 'getAllCurriculum']);
    Route::post('/curriculum', [CurriculumController::class, 'createCurriculum']);
    Route::patch('/curriculum', [CurriculumController::class, 'updateCurriculum']);
    Route::put('/curriculum', [CurriculumController::class, 'updateCurriculum']);

    // Course Material Evaluation
    Route::group(['prefix' => "evaluation"], function(){
        Route::get('', [MaterialEvaluationController::class, 'index']);
        Route::post('', [MaterialEvaluationController::class, 'store']);
        Route::get('{id}', [MaterialEvaluationController::class, 'show']);
        Route::put('{id}', [MaterialEvaluationController::class, 'update']);
        Route::patch('{id}', [MaterialEvaluationController::class, 'update']);
        Route::delete('{id}', [MaterialEvaluationController::class, 'destroy']);
    });

    // Course Material Submission
    Route::group(['prefix' => "submission"], function(){
        Route::get('', [MaterialSubmissionController::class, 'getAllSubmissions']);
        Route::post('', [MaterialSubmissionController::class, 'createSubmission']);
        Route::get('{id}', [MaterialSubmissionController::class, 'getSubmission']);
        Route::put('{id}', [MaterialSubmissionController::class, 'upateSubmission']);
        Route::patch('{id}', [MaterialSubmissionController::class, 'upateSubmission']);
        Route::delete('{id}', [MaterialSubmissionController::class, 'deleteSubmission']);
    });
});

// Course Material Review
Route::prefix('course-material-review')->middleware(['auth:sanctum'])->group(function () {
    Route::get('/reviews', [MaterialReviewsController::class,'index']);
    Route::post('/create', [MaterialReviewsController::class,'create']);
    Route::put('/update', [MaterialReviewsController::class,'update']);
    Route::delete('/delete', [MaterialReviewsController::class,'destroy']);

    //Comments
    Route::post('/create-comment', [CommentsController::class,'create']);
    Route::put('/update-comment', [CommentsController::class,'update']);
    Route::delete('/delete-comment', [CommentsController::class,'destroy']);
});

Route::group(['prefix' => 'agora'], function () {
    Route::post('token', [ScheduleLessonController::class, 'generateNewAgoraTokenByChanel']);

    // Webhook routes (no auth required - called by Agora servers)
    Route::post('webhook/channel-events', [AgoraWebhookController::class, 'handleChannelEvent']);
    Route::post('webhook/recording-events', [AgoraWebhookController::class, 'handleRecordingEvent']);

    // Authenticated routes for session management
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('session/{lessonId}/participants', [AgoraWebhookController::class, 'getSessionParticipants']);
    });
});

Route::prefix('admin')->middleware(['auth:sanctum', 'role:admin'])->group(function () {
    Route::get('users', GetUsersController::class);
    Route::get('users/summary', [GetUserSummaryController::class, '__invoke']);
    Route::get('/users/application-stats', action: [TutorApplicationController::class, '__invoke']);
    Route::get('/users/applications', action: [TutorApplicationController::class, 'getTutorApplications']);

    // Financial Statistics Routes
    Route::get('financial-summary', FinancialSummaryController::class);
    Route::get('daily-transactions', DailyTransactionsController::class);
    Route::get('monthly-profits', MonthlyProfitsController::class);
    Route::get('all-transactions', GetAllTransactionsController::class);
    Route::get('dashboard-stats', [GeneralDashboardController::class, '__invoke']);

    Route::prefix('tutor-profile')->group(function () {
        Route::post('approve', [ManageTutorProfileController::class, 'approveProfile']);
        Route::post('reject', [ManageTutorProfileController::class, 'rejectProfile']);
    });

    Route::prefix('courses')->group(function () {
        Route::post('', [ManageCoursesController::class, 'create']);
        Route::put('{id}', [ManageCoursesController::class, 'update']);
        Route::delete('{id}', [ManageCoursesController::class, 'delete']);
    });

    // Audit Log Routes (Admin only)
    Route::prefix('audit')->middleware('audit')->group(function () {
        Route::get('logs', [AuditLogController::class, 'index']);
        Route::get('logs/{id}', [AuditLogController::class, 'show']);
        Route::get('statistics', [AuditLogController::class, 'statistics']);
        Route::post('logs/{id}/review', [AuditLogController::class, 'markAsReviewed']);
        Route::post('logs/{id}/suspicious', [AuditLogController::class, 'markAsSuspicious']);
        Route::get('pending-review', [AuditLogController::class, 'pendingReview']);
        Route::get('suspicious', [AuditLogController::class, 'suspicious']);
        Route::post('export', [AuditLogController::class, 'export']);
    });
});

// Real-time and Broadcasting routes
Route::group(['prefix' => 'real-time'], function () {
    Route::post('send-notification', [RealTimeController::class, 'sendNotification']);
    Route::post('lesson-reminder', [RealTimeController::class, 'sendLessonReminder']);
    Route::post('payment-notification', [RealTimeController::class, 'sendPaymentNotification']);
    Route::get('notification-count', [RealTimeController::class, 'getNotificationCount']);
    Route::post('mark-notification-read', [RealTimeController::class, 'markNotificationAsRead']);
    Route::get('broadcasting-config', [RealTimeController::class, 'getBroadcastingConfig']);

    // Admin only routes
    Route::middleware('role:admin')->group(function () {
        Route::post('system-maintenance', [RealTimeController::class, 'broadcastSystemMaintenance']);
    });

    // Development/testing routes
    Route::post('test-broadcast', [RealTimeController::class, 'testBroadcast']);
});

// Chat and Messaging routes
Route::middleware(['auth:sanctum', 'throttle:chat'])->group(function () {
    Route::group(['prefix' => 'chat'], function () {
        // Conversation management (API endpoints for frontend)
        Route::get('conversations', [ChatController::class, 'getConversations']);
        Route::post('conversations/start', [ChatController::class, 'startConversation']);
        Route::post('conversations/{conversationId}/mark-read', [ChatController::class, 'markAsRead']);

        // Messaging (API endpoints for sending messages)
        Route::post('messages/send', [ChatController::class, 'sendMessage']);
        Route::post('messages/send-file', [ChatController::class, 'sendMessageWithFile']);
        Route::post('messages/send-lesson-link', [ChatController::class, 'sendLessonLink']);
        Route::get('conversations/{conversationId}/messages', [ChatController::class, 'getMessages']);
        Route::get('conversations/{conversationId}/search', [ChatController::class, 'searchMessages']);
        Route::delete('messages/{messageId}', [ChatController::class, 'deleteMessage']);

        // Utilities and initial data loading
        Route::get('unread-count', [ChatController::class, 'getUnreadCount']);
        Route::get('online-users', [ChatController::class, 'getOnlineUsers']);
    });
});
