<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\User;
use App\Models\Lesson;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

// Enable client events for typing indicators
Broadcast::channel('private-conversation.{conversationId}', function (User $user, int $conversationId) {
    $conversation = \App\Models\Conversation::find($conversationId);

    if (!$conversation) {
        return false;
    }

    // Allow access if user is a participant in the conversation
    return $conversation->isParticipant($user->id);
});

// Client event authorization for typing events
Broadcast::channel('private-conversation.{conversationId}', function (User $user, int $conversationId) {
    $conversation = \App\Models\Conversation::find($conversationId);
    return $conversation && $conversation->isParticipant($user->id);
});

// User-specific message channel
Broadcast::channel('user.{userId}.messages', function (User $user, int $userId) {
    return (int) $user->id === $userId;
});

// Conversation-specific channel
Broadcast::channel('conversation.{conversationId}', function (User $user, int $conversationId) {
    $conversation = \App\Models\Conversation::find($conversationId);

    if (!$conversation) {
        return false;
    }

    // Allow access if user is a participant in the conversation
    return $conversation->isParticipant($user->id);
});

// User-specific status channel
Broadcast::channel('user.{userId}.status', function (User $user, int $userId) {
    return (int) $user->id === $userId;
});

// Presence channel for online users in chat
Broadcast::channel('chat.online-users', function (User $user) {
    return [
        'id' => $user->id,
        'name' => $user->first_name . ' ' . $user->last_name,
        'avatar' => $user->profile_image_url,
        'role' => $user->hasRole('tutor') ? 'tutor' : 'student',
    ];
});

// User-specific notification channel
Broadcast::channel('user.{userId}.notifications', function (User $user, int $userId) {
    return (int) $user->id === $userId;
});

// Lesson-specific channels
Broadcast::channel('lesson.{lessonId}', function (User $user, int $lessonId) {
    $lesson = Lesson::find($lessonId);

    if (!$lesson) {
        return false;
    }

    // Allow access if user is either the student or tutor for this lesson
    return (int) $user->id === (int) $lesson->student_id ||
           (int) $user->id === (int) $lesson->tutor_id;
});

// Student-specific lesson channel
Broadcast::channel('lesson.{lessonId}.student.{studentId}', function (User $user, int $lessonId, int $studentId) {
    $lesson = Lesson::find($lessonId);

    if (!$lesson) {
        return false;
    }

    // Only allow the specific student to access this channel
    return (int) $user->id === $studentId &&
           (int) $lesson->student_id === $studentId;
});

// Tutor-specific lesson channel
Broadcast::channel('lesson.{lessonId}.tutor.{tutorId}', function (User $user, int $lessonId, int $tutorId) {
    $lesson = Lesson::find($lessonId);

    if (!$lesson) {
        return false;
    }

    // Only allow the specific tutor to access this channel
    return (int) $user->id === $tutorId &&
           (int) $lesson->tutor_id === $tutorId;
});

// Global system announcements (admin only)
Broadcast::channel('system.announcements', function (User $user) {
    // Check if user has admin role
    return $user->hasRole('admin') || $user->hasRole('super-admin');
});

// Tutor dashboard updates
Broadcast::channel('tutor.{tutorId}.dashboard', function (User $user, int $tutorId) {
    // Allow access if user is the tutor and has tutor role
    return (int) $user->id === $tutorId &&
           ($user->hasRole('tutor') || $user->hasRole('admin'));
});

// Student dashboard updates
Broadcast::channel('student.{studentId}.dashboard', function (User $user, int $studentId) {
    // Allow access if user is the student and has student role
    return (int) $user->id === $studentId &&
           ($user->hasRole('student') || $user->hasRole('admin'));
});

// Course-specific announcements
Broadcast::channel('course.{courseId}.announcements', function (User $user, int $courseId) {
    // Check if user is enrolled in the course (student) or teaches it (tutor)
    $isStudent = $user->student && $user->student->courses->contains($courseId);
    $isTutor = $user->tutor && $user->tutor->courses->contains($courseId);

    return $isStudent || $isTutor || $user->hasRole('admin');
});

// Admin monitoring channel
Broadcast::channel('admin.monitoring', function (User $user) {
    return $user->hasRole('admin') || $user->hasRole('super-admin');
});

// Security alert channels
Broadcast::channel('user.{userId}.security', function (User $user, int $userId) {
    return (int) $user->id === $userId;
});

Broadcast::channel('admin.security.alerts', function (User $user) {
    return $user->hasRole('admin') || $user->hasRole('super-admin');
});
