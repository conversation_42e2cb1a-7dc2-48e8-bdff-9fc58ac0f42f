# 🚀 Frontend Integration Guide
## Complete API & Features Documentation

This guide provides comprehensive documentation for integrating the frontend with our Laravel API backend. All implemented features are documented with examples, endpoints, and integration patterns.

---

## 📋 Table of Contents

1. [Authentication System](#authentication-system)
2. [Time-Based Lesson Tracker](#time-based-lesson-tracker)
3. [Real-Time Chat System](#real-time-chat-system)
4. [Security Features](#security-features)
5. [Performance & Caching](#performance--caching)
6. [WebSocket Integration](#websocket-integration)
7. [Error Handling](#error-handling)
8. [Rate Limiting](#rate-limiting)
9. [Testing Endpoints](#testing-endpoints)

---

## 🔐 Authentication System

### Base Configuration
```javascript
// API Base URL
const API_BASE = 'https://your-domain.com';

// Default headers for all requests
const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
};
```

### 1. User Registration

**Endpoint:** `POST /register`

**Request:**
```javascript
const registerUser = async (userData) => {
    const response = await fetch(`${API_BASE}/register`, {
        method: 'POST',
        headers: defaultHeaders,
        body: JSON.stringify({
            first_name: "John",
            last_name: "Doe", 
            email: "<EMAIL>",
            country: "US",
            password: "StrongP@ssw0rd2024!",
            password_confirmation: "StrongP@ssw0rd2024!"
        })
    });
    return response.json();
};
```

**Success Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "country": "US",
            "email_verified_at": null,
            "two_factor_enabled": false
        },
        "token": "1|abc123...xyz"
    },
    "message": "User registered successfully"
}
```

### 2. User Login

**Endpoint:** `POST /login`

**Request:**
```javascript
const loginUser = async (credentials) => {
    const response = await fetch(`${API_BASE}/login`, {
        method: 'POST',
        headers: defaultHeaders,
        body: JSON.stringify({
            email: "<EMAIL>",
            password: "StrongP@ssw0rd2024!",
            // Optional: two_factor_code: "123456" if 2FA enabled
        })
    });
    return response.json();
};
```

**Success Response:**
```json
{
    "success": true,
    "data": {
        "token": "1|abc123...xyz",
        "user": {
            "id": 1,
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "two_factor_enabled": false,
            "roles": ["student"]
        }
    },
    "message": "Login successful"
}
```

**2FA Required Response:**
```json
{
    "success": false,
    "data": {
        "requires_2fa": true,
        "user_id": 1
    },
    "message": "Two-factor authentication required"
}
```

### 3. Password Strength Validation

**Endpoint:** `POST /password/check-strength`

**Request:**
```javascript
const checkPasswordStrength = async (password) => {
    const response = await fetch(`${API_BASE}/password/check-strength`, {
        method: 'POST',
        headers: defaultHeaders,
        body: JSON.stringify({ password })
    });
    return response.json();
};
```

**Response:**
```json
{
    "success": true,
    "data": {
        "is_valid": true,
        "requirements": {
            "min_length": true,
            "has_uppercase": true,
            "has_lowercase": true,
            "has_numbers": true,
            "has_symbols": true
        }
    }
}
```

### 4. Two-Factor Authentication

**Setup 2FA:** `POST /2fa/setup`
```javascript
const setup2FA = async () => {
    const response = await fetch(`${API_BASE}/2fa/setup`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

**Response:**
```json
{
    "success": true,
    "data": {
        "qr_code_url": "data:image/png;base64,...",
        "manual_entry_key": "JBSWY3DPEHPK3PXP",
        "recovery_codes": ["12345-67890", "09876-54321"]
    }
}
```

**Confirm 2FA:** `POST /2fa/confirm`
```javascript
const confirm2FA = async (code) => {
    const response = await fetch(`${API_BASE}/2fa/confirm`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ code })
    });
    return response.json();
};
```

---

## ⏱️ Time-Based Lesson Tracker

### 1. Start Lesson Session

**Endpoint:** `POST /lessons/{lessonId}/start`

**Request:**
```javascript
const startLesson = async (lessonId) => {
    const response = await fetch(`${API_BASE}/lessons/${lessonId}/start`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

**Response:**
```json
{
    "success": true,
    "data": {
        "session_id": "uuid-session-id",
        "lesson_id": 123,
        "started_at": "2024-01-15T10:30:00Z",
        "status": "active"
    }
}
```

### 2. Update Lesson Progress

**Endpoint:** `PUT /lessons/{lessonId}/progress`

**Request:**
```javascript
const updateLessonProgress = async (lessonId, progressData) => {
    const response = await fetch(`${API_BASE}/lessons/${lessonId}/progress`, {
        method: 'PUT',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            elapsed_time: 1800, // seconds
            current_position: 1650, // seconds
            watch_percentage: 91.67,
            is_paused: false
        })
    });
    return response.json();
};
```

### 3. Pause/Resume Lesson

**Endpoint:** `POST /lessons/{lessonId}/pause` or `POST /lessons/{lessonId}/resume`

**Request:**
```javascript
const pauseLesson = async (lessonId) => {
    const response = await fetch(`${API_BASE}/lessons/${lessonId}/pause`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

### 4. Complete Lesson

**Endpoint:** `POST /lessons/{lessonId}/complete`

**Request:**
```javascript
const completeLesson = async (lessonId) => {
    const response = await fetch(`${API_BASE}/lessons/${lessonId}/complete`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            final_watch_time: 1800,
            completion_percentage: 100
        })
    });
    return response.json();
};
```

### 5. Get Lesson Analytics

**Endpoint:** `GET /lessons/{lessonId}/analytics`

**Request:**
```javascript
const getLessonAnalytics = async (lessonId) => {
    const response = await fetch(`${API_BASE}/lessons/${lessonId}/analytics`, {
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

**Response:**
```json
{
    "success": true,
    "data": {
        "total_watch_time": 3600,
        "completion_rate": 95.5,
        "average_session_duration": 1200,
        "pause_count": 3,
        "resume_count": 3,
        "sessions": [
            {
                "started_at": "2024-01-15T10:30:00Z",
                "ended_at": "2024-01-15T11:00:00Z",
                "duration": 1800,
                "status": "completed"
            }
        ]
    }
}
```

---

## 💬 Real-Time Chat System

### WebSocket Connection Setup

```javascript
// Using Pusher
import Pusher from 'pusher-js';

const pusher = new Pusher('your-pusher-app-key', {
    cluster: 'your-cluster',
    encrypted: true,
    auth: {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    }
});
```

### 1. Join Conversation

**HTTP Endpoint:** `POST /conversations/{conversationId}/join`

**WebSocket Event:** Emit `join-conversation`
```javascript
// HTTP request to join
const joinConversation = async (conversationId) => {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}/join`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};

// WebSocket subscription
const channel = pusher.subscribe(`private-conversation.${conversationId}`);
```

### 2. Send Message

**HTTP Endpoint:** `POST /conversations/{conversationId}/messages`

**WebSocket Event:** Emit `send-message`
```javascript
// HTTP API (for message persistence)
const sendMessage = async (conversationId, messageData) => {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}/messages`, {
        method: 'POST',
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            content: "Hello, how are you?",
            type: "text" // or "file", "image"
        })
    });
    return response.json();
};

// WebSocket event (for real-time delivery)
channel.trigger('client-send-message', {
    content: "Hello, how are you!",
    type: "text",
    timestamp: new Date().toISOString()
});
```

### 3. Typing Indicators

**WebSocket Events Only:**
```javascript
// Start typing
channel.trigger('client-typing-start', {
    user_id: currentUserId,
    user_name: "John Doe"
});

// Stop typing
channel.trigger('client-typing-stop', {
    user_id: currentUserId
});

// Listen for typing events
channel.bind('client-typing-start', (data) => {
    showTypingIndicator(data.user_name);
});

channel.bind('client-typing-stop', (data) => {
    hideTypingIndicator(data.user_id);
});
```

### 4. Online Status

**WebSocket Events:**
```javascript
// Update status
channel.trigger('client-status-update', {
    status: 'online' // or 'away', 'busy', 'offline'
});

// Listen for status updates
channel.bind('client-status-update', (data) => {
    updateUserStatus(data.user_id, data.status);
});
```

### 5. Message Events Listening

```javascript
// New message received
channel.bind('message-sent', (data) => {
    addMessageToChat(data.message);
    playNotificationSound();
});

// Message delivered
channel.bind('message-delivered', (data) => {
    markMessageAsDelivered(data.message_id);
});

// Message read
channel.bind('message-read', (data) => {
    markMessageAsRead(data.message_id);
});

// User joined conversation
channel.bind('user-joined', (data) => {
    showUserJoinedNotification(data.user);
    updateOnlineUsersList(data.user);
});

// User left conversation  
channel.bind('user-left', (data) => {
    showUserLeftNotification(data.user);
    removeFromOnlineUsersList(data.user_id);
});
```

### 6. Get Conversation History

**Endpoint:** `GET /conversations/{conversationId}/messages`

**Request:**
```javascript
const getConversationHistory = async (conversationId, page = 1) => {
    const response = await fetch(`${API_BASE}/conversations/${conversationId}/messages?page=${page}`, {
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

---

## 🔒 Security Features

### 1. CSRF Protection

For web routes (if using web interface), include CSRF token:

```javascript
// Get CSRF token
const getCSRFToken = async () => {
    const response = await fetch(`${API_BASE}/sanctum/csrf-cookie`, {
        credentials: 'include'
    });
    return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
};
```

### 2. Input Sanitization

All inputs are automatically sanitized by middleware. Some guidelines:

```javascript
// Good practices for frontend
const sanitizeInput = (input) => {
    // Basic client-side sanitization (server-side is primary)
    return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;');
};
```

### 3. Rate Limiting Headers

Monitor rate limiting through response headers:

```javascript
const makeAPIRequest = async (url, options) => {
    const response = await fetch(url, options);
    
    // Check rate limit headers
    const remaining = response.headers.get('X-RateLimit-Remaining');
    const resetTime = response.headers.get('X-RateLimit-Reset');
    
    if (response.status === 429) {
        // Handle rate limit exceeded
        const retryAfter = response.headers.get('Retry-After');
        console.log(`Rate limited. Retry after ${retryAfter} seconds`);
    }
    
    return response.json();
};
```

---

## ⚡ Performance & Caching

### 1. API Response Caching

Some endpoints return cached responses. Check headers:

```javascript
const checkCacheStatus = (response) => {
    const cacheStatus = response.headers.get('X-Cache-Status');
    // Values: 'HIT', 'MISS', 'BYPASS'
    console.log(`Cache status: ${cacheStatus}`);
};
```

### 2. Pagination

Large datasets use pagination:

```javascript
const getPaginatedData = async (endpoint, page = 1, perPage = 15) => {
    const response = await fetch(`${API_BASE}${endpoint}?page=${page}&per_page=${perPage}`, {
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

**Pagination Response:**
```json
{
    "success": true,
    "data": {
        "data": [...],
        "current_page": 1,
        "last_page": 10,
        "per_page": 15,
        "total": 150,
        "from": 1,
        "to": 15,
        "next_page_url": "...",
        "prev_page_url": null
    }
}
```

---

## 🌐 WebSocket Integration

### Complete Setup Example

```javascript
class WebSocketManager {
    constructor(token) {
        this.token = token;
        this.pusher = new Pusher('your-pusher-app-key', {
            cluster: 'your-cluster',
            encrypted: true,
            auth: {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            }
        });
        this.channels = {};
    }

    // Subscribe to conversation
    subscribeToConversation(conversationId) {
        const channelName = `private-conversation.${conversationId}`;
        const channel = this.pusher.subscribe(channelName);
        
        // Bind all events
        channel.bind('message-sent', this.handleNewMessage.bind(this));
        channel.bind('client-typing-start', this.handleTypingStart.bind(this));
        channel.bind('client-typing-stop', this.handleTypingStop.bind(this));
        channel.bind('client-status-update', this.handleStatusUpdate.bind(this));
        channel.bind('user-joined', this.handleUserJoined.bind(this));
        channel.bind('user-left', this.handleUserLeft.bind(this));
        
        this.channels[conversationId] = channel;
        return channel;
    }

    // Send typing indicator
    startTyping(conversationId) {
        const channel = this.channels[conversationId];
        if (channel) {
            channel.trigger('client-typing-start', {
                user_id: this.currentUserId,
                user_name: this.currentUserName
            });
        }
    }

    stopTyping(conversationId) {
        const channel = this.channels[conversationId];
        if (channel) {
            channel.trigger('client-typing-stop', {
                user_id: this.currentUserId
            });
        }
    }

    // Send message via WebSocket
    sendMessage(conversationId, message) {
        const channel = this.channels[conversationId];
        if (channel) {
            channel.trigger('client-send-message', {
                content: message.content,
                type: message.type,
                timestamp: new Date().toISOString()
            });
        }
    }

    // Event handlers
    handleNewMessage(data) {
        console.log('New message:', data);
        // Update UI
    }

    handleTypingStart(data) {
        console.log(`${data.user_name} is typing...`);
        // Show typing indicator
    }

    handleTypingStop(data) {
        console.log(`User ${data.user_id} stopped typing`);
        // Hide typing indicator
    }

    handleStatusUpdate(data) {
        console.log(`User ${data.user_id} is now ${data.status}`);
        // Update user status indicator
    }

    handleUserJoined(data) {
        console.log(`${data.user.name} joined the conversation`);
        // Add to online users list
    }

    handleUserLeft(data) {
        console.log(`${data.user.name} left the conversation`);
        // Remove from online users list
    }
}

// Usage
const wsManager = new WebSocketManager(authToken);
wsManager.subscribeToConversation(123);
```

---

## ❌ Error Handling

### Standard Error Response Format

```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Error message 1", "Error message 2"]
    }
}
```

### Frontend Error Handling

```javascript
const handleAPIResponse = async (response) => {
    const data = await response.json();
    
    if (!response.ok) {
        switch (response.status) {
            case 401:
                // Unauthorized - redirect to login
                localStorage.removeItem('auth_token');
                window.location.href = '/login';
                break;
                
            case 403:
                // Forbidden - show permission error
                showError('You do not have permission to perform this action');
                break;
                
            case 422:
                // Validation errors
                displayValidationErrors(data.errors);
                break;
                
            case 429:
                // Rate limited
                const retryAfter = response.headers.get('Retry-After');
                showError(`Too many requests. Please wait ${retryAfter} seconds.`);
                break;
                
            case 500:
                // Server error
                showError('Server error. Please try again later.');
                break;
                
            default:
                showError(data.message || 'An error occurred');
        }
        throw new Error(data.message);
    }
    
    return data;
};
```

---

## 🔄 Rate Limiting

### Current Rate Limits

- **Authentication endpoints:** 5 requests/minute per IP
- **Payment endpoints:** 3 requests/minute per user
- **Chat endpoints:** 60 requests/minute per user
- **General API:** 60 requests/minute per user

### Frontend Implementation

```javascript
class RateLimitManager {
    constructor() {
        this.limits = {};
    }

    async makeRequest(url, options, rateLimitKey = 'general') {
        const response = await fetch(url, options);
        
        // Update rate limit info
        this.limits[rateLimitKey] = {
            remaining: parseInt(response.headers.get('X-RateLimit-Remaining') || '0'),
            reset: parseInt(response.headers.get('X-RateLimit-Reset') || '0'),
            limit: parseInt(response.headers.get('X-RateLimit-Limit') || '60')
        };

        if (response.status === 429) {
            const retryAfter = parseInt(response.headers.get('Retry-After') || '60');
            await this.delay(retryAfter * 1000);
            return this.makeRequest(url, options, rateLimitKey);
        }

        return response;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getRemainingRequests(key = 'general') {
        return this.limits[key]?.remaining || 0;
    }
}
```

---

## 🧪 Testing Endpoints

### Health Check

**Endpoint:** `GET /up`
```javascript
const checkHealth = async () => {
    const response = await fetch(`${API_BASE}/up`);
    return response.status === 200;
};
```

### Auth Status

**Endpoint:** `GET /auth-status`
```javascript
const checkAuthStatus = async () => {
    const response = await fetch(`${API_BASE}/auth-status`, {
        headers: {
            ...defaultHeaders,
            'Authorization': `Bearer ${token}`
        }
    });
    return response.json();
};
```

---

## 📱 Complete Frontend Integration Example

```javascript
class LearnOsoAPI {
    constructor(baseURL, token = null) {
        this.baseURL = baseURL;
        this.token = token;
        this.wsManager = null;
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        localStorage.setItem('auth_token', token);
        
        // Initialize WebSocket if token is set
        if (token && !this.wsManager) {
            this.wsManager = new WebSocketManager(token);
        }
    }

    // Get headers with auth
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    // Generic request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: this.getHeaders(),
            ...options
        };

        const response = await fetch(url, config);
        return handleAPIResponse(response);
    }

    // Authentication methods
    async register(userData) {
        return this.request('/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
    }

    async login(credentials) {
        const response = await this.request('/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
        
        if (response.success && response.data.token) {
            this.setToken(response.data.token);
        }
        
        return response;
    }

    async logout() {
        const response = await this.request('/logout', { method: 'POST' });
        this.token = null;
        localStorage.removeItem('auth_token');
        return response;
    }

    // Lesson methods
    async startLesson(lessonId) {
        return this.request(`/lessons/${lessonId}/start`, { method: 'POST' });
    }

    async updateLessonProgress(lessonId, progressData) {
        return this.request(`/lessons/${lessonId}/progress`, {
            method: 'PUT',
            body: JSON.stringify(progressData)
        });
    }

    // Chat methods
    async joinConversation(conversationId) {
        const response = await this.request(`/conversations/${conversationId}/join`, {
            method: 'POST'
        });
        
        if (response.success && this.wsManager) {
            this.wsManager.subscribeToConversation(conversationId);
        }
        
        return response;
    }

    async sendMessage(conversationId, messageData) {
        // Send via HTTP for persistence
        const response = await this.request(`/conversations/${conversationId}/messages`, {
            method: 'POST',
            body: JSON.stringify(messageData)
        });

        // Send via WebSocket for real-time delivery
        if (this.wsManager) {
            this.wsManager.sendMessage(conversationId, messageData);
        }

        return response;
    }
}

// Usage Example
const api = new LearnOsoAPI('https://your-api-domain.com');

// Login and start using the API
api.login({ email: '<EMAIL>', password: 'password123' })
   .then(() => {
       console.log('Logged in successfully');
       
       // Start a lesson
       return api.startLesson(123);
   })
   .then(() => {
       console.log('Lesson started');
       
       // Join a chat conversation
       return api.joinConversation(456);
   })
   .then(() => {
       console.log('Joined conversation');
   })
   .catch(error => {
       console.error('Error:', error);
   });
```

---

## 🚨 Important Security Notes

1. **Always use HTTPS** in production
2. **Store tokens securely** (consider using httpOnly cookies for web)
3. **Implement token refresh** mechanism
4. **Validate all inputs** on frontend before sending
5. **Handle rate limiting** gracefully
6. **Use CSP headers** for XSS protection
7. **Implement proper error handling** to avoid information leakage

---

## 📞 Support & Troubleshooting

### Common Issues

1. **CORS errors:** Ensure your domain is in the allowed origins
2. **401 Unauthorized:** Check token validity and format
3. **Rate limiting:** Implement proper backoff strategies
4. **WebSocket connection issues:** Verify Pusher configuration and auth

### Debug Endpoints

- `GET /up` - Health check
- `GET /auth-status` - Check authentication status
- `POST /password/check-strength` - Test password validation

This documentation provides everything needed to integrate the frontend with all implemented backend features. Each section includes practical examples and proper error handling patterns. 
