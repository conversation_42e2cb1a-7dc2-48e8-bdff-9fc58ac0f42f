<?php

namespace Tests\Feature;

use Tests\TestCase;

class SimpleTest extends TestCase
{
    /**
     * Test basic application functionality
     */
    public function test_application_returns_a_successful_response(): void
    {
        $response = $this->get('/');

        $response->assertStatus(302); // Redirects to learnoso.com
    }

    /**
     * Test password strength endpoint works
     */
    public function test_password_strength_endpoint_works(): void
    {
        $response = $this->postJson('/password/check-strength', [
            'password' => 'weak123'
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'is_valid'
            ]
        ]);
    }

    /**
     * Test security headers are present
     */
    public function test_security_headers_are_present(): void
    {
        $response = $this->get('/');

        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
        $response->assertHeader('Content-Security-Policy');
    }

    /**
     * Test auth status endpoint requires authentication
     */
    public function test_auth_status_requires_authentication(): void
    {
        $response = $this->getJson('/auth-status');

        $response->assertStatus(401);
    }
}
