<mxfile host="65bd71144e">
    <diagram id="tsWuBUBL5ifnU_bHPeHE" name="Page-1">
        <mxGraphModel dx="134" dy="109" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="14" value="Person" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
                    <mxGeometry x="220" y="40" width="160" height="114" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="- id: int&#10;- name: string&#10;# email: string" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="14">
                    <mxGeometry y="26" width="160" height="54" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="14">
                    <mxGeometry y="80" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="searchBook()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="14">
                    <mxGeometry y="88" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="33" style="edgeStyle=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;strokeWidth=4;entryX=0.316;entryY=1.004;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=block;endFill=1;" edge="1" parent="1" source="23" target="17">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="250" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="LIberian" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
                    <mxGeometry x="122" y="250" width="160" height="116" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="id: int&#10;name: string&#10;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="23">
                    <mxGeometry y="26" width="160" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="23">
                    <mxGeometry y="82" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="searchBook()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="23">
                    <mxGeometry y="90" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.799;entryY=1.301;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=4;endArrow=block;endFill=1;startArrow=none;startFill=0;" edge="1" parent="1" source="29" target="17">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="Mrmber" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
                    <mxGeometry x="356" y="250" width="160" height="116" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="id: int&#10;name: string&#10;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="29">
                    <mxGeometry y="26" width="160" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="29">
                    <mxGeometry y="82" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="searchBook()" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="29">
                    <mxGeometry y="90" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="Book" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
                    <mxGeometry x="265" y="500" width="160" height="110" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="+ field: type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="35">
                    <mxGeometry y="26" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="35">
                    <mxGeometry y="52" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="35">
                    <mxGeometry y="60" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="41" value="" style="endArrow=open;startArrow=none;endFill=0;startFill=0;endSize=8;html=1;verticalAlign=bottom;labelBackgroundColor=none;strokeWidth=3;entryX=0.75;entryY=0;entryDx=0;entryDy=0;exitX=0.604;exitY=1.278;exitDx=0;exitDy=0;exitPerimeter=0;" edge="1" parent="1" source="32" target="35">
                    <mxGeometry width="160" relative="1" as="geometry">
                        <mxPoint x="260" y="340" as="sourcePoint"/>
                        <mxPoint x="420" y="340" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="47" style="edgeStyle=none;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.768;entryY=1.141;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=4;endArrow=none;endFill=0;startArrow=open;startFill=0;" edge="1" parent="1" source="42" target="38">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="Book" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;" vertex="1" parent="1">
                    <mxGeometry x="260" y="680" width="160" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="+ field: type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="42">
                    <mxGeometry y="26" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="42">
                    <mxGeometry y="52" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;" vertex="1" parent="42">
                    <mxGeometry y="60" width="160" height="26" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
